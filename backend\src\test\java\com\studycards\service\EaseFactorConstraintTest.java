package com.studycards.service;

import com.studycards.config.SpacedRepetitionConfig;
import com.studycards.model.Card;
import com.studycards.model.Deck;
import com.studycards.model.User;
import com.studycards.repository.CardPerformanceHistoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that the ease factor constraint violation fix works correctly
 * and prevents values below 1.3 from being saved to the database.
 */
@ExtendWith(MockitoExtension.class)
class EaseFactorConstraintTest {

    @InjectMocks
    private SpacedRepetitionService spacedRepetitionService;

    @Spy
    private SpacedRepetitionConfig config;

    @Mock
    private CardPerformanceHistoryRepository cardPerformanceHistoryRepository;

    private Card testCard;

    @BeforeEach
    void setUp() {
        // Initialize config with test values
        config.setInitialInterval(1);
        config.setSecondInterval(6);
        config.setMinEaseFactor(1.3f);
        config.setMaxEaseFactor(2.5f);
        config.setDefaultEaseFactor(2.5f);
        config.setMinRandomFactor(0.85f);
        config.setMaxRandomFactor(1.15f);
        config.setEaseFactorIncrement(0.1f);
        config.setEaseFactorDecrement(0.2f);
        config.setPartialRecallReductionFactor(0.5f);
        config.setMaxInterval(365);

        // Create a test deck
        Deck testDeck = new Deck();
        testDeck.setId(1L);
        testDeck.setTitle("Test Deck");

        // Create a test user
        User testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");

        // Create a test card
        testCard = new Card();
        testCard.setId(1L);
        testCard.setQuestion("Test Question");
        testCard.setAnswer("Test Answer");
        testCard.setDeck(testDeck);
        testCard.setCreatedBy(testUser);
        testCard.setDifficultyLevel(1);
        testCard.setReviewCount(0);
        testCard.setCorrectCount(0);
        testCard.setLearningProgress(0.0f);
        testCard.setIntervalDays(1);
        testCard.setEaseFactor(BigDecimal.valueOf(2.5)); // Start with default
    }

    @Test
    void testCalculateNextReview_WithPoorPerformance_ShouldNotGoBelowMinimum() {
        // Arrange - set card to have ease factor close to minimum
        testCard.setEaseFactor(BigDecimal.valueOf(1.4));

        // Act - simulate very poor performance (quality 1) multiple times
        Card updatedCard1 = spacedRepetitionService.calculateNextReview(testCard, 1);
        Card updatedCard2 = spacedRepetitionService.calculateNextReview(updatedCard1, 1);
        Card updatedCard3 = spacedRepetitionService.calculateNextReview(updatedCard2, 1);

        // Assert - ease factor should never go below 1.3
        assertNotNull(updatedCard3.getEaseFactor());
        assertTrue(updatedCard3.getEaseFactor().compareTo(BigDecimal.valueOf(1.3)) >= 0,
                "Ease factor should not be below 1.3, but was: " + updatedCard3.getEaseFactor());
        assertEquals(new BigDecimal("1.3"), updatedCard3.getEaseFactor(),
                "Ease factor should be exactly 1.3 after multiple poor performances");
    }

    @Test
    void testCalculateNextReview_WithFloatingPointPrecision_ShouldHandleCorrectly() {
        // Arrange - set card to have ease factor that might cause precision issues
        testCard.setEaseFactor(BigDecimal.valueOf(1.30000001)); // Slightly above minimum

        // Act - simulate poor performance that would reduce ease factor
        Card updatedCard = spacedRepetitionService.calculateNextReview(testCard, 1);

        // Assert - should handle precision correctly and not go below minimum
        assertNotNull(updatedCard.getEaseFactor());
        assertTrue(updatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(1.3)) >= 0,
                "Ease factor should not be below 1.3 due to precision issues, but was: " + updatedCard.getEaseFactor());
    }

    @Test
    void testCalculateNextReview_WithExtremelyPoorPerformance_ShouldClampToMinimum() {
        // Arrange - start with default ease factor
        testCard.setEaseFactor(BigDecimal.valueOf(2.5));

        // Act - simulate extremely poor performance repeatedly
        Card updatedCard = testCard;
        for (int i = 0; i < 10; i++) {
            updatedCard = spacedRepetitionService.calculateNextReview(updatedCard, 1);
        }

        // Assert - should be clamped to minimum
        assertNotNull(updatedCard.getEaseFactor());
        assertEquals(new BigDecimal("1.3"), updatedCard.getEaseFactor(),
                "After repeated poor performance, ease factor should be clamped to minimum 1.3");
    }

    @Test
    void testCalculateNextReview_WithGoodPerformance_ShouldNotExceedMaximum() {
        // Arrange - start with high ease factor
        testCard.setEaseFactor(BigDecimal.valueOf(2.4));

        // Act - simulate excellent performance repeatedly
        Card updatedCard = testCard;
        for (int i = 0; i < 10; i++) {
            updatedCard = spacedRepetitionService.calculateNextReview(updatedCard, 5);
        }

        // Assert - should be clamped to maximum
        assertNotNull(updatedCard.getEaseFactor());
        assertTrue(updatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(2.5)) <= 0,
                "Ease factor should not exceed maximum 2.5, but was: " + updatedCard.getEaseFactor());
    }

    @Test
    void testCalculateNextReview_WithNullEaseFactor_ShouldSetDefault() {
        // Arrange - set ease factor to null
        testCard.setEaseFactor(null);

        // Act
        Card updatedCard = spacedRepetitionService.calculateNextReview(testCard, 3);

        // Assert - should set to default
        assertNotNull(updatedCard.getEaseFactor());
        assertTrue(updatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(1.3)) >= 0,
                "Ease factor should be at least minimum after null handling");
    }
}
