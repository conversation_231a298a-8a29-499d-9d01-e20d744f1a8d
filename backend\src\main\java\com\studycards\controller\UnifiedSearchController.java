package com.studycards.controller;

import com.studycards.dto.*;
import com.studycards.service.UnifiedSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Unified search controller that consolidates all search functionality
 */
@Slf4j
@RestController
@RequestMapping("/api/unified-search")
public class UnifiedSearchController {

    @Autowired
    private UnifiedSearchService unifiedSearchService;

    /**
     * Unified deck search endpoint
     */
    @PostMapping("/decks")
    public ResponseEntity<Page<DeckResponse>> searchDecks(
            @RequestBody DeckSearchRequest searchRequest,
            HttpServletRequest request) {
        
        // Add request context for analytics
        searchRequest.setClientIp(getClientIp(request));
        searchRequest.setUserAgent(request.getHeader("User-Agent"));
        searchRequest.setSearchContext("unified_search");
        
        log.debug("Unified deck search request: {}", searchRequest);
        
        Page<DeckResponse> results = unifiedSearchService.searchDecks(searchRequest);
        
        log.debug("Unified deck search returned {} results", results.getTotalElements());
        
        return ResponseEntity.ok(results);
    }

    /**
     * Unified card search endpoint
     */
    @PostMapping("/cards")
    public ResponseEntity<Page<CardResponse>> searchCards(
            @RequestBody CardSearchRequest searchRequest,
            HttpServletRequest request) {
        
        log.debug("Unified card search request: {}", searchRequest);
        
        Page<CardResponse> results = unifiedSearchService.searchCards(searchRequest);
        
        log.debug("Unified card search returned {} results", results.getTotalElements());
        
        return ResponseEntity.ok(results);
    }

    /**
     * Quick search endpoint for both decks and cards
     */
    @GetMapping("/quick")
    public ResponseEntity<QuickSearchResponse> quickSearch(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        
        log.debug("Quick search request: query={}, limit={}", query, limit);
        
        // Create quick search requests
        DeckSearchRequest deckRequest = DeckSearchRequest.builder()
            .query(query)
            .isPublic(true)
            .enableRelevanceRanking(true)
            .rankingStrategy("relevance")
            .size(limit / 2)
            .searchContext("quick_search")
            .clientIp(getClientIp(request))
            .userAgent(request.getHeader("User-Agent"))
            .build();
            
        CardSearchRequest cardRequest = CardSearchRequest.builder()
            .query(query)
            .includePublicDecks(true)
            .size(limit / 2)
            .build();
        
        // Execute searches
        Page<DeckResponse> deckResults = unifiedSearchService.searchDecks(deckRequest);
        Page<CardResponse> cardResults = unifiedSearchService.searchCards(cardRequest);
        
        // Build response
        QuickSearchResponse response = QuickSearchResponse.builder()
            .query(query)
            .deckResults(deckResults.getContent())
            .cardResults(cardResults.getContent())
            .totalDeckResults(deckResults.getTotalElements())
            .totalCardResults(cardResults.getTotalElements())
            .build();
        
        log.debug("Quick search returned {} decks and {} cards", 
            deckResults.getTotalElements(), cardResults.getTotalElements());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Search suggestions endpoint
     */
    @GetMapping("/suggestions")
    public ResponseEntity<SearchSuggestionResponse> getSearchSuggestions(
            @RequestParam String query,
            @RequestParam(defaultValue = "all") String type,
            @RequestParam(defaultValue = "10") int limit) {
        
        log.debug("Search suggestions request: query={}, type={}, limit={}", query, type, limit);
        
        // This would integrate with the existing SearchService suggestions
        // For now, return empty response
        SearchSuggestionResponse response = SearchSuggestionResponse.builder()
            .recentSearches(java.util.Collections.emptyList())
            .popularSearches(java.util.Collections.emptyList())
            .tagSuggestions(java.util.Collections.emptyList())
            .contentSuggestions(java.util.Collections.emptyList())
            .build();
        
        return ResponseEntity.ok(response);
    }

    /**
     * Extract client IP address
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
