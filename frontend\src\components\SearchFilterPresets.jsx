// src/components/SearchFilterPresets.jsx
import React, { useState } from 'react';
import {
  BookmarkIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkIconSolid } from '@heroicons/react/24/solid';
import { motion, AnimatePresence } from 'framer-motion';
import Button from './Button';

const SearchFilterPresets = ({ 
  presets, 
  currentFilters, 
  searchType, 
  onSavePreset, 
  onLoadPreset, 
  onDeletePreset 
}) => {
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [editingPreset, setEditingPreset] = useState(null);
  const [editName, setEditName] = useState('');

  const hasActiveFilters = () => {
    return Object.values(currentFilters).some(value => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'boolean') return value === true;
      return value !== null && value !== undefined && value !== '';
    });
  };

  const handleSavePreset = () => {
    if (!presetName.trim()) return;
    
    onSavePreset({
      name: presetName.trim(),
      searchType,
      filters: currentFilters
    });
    
    setPresetName('');
    setShowSaveDialog(false);
  };

  const handleEditPreset = (preset) => {
    setEditingPreset(preset.id);
    setEditName(preset.name);
  };

  const handleSaveEdit = (presetId) => {
    if (!editName.trim()) return;
    
    // Update preset name (this would need to be implemented in the context)
    // For now, we'll just close the edit mode
    setEditingPreset(null);
    setEditName('');
  };

  const handleCancelEdit = () => {
    setEditingPreset(null);
    setEditName('');
  };

  const getPresetDescription = (preset) => {
    const filters = preset.filters;
    const descriptions = [];
    
    if (preset.searchType === 'decks') {
      if (filters.isPublic !== null) {
        descriptions.push(filters.isPublic ? 'Public' : 'Private');
      }
      if (filters.tagNames?.length > 0) {
        descriptions.push(`${filters.tagNames.length} tag${filters.tagNames.length > 1 ? 's' : ''}`);
      }
      if (filters.favoritesOnly) {
        descriptions.push('Favorites');
      }
      if (filters.minDifficulty || filters.maxDifficulty) {
        descriptions.push('Difficulty filter');
      }
      if (filters.isCollaborative !== null) {
        descriptions.push(filters.isCollaborative ? 'Collaborative' : 'Non-collaborative');
      }
    } else if (preset.searchType === 'cards') {
      if (filters.minDifficulty || filters.maxDifficulty) {
        descriptions.push('Difficulty filter');
      }
      if (filters.minProgress || filters.maxProgress) {
        descriptions.push('Progress filter');
      }
      if (filters.includeDueCards) {
        descriptions.push('Due cards');
      }
      if (filters.tagNames?.length > 0) {
        descriptions.push(`${filters.tagNames.length} tag${filters.tagNames.length > 1 ? 's' : ''}`);
      }
    }
    
    return descriptions.length > 0 ? descriptions.join(', ') : 'No filters';
  };

  const filteredPresets = presets.filter(preset => preset.searchType === searchType);

  return (
    <div className="space-y-4">
      {/* Save Current Filters */}
      {hasActiveFilters() && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Save Current Filters
              </h4>
              <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                Save your current filter combination as a preset for quick access
              </p>
            </div>
            <Button
              size="sm"
              onClick={() => setShowSaveDialog(true)}
              className="flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Save Preset</span>
            </Button>
          </div>
        </div>
      )}

      {/* Save Dialog */}
      <AnimatePresence>
        {showSaveDialog && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg"
          >
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              Save Filter Preset
            </h4>
            <div className="flex space-x-2">
              <input
                type="text"
                value={presetName}
                onChange={(e) => setPresetName(e.target.value)}
                placeholder="Enter preset name..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                onKeyPress={(e) => e.key === 'Enter' && handleSavePreset()}
                autoFocus
              />
              <Button
                size="sm"
                onClick={handleSavePreset}
                disabled={!presetName.trim()}
              >
                <CheckIcon className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setShowSaveDialog(false);
                  setPresetName('');
                }}
              >
                <XMarkIcon className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Existing Presets */}
      {filteredPresets.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <BookmarkIconSolid className="h-4 w-4 mr-2 text-primary-500" />
            Saved Presets ({searchType})
          </h4>
          
          <div className="space-y-2">
            {filteredPresets.map((preset) => (
              <motion.div
                key={preset.id}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {editingPreset === preset.id ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          className="flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                          onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit(preset.id)}
                          autoFocus
                        />
                        <button
                          onClick={() => handleSaveEdit(preset.id)}
                          className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        >
                          <CheckIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <div>
                        <button
                          onClick={() => onLoadPreset(preset.id)}
                          className="text-left w-full group"
                        >
                          <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            {preset.name}
                          </h5>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {getPresetDescription(preset)}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            Created {new Date(preset.createdAt).toLocaleDateString()}
                          </p>
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {editingPreset !== preset.id && (
                    <div className="flex items-center space-x-1 ml-3">
                      <button
                        onClick={() => handleEditPreset(preset)}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                        title="Edit preset name"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm(`Delete preset "${preset.name}"?`)) {
                            onDeletePreset(preset.id);
                          }
                        }}
                        className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded"
                        title="Delete preset"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredPresets.length === 0 && !hasActiveFilters() && (
        <div className="text-center py-6 text-gray-500 dark:text-gray-400">
          <BookmarkIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No saved presets for {searchType}</p>
          <p className="text-xs mt-1">Apply some filters and save them as a preset</p>
        </div>
      )}
    </div>
  );
};

export default SearchFilterPresets;
