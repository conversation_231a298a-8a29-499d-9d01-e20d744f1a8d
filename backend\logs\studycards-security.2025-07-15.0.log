2025-07-15 00:39:37.422 [http-nio-8082-exec-1] ERROR c.s.security.AuthEntryPointJwt - 
                [] [] [] [] Unauthorized error: Full authentication is required to access this resource
2025-07-15 00:40:54.348 [http-nio-8082-exec-5] INFO  c.s.security.CustomOAuth2UserService - 
                [] [] [] [] Google OAuth2 user attributes validated successfully for email: <EMAIL>
2025-07-15 00:40:54.352 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success handler called
2025-07-15 00:40:54.352 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Request URI: /login/oauth2/code/google
2025-07-15 00:40:54.352 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Query string: state=va6zS8nUwZx1y1tmx5HEKsmUGtaFR49DNopDrS7kbxo%3D&code=4%2F0AVMBsJiiPOAD7_2xH6BYj2a6JwENAmdS1Cu5SQZlqEmK-IhQbVp7NahMUxOZT_KBXEa5FQ&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid&authuser=1&prompt=none
2025-07-15 00:40:54.352 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success from Code
2025-07-15 00:40:54.413 [http-nio-8082-exec-5] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT token generated successfully for email: <EMAIL> (ID: 5aef6373-e56e-4f56-8afe-8e16d04fa8ce)
2025-07-15 00:40:54.554 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 cookie configuration - requireHttps: false, isSecure: false, useSecureCookies: false
2025-07-15 00:40:54.556 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 access token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-15 00:40:54.556 [http-nio-8082-exec-5] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 refresh token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-15 19:18:23.990 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-15 19:18:23.997 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-15 19:18:23.997 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-15 19:18:23.997 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-15 19:18:23.997 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-15 23:12:16.599 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-15 23:12:16.608 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-15 23:12:16.608 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-15 23:12:16.608 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-15 23:12:16.608 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
