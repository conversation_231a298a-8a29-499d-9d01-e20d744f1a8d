package com.studycards.performance;

import com.studycards.dto.DeckSearchRequest;
import com.studycards.dto.CardSearchRequest;
import com.studycards.service.UnifiedSearchService;
import com.studycards.service.SearchPerformanceService;
import com.studycards.model.User;
import com.studycards.model.Deck;
import com.studycards.model.Card;
import com.studycards.repository.DeckRepository;
import com.studycards.repository.CardRepository;
import com.studycards.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Performance tests for search functionality
 * These tests validate that the search optimizations are working correctly
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Disabled("Performance tests - enable manually for performance validation")
public class SearchPerformanceTest {

    @Autowired
    private UnifiedSearchService unifiedSearchService;

    @Autowired
    private SearchPerformanceService searchPerformanceService;

    @Autowired
    private DeckRepository deckRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private UserRepository userRepository;

    private User testUser;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = new User();
        testUser.setUsername("perftest");
        testUser.setEmail("<EMAIL>");
        testUser = userRepository.save(testUser);

        // Create test data for performance testing
        createTestData();
    }

    private void createTestData() {
        // Create multiple decks with various properties for comprehensive testing
        for (int i = 0; i < 100; i++) {
            Deck deck = new Deck();
            deck.setTitle("Performance Test Deck " + i);
            deck.setDescription("Description for performance testing deck " + i);
            deck.setCreator(testUser);
            deck.setIsPublic(i % 2 == 0); // Mix of public and private
            deck.setAverageDifficulty((i % 5) + 1); // Difficulty 1-5
            deck.setCardCount(10 + (i % 20)); // 10-29 cards
            
            deck = deckRepository.save(deck);

            // Create cards for each deck
            for (int j = 0; j < (10 + (i % 20)); j++) {
                Card card = new Card();
                card.setQuestion("Question " + j + " for deck " + i);
                card.setAnswer("Answer " + j + " for deck " + i);
                card.setDeck(deck);
                card.setDifficultyLevel((j % 5) + 1);
                card.setLearningProgress(0.1f * (j % 10));
                
                cardRepository.save(card);
            }
        }
    }

    @Test
    void testBasicSearchPerformance() {
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("Performance")
            .page(0)
            .size(20)
            .build();

        long startTime = System.currentTimeMillis();
        
        Page<?> results = unifiedSearchService.searchDecks(request);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Assert performance criteria
        assertTrue(executionTime < 1000, "Basic search should complete within 1 second, took: " + executionTime + "ms");
        assertNotNull(results);
        assertTrue(results.getTotalElements() > 0, "Should find test data");
        
        // Log performance metrics
        searchPerformanceService.monitorQueryPerformance("deck", "Performance", executionTime, (int) results.getTotalElements());
    }

    @Test
    void testComplexFilteredSearchPerformance() {
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("Test")
            .isPublic(true)
            .tagNames(Arrays.asList("performance", "test"))
            .minDifficulty(2)
            .maxDifficulty(4)
            .minCardCount(15)
            .maxCardCount(25)
            .enableRelevanceRanking(true)
            .page(0)
            .size(20)
            .build();

        long startTime = System.currentTimeMillis();
        
        Page<?> results = unifiedSearchService.searchDecks(request);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // Complex filtered searches should still be fast
        assertTrue(executionTime < 2000, "Complex filtered search should complete within 2 seconds, took: " + executionTime + "ms");
        assertNotNull(results);
        
        searchPerformanceService.monitorQueryPerformance("deck_filtered", "Test", executionTime, (int) results.getTotalElements());
    }

    @Test
    void testCardSearchPerformance() {
        CardSearchRequest request = CardSearchRequest.builder()
            .query("Question")
            .minDifficulty(1)
            .maxDifficulty(5)
            .includePublicDecks(true)
            .page(0)
            .size(50)
            .build();

        long startTime = System.currentTimeMillis();
        
        Page<?> results = unifiedSearchService.searchCards(request);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        assertTrue(executionTime < 1500, "Card search should complete within 1.5 seconds, took: " + executionTime + "ms");
        assertNotNull(results);
        assertTrue(results.getTotalElements() > 0, "Should find test cards");
        
        searchPerformanceService.monitorQueryPerformance("card", "Question", executionTime, (int) results.getTotalElements());
    }

    @Test
    void testConcurrentSearchPerformance() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        List<CompletableFuture<Long>> futures = IntStream.range(0, 20)
            .mapToObj(i -> CompletableFuture.supplyAsync(() -> {
                DeckSearchRequest request = DeckSearchRequest.builder()
                    .query("Performance " + i)
                    .page(0)
                    .size(10)
                    .build();

                long startTime = System.currentTimeMillis();
                unifiedSearchService.searchDecks(request);
                return System.currentTimeMillis() - startTime;
            }, executor))
            .toList();

        // Wait for all searches to complete
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.get();

        // Check that all concurrent searches completed reasonably quickly
        List<Long> executionTimes = futures.stream()
            .map(CompletableFuture::join)
            .toList();

        long maxTime = executionTimes.stream().mapToLong(Long::longValue).max().orElse(0);
        long avgTime = (long) executionTimes.stream().mapToLong(Long::longValue).average().orElse(0);

        assertTrue(maxTime < 3000, "Max concurrent search time should be under 3 seconds, was: " + maxTime + "ms");
        assertTrue(avgTime < 1500, "Average concurrent search time should be under 1.5 seconds, was: " + avgTime + "ms");

        executor.shutdown();
    }

    @Test
    void testLargeResultSetPerformance() {
        // Search that should return many results
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("Deck") // Should match most test decks
            .page(0)
            .size(50)
            .build();

        long startTime = System.currentTimeMillis();
        
        Page<?> results = unifiedSearchService.searchDecks(request);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        assertTrue(executionTime < 2000, "Large result set search should complete within 2 seconds, took: " + executionTime + "ms");
        assertNotNull(results);
        assertTrue(results.getTotalElements() >= 50, "Should find many test decks");
        
        searchPerformanceService.monitorQueryPerformance("deck_large", "Deck", executionTime, (int) results.getTotalElements());
    }

    @Test
    void testPaginationPerformance() {
        DeckSearchRequest request = DeckSearchRequest.builder()
            .query("Performance")
            .page(0)
            .size(10)
            .build();

        // Test multiple pages
        for (int page = 0; page < 5; page++) {
            request.setPage(page);
            
            long startTime = System.currentTimeMillis();
            Page<?> results = unifiedSearchService.searchDecks(request);
            long executionTime = System.currentTimeMillis() - startTime;

            assertTrue(executionTime < 1000, "Pagination page " + page + " should be fast, took: " + executionTime + "ms");
            assertNotNull(results);
        }
    }

    @Test
    void testSearchAnalyticsPerformance() {
        LocalDateTime startDate = LocalDateTime.now().minusDays(1);
        LocalDateTime endDate = LocalDateTime.now();

        long startTime = System.currentTimeMillis();
        
        SearchPerformanceService.SearchPerformanceMetrics metrics = 
            searchPerformanceService.getComprehensivePerformanceMetrics(startDate, endDate);
        
        long executionTime = System.currentTimeMillis() - startTime;

        assertTrue(executionTime < 2000, "Analytics query should complete within 2 seconds, took: " + executionTime + "ms");
        assertNotNull(metrics);
    }

    @Test
    void testMemoryUsageUnderLoad() {
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        // Perform many searches
        for (int i = 0; i < 100; i++) {
            DeckSearchRequest request = DeckSearchRequest.builder()
                .query("Memory test " + i)
                .page(0)
                .size(20)
                .build();

            unifiedSearchService.searchDecks(request);
        }

        // Force garbage collection
        System.gc();
        Thread.yield();

        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;

        // Memory increase should be reasonable (less than 50MB)
        assertTrue(memoryIncrease < 50 * 1024 * 1024, 
            "Memory usage should not increase significantly, increased by: " + (memoryIncrease / 1024 / 1024) + "MB");
    }

    @Test
    void testDatabaseConnectionPoolUnderLoad() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(20);
        
        List<CompletableFuture<Void>> futures = IntStream.range(0, 50)
            .mapToObj(i -> CompletableFuture.runAsync(() -> {
                DeckSearchRequest request = DeckSearchRequest.builder()
                    .query("Connection test " + i)
                    .page(0)
                    .size(10)
                    .build();

                // Should not throw connection pool exhaustion errors
                assertDoesNotThrow(() -> unifiedSearchService.searchDecks(request));
            }, executor))
            .toList();

        // Wait for all to complete
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        
        // Should complete without timeout (indicating no connection pool issues)
        assertDoesNotThrow(() -> allOf.get());

        executor.shutdown();
    }
}
