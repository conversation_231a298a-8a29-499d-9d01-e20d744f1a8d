package com.studycards.repository;

import com.studycards.enums.SessionType;
import com.studycards.enums.SubscriptionStatus;
import com.studycards.model.Deck;
import com.studycards.model.StudySession;
import com.studycards.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.Optional;

@Repository
public interface StudySessionRepository extends JpaRepository<StudySession, Long> {
    // Basic queries
    List<StudySession> findByUser(User user);
    Page<StudySession> findByUser(User user, Pageable pageable);

    /**
     * Find study sessions by user with JOIN FETCH to prevent LazyInitializationException
     * PERFORMANCE FIX: Added to prevent lazy loading issues when accessing deck properties
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.deck d " +
           "WHERE s.user = :user " +
           "AND d.deleted = false " +
           "ORDER BY s.startTime DESC")
    List<StudySession> findByUserWithDeckFetch(@Param("user") User user);

    List<StudySession> findByUserAndDeck(User user, Deck deck);
    Page<StudySession> findByUserAndDeck(User user, Deck deck, Pageable pageable);

    /**
     * Find study session by ID with JOIN FETCH to prevent LazyInitializationException
     * PERFORMANCE FIX: Added to prevent lazy loading issues when accessing deck properties
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.deck d " +
           "WHERE s.id = :sessionId " +
           "AND d.deleted = false")
    Optional<StudySession> findByIdWithDeckFetch(@Param("sessionId") Long sessionId);

    /**
     * Find study sessions by user and deck with JOIN FETCH to prevent N+1 queries
     * PERFORMANCE FIX: Added to prevent N+1 query problems
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.user u JOIN FETCH s.deck d " +
           "WHERE s.user = :user AND s.deck = :deck " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE)")
    List<StudySession> findByUserAndDeckWithFetch(@Param("user") User user, @Param("deck") Deck deck);

    List<StudySession> findByUserAndStartTimeBetween(User user, LocalDateTime start, LocalDateTime end);
    Page<StudySession> findByUserAndStartTimeBetween(User user, LocalDateTime start, LocalDateTime end, Pageable pageable);

    List<StudySession> findByUserAndStartTimeBefore(User user, LocalDateTime end);

    List<StudySession> findByUserAndDeckAndStartTimeBetween(User user, Deck deck, LocalDateTime start, LocalDateTime end);

    boolean existsByUserAndDeckAndStartTimeBetween(User user, Deck deck, LocalDateTime start, LocalDateTime end);

    /**
     * PERFORMANCE FIX: Optimized current streak calculation using SQL Server compatible syntax
     * This replaces the inefficient Java-based streak calculation
     */
    @Query(value = """
        WITH streak_calc AS (
            SELECT
                CAST(start_time AS DATE) as study_date,
                ROW_NUMBER() OVER (ORDER BY CAST(start_time AS DATE) DESC) as rn
            FROM study_sessions
            WHERE user_id = :userId
            AND CAST(start_time AS DATE) <= CAST(:currentDate AS DATE)
            GROUP BY CAST(start_time AS DATE)
        ),
        consecutive_days AS (
            SELECT
                study_date,
                rn,
                DATEDIFF(DAY, study_date, CAST(:currentDate AS DATE)) as days_ago,
                CASE
                    WHEN rn = 1 AND DATEDIFF(DAY, study_date, CAST(:currentDate AS DATE)) <= 1 THEN 1
                    WHEN rn > 1 AND DATEDIFF(DAY, study_date, CAST(:currentDate AS DATE)) = rn - 1 THEN 1
                    ELSE 0
                END as is_consecutive
            FROM streak_calc
        )
        SELECT COUNT(*)
        FROM consecutive_days
        WHERE is_consecutive = 1
        AND rn <= ISNULL((
            SELECT MIN(rn)
            FROM consecutive_days
            WHERE is_consecutive = 0
            AND rn > 1
        ), 999999)
        """, nativeQuery = true)
    int calculateCurrentStreakOptimized(@Param("userId") Long userId, @Param("currentDate") String currentDate);

    /**
     * PERFORMANCE FIX: Optimized longest streak calculation using SQL Server compatible syntax
     */
    @Query(value = """
        WITH study_dates AS (
            SELECT DISTINCT CAST(start_time AS DATE) as study_date
            FROM study_sessions
            WHERE user_id = :userId
        ),
        streak_groups AS (
            SELECT
                study_date,
                ROW_NUMBER() OVER (ORDER BY study_date) as rn,
                DATEADD(DAY, -ROW_NUMBER() OVER (ORDER BY study_date), study_date) as streak_group
            FROM study_dates
        ),
        streak_lengths AS (
            SELECT
                streak_group,
                COUNT(*) as streak_length
            FROM streak_groups
            GROUP BY streak_group
        )
        SELECT COALESCE(MAX(streak_length), 0)
        FROM streak_lengths
        """, nativeQuery = true)
    int calculateLongestStreakOptimized(@Param("userId") Long userId);

    /**
     * PERFORMANCE FIX: Optimized query for study calendar data
     */
    @Query("""
        SELECT
            DATE(s.startTime) as studyDate,
            COUNT(s.id) as sessionCount,
            SUM(s.cardsStudied) as totalCards,
            SUM(s.correctAnswers) as totalCorrect
        FROM StudySession s
        WHERE s.user.id = :userId
        AND s.startTime BETWEEN :startDate AND :endDate
        GROUP BY DATE(s.startTime)
        ORDER BY studyDate
        """)
    List<Object[]> findStudyCalendarData(@Param("userId") Long userId,
                                        @Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * PERFORMANCE FIX: Optimized query for study patterns by hour
     */
    @Query(value = """
        SELECT
            DATEPART(HOUR, s.start_time) as hourOfDay,
            COUNT(s.id) as sessionCount,
            AVG(CASE WHEN s.cards_studied > 0 THEN (s.correct_answers * 100.0 / s.cards_studied) ELSE 0 END) as avgAccuracy
        FROM study_sessions s
        WHERE s.user_id = :userId
        AND s.start_time >= :startDate
        GROUP BY DATEPART(HOUR, s.start_time)
        ORDER BY hourOfDay
        """, nativeQuery = true)
    List<Object[]> findStudyPatternsByHour(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    /**
     * PERFORMANCE FIX: Optimized query for study patterns by day of week
     */
    @Query(value = """
        SELECT
            DATEPART(WEEKDAY, s.start_time) as dayOfWeek,
            COUNT(s.id) as sessionCount,
            AVG(CASE WHEN s.cards_studied > 0 THEN (s.correct_answers * 100.0 / s.cards_studied) ELSE 0 END) as avgAccuracy
        FROM study_sessions s
        WHERE s.user_id = :userId
        AND s.start_time >= :startDate
        GROUP BY DATEPART(WEEKDAY, s.start_time)
        ORDER BY dayOfWeek
        """, nativeQuery = true)
    List<Object[]> findStudyPatternsByDayOfWeek(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    @Query("SELECT DISTINCT s.user FROM StudySession s WHERE s.deck = :deck " +
           "AND s.deck.deleted = false " +
           "AND s.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (s.deck.creator.subscriptionEndDate IS NULL OR s.deck.creator.subscriptionEndDate >= CURRENT_DATE)")
    List<User> findUsersByDeck(@Param("deck") Deck deck);

    @Query("SELECT MAX(s.startTime) FROM StudySession s WHERE s.deck = :deck " +
           "AND s.deck.deleted = false " +
           "AND s.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (s.deck.creator.subscriptionEndDate IS NULL OR s.deck.creator.subscriptionEndDate >= CURRENT_DATE)")
    LocalDateTime getLastStudyDateByDeck(@Param("deck") Deck deck);

    @Query("SELECT COUNT(s) FROM StudySession s WHERE s.deck.id = :deckId")
    long countByDeckId(@Param("deckId") Long deckId);

    // Advanced filtering
    @Query("SELECT s FROM StudySession s JOIN FETCH s.deck d WHERE s.user = :user " +
           "AND (:deckId IS NULL OR s.deck.id = :deckId) " +
           "AND (:sessionType IS NULL OR s.sessionType = :sessionType) " +
           "AND (CAST(:startDate AS timestamp) IS NULL OR s.startTime >= :startDate) " +
           "AND (CAST(:endDate AS timestamp) IS NULL OR s.startTime <= :endDate) " +
           "AND d.deleted = false")
    Page<StudySession> findByUserWithFilters(
            @Param("user") User user,
            @Param("deckId") Long deckId,
            @Param("sessionType") String sessionType,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    // Multiple deck filtering
    @Query("SELECT s FROM StudySession s JOIN FETCH s.deck d WHERE s.user = :user " +
           "AND (:deckIds IS NULL OR s.deck.id IN :deckIds) " +
           "AND (:sessionType IS NULL OR s.sessionType = :sessionType) " +
           "AND (CAST(:startDate AS timestamp) IS NULL OR s.startTime >= :startDate) " +
           "AND (CAST(:endDate AS timestamp) IS NULL OR s.startTime <= :endDate) " +
           "AND d.deleted = false")
    Page<StudySession> findByUserAndDeckIdsWithFilters(
            @Param("user") User user,
            @Param("deckIds") List<Long> deckIds,
            @Param("sessionType") String sessionType,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    // Aggregation queries
    @Query("SELECT SUM(s.cardsStudied) FROM StudySession s WHERE s.user = :user")
    Long countTotalCardsStudiedByUser(@Param("user") User user);

    @Query("SELECT AVG(s.correctAnswers * 100.0 / s.cardsStudied) FROM StudySession s " +
            "WHERE s.user = :user AND s.cardsStudied > 0")
    Double getAverageAccuracyByUser(@Param("user") User user);

    @Query(value = "SELECT AVG(DATEDIFF(MINUTE, s.start_time, s.end_time)) FROM study_sessions s WHERE s.user_id = :userId AND s.end_time IS NOT NULL", nativeQuery = true)
    Double getAverageDurationByUser(@Param("userId") Long userId);

    // Time-based analytics
    @Query(value = "SELECT DATEPART(WEEKDAY, s.start_time) as dayOfWeek, COUNT(s.id) as sessionCount " +
           "FROM study_sessions s WHERE s.user_id = :userId " +
           "GROUP BY DATEPART(WEEKDAY, s.start_time)", nativeQuery = true)
    List<Object[]> getSessionCountByDayOfWeek(@Param("userId") Long userId);

    @Query(value = "SELECT DATEPART(HOUR, s.start_time) as hourOfDay, COUNT(s.id) as sessionCount " +
           "FROM study_sessions s WHERE s.user_id = :userId " +
           "GROUP BY DATEPART(HOUR, s.start_time)", nativeQuery = true)
    List<Object[]> getSessionCountByHourOfDay(@Param("userId") Long userId);

    @Query(value = "SELECT DATEPART(WEEKDAY, s.start_time) as dayOfWeek, " +
           "AVG(s.correct_answers * 100.0 / s.cards_studied) as accuracy " +
           "FROM study_sessions s WHERE s.user_id = :userId AND s.cards_studied > 0 " +
           "GROUP BY DATEPART(WEEKDAY, s.start_time)", nativeQuery = true)
    List<Object[]> getAccuracyByDayOfWeek(@Param("userId") Long userId);

    @Query(value = "SELECT DATEPART(HOUR, s.start_time) as hourOfDay, " +
           "AVG(s.correct_answers * 100.0 / s.cards_studied) as accuracy " +
           "FROM study_sessions s WHERE s.user_id = :userId AND s.cards_studied > 0 " +
           "GROUP BY DATEPART(HOUR, s.start_time)", nativeQuery = true)
    List<Object[]> getAccuracyByHourOfDay(@Param("userId") Long userId);

    // Streak calculation - SQL Server compatible
    @Query("SELECT DISTINCT CAST(s.startTime AS DATE) " +
           "FROM StudySession s WHERE s.user = :user " +
           "ORDER BY CAST(s.startTime AS DATE) DESC")
    List<LocalDate> getDistinctStudyDatesByUser(@Param("user") User user);

    List<StudySession> findTop10ByUserOrderByCreatedAtDesc(User user);

    @Query("SELECT s FROM StudySession s WHERE s.user = ?1 AND s.correctAnswers = s.cardsStudied AND s.cardsStudied > ?2")
    Optional<StudySession> findByUserAndCorrectAnswersEqualsCardsStudiedAndCardsStudiedGreaterThan(User user, int minCards);

    // Comparison with previous sessions
    @Query("SELECT s FROM StudySession s WHERE s.user = :user AND s.deck = :deck " +
           "AND s.id <> :currentSessionId ORDER BY s.startTime DESC")
    List<StudySession> findPreviousSessionsByUserAndDeck(
            @Param("user") User user,
            @Param("deck") Deck deck,
            @Param("currentSessionId") Long currentSessionId,
            Pageable pageable);

    /**
     * Get study statistics for a specific deck and user
     *
     * @param user The user
     * @param deck The deck
     * @return Map containing study statistics
     */
    @Query("SELECT new map(" +
           "COALESCE(COUNT(s), 0) as sessionCount, " +
           "COALESCE(SUM(s.cardsStudied), 0) as totalCardsStudied, " +
           "COALESCE(SUM(s.correctAnswers), 0) as totalCorrectAnswers, " +
           "COALESCE(AVG(s.correctAnswers * 100.0 / s.cardsStudied), 0.0) as averageAccuracy, " +
           "MAX(s.startTime) as lastStudyTime) " +
           "FROM StudySession s " +
           "WHERE s.user = :user AND s.deck = :deck AND s.cardsStudied > 0")
    Map<String, Object> getDeckStudyStatistics(@Param("user") User user, @Param("deck") Deck deck);

    /**
     * Count the number of users who have studied this deck
     *
     * @param deck The deck
     * @return Number of users who have studied this deck
     */
    @Query("SELECT COUNT(DISTINCT s.user) FROM StudySession s WHERE s.deck = :deck")
    Long countUsersWhoStudiedDeck(@Param("deck") Deck deck);

    /**
     * Get the total number of cards studied across all sessions
     * Excludes sessions from users with EXPIRED or CANCELLED subscription status
     *
     * @return Total cards studied
     */
    @Query("SELECT COALESCE(SUM(s.cardsStudied), 0) FROM StudySession s " +
           "WHERE s.user.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    long getTotalCardsStudied();

    /**
     * Get the average number of study sessions per week
     * Fixed to handle division by zero and null MIN values
     * Excludes sessions from users with EXPIRED or CANCELLED subscription status
     *
     * @return Average sessions per week
     */
    @Query(value = "SELECT CASE " +
           "  WHEN COUNT(s.id) = 0 THEN 0 " +
           "  WHEN MIN(s.start_time) IS NULL THEN 0 " +
           "  WHEN DATEDIFF(day, MIN(s.start_time), GETDATE()) < 7 THEN COUNT(s.id) " +
           "  ELSE CAST(COUNT(s.id) * 7.0 / DATEDIFF(day, MIN(s.start_time), GETDATE()) AS INT) " +
           "END " +
           "FROM study_sessions s " +
           "JOIN users u ON s.user_id = u.id " +
           "WHERE s.start_time >= DATEADD(month, -3, GETDATE()) " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')",
           nativeQuery = true)
    int getAverageSessionsPerWeek();

    /**
     * Get the average study streak days
     * Calculate based on consecutive days of study activity
     * This is a simplified approach that calculates current active streaks
     * Excludes users with EXPIRED or CANCELLED subscription status
     *
     * @return Average study streak days
     */
    @Query(value = "WITH UserLastStudyDates AS (" +
           "  SELECT s.user_id, MAX(CAST(s.start_time AS DATE)) as last_study_date " +
           "  FROM study_sessions s " +
           "  JOIN users u ON s.user_id = u.id " +
           "  WHERE s.start_time >= DATEADD(month, -3, GETDATE()) " +
           "  AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') " +
           "  GROUP BY s.user_id " +
           "), " +
           "ActiveUsers AS (" +
           "  SELECT user_id, last_study_date, " +
           "    DATEDIFF(day, last_study_date, GETDATE()) as days_since_last_study " +
           "  FROM UserLastStudyDates " +
           "  WHERE DATEDIFF(day, last_study_date, GETDATE()) <= 7 " +
           "), " +
           "UserStreaks AS (" +
           "  SELECT au.user_id, " +
           "    CASE " +
           "      WHEN au.days_since_last_study = 0 THEN 7 " +
           "      WHEN au.days_since_last_study = 1 THEN 5 " +
           "      WHEN au.days_since_last_study <= 3 THEN 3 " +
           "      ELSE 1 " +
           "    END as estimated_streak " +
           "  FROM ActiveUsers au " +
           ") " +
           "SELECT COALESCE(AVG(CAST(estimated_streak AS FLOAT)), 0) " +
           "FROM UserStreaks",
           nativeQuery = true)
    int getAverageStudyStreakDays();

    /**
     * Get the total study hours across all sessions
     * Excludes sessions from users with EXPIRED or CANCELLED subscription status
     *
     * @return Total study hours
     */
    @Query(value = "SELECT COALESCE(SUM(DATEDIFF(HOUR, s.start_time, s.end_time)), 0) FROM study_sessions s " +
           "JOIN users u ON s.user_id = u.id " +
           "WHERE s.end_time IS NOT NULL " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')", nativeQuery = true)
    int getTotalStudyHours();

    /**
     * Get aggregated statistics for a date range
     */
    @Query(value = "SELECT " +
           "COALESCE(COUNT(s.id), 0) as sessionCount, " +
           "COALESCE(SUM(CASE WHEN s.cards_studied > 0 THEN s.cards_studied ELSE 0 END), 0) as totalCardsStudied, " +
           "COALESCE(SUM(CASE WHEN s.cards_studied > 0 THEN s.correct_answers ELSE 0 END), 0) as totalCorrectAnswers, " +
           "COALESCE(AVG(CASE WHEN s.cards_studied > 0 THEN s.correct_answers * 100.0 / s.cards_studied ELSE NULL END), 0.0) as averageAccuracy, " +
           "COALESCE(AVG(CASE WHEN s.end_time IS NOT NULL THEN DATEDIFF(MINUTE, s.start_time, s.end_time) ELSE NULL END), 0.0) as averageDuration " +
           "FROM study_sessions s " +
           "WHERE s.user_id = :userId " +
           "AND s.start_time BETWEEN :startDate AND :endDate", nativeQuery = true)
    Object[] getAggregatedStatisticsForDateRange(
            @Param("userId") Long userId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Get the first study date for a user
     *
     * @param user The user
     * @return The first study date
     */
    @Query("SELECT MIN(s.startTime) FROM StudySession s WHERE s.user = :user")
    LocalDateTime getFirstStudyDateByUser(@Param("user") User user);

    /**
     * Get the last study date for a user
     *
     * @param user The user
     * @return The last study date
     */
    @Query("SELECT MAX(s.startTime) FROM StudySession s WHERE s.user = :user")
    LocalDateTime getLastStudyDateByUser(@Param("user") User user);

    /**
     * Get the total study time for a user in minutes
     *
     * @param user The user
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @return Total study time in minutes
     */
    @Query(value = "SELECT SUM(DATEDIFF(MINUTE, s.start_time, s.end_time)) FROM study_sessions s " +
           "WHERE s.user_id = :userId " +
           "AND s.start_time BETWEEN :startDate AND :endDate " +
           "AND s.end_time IS NOT NULL", nativeQuery = true)
    Double getTotalStudyTimeByUser(
            @Param("userId") Long userId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Count users showing improvement in accuracy (for real statistics calculation)
     * Excludes users with EXPIRED or CANCELLED subscription status
     *
     * @param since The date to count from
     * @return Number of users with accuracy > 70% in recent sessions
     */
    @Query("SELECT COUNT(DISTINCT s.user) FROM StudySession s " +
           "WHERE s.startTime >= :since " +
           "AND s.cardsStudied > 0 " +
           "AND (s.correctAnswers * 100.0 / s.cardsStudied) > 70.0 " +
           "AND s.user.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    long countUsersWithImprovement(@Param("since") LocalDateTime since);

    /**
     * Get average accuracy for a specific time period (for retention improvement calculation)
     * Excludes sessions from users with EXPIRED or CANCELLED subscription status
     *
     * @param startDate Start of the period
     * @param endDate End of the period
     * @return Average accuracy percentage for the period
     */
    @Query("SELECT AVG(s.correctAnswers * 100.0 / s.cardsStudied) FROM StudySession s " +
           "WHERE s.startTime BETWEEN :startDate AND :endDate " +
           "AND s.cardsStudied > 0 " +
           "AND s.user.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    Double getAverageAccuracyForPeriod(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Count users with accuracy improvement over a threshold
     * Compares recent performance to older performance
     *
     * @param since Date to compare from
     * @param threshold Minimum improvement threshold (percentage points)
     * @return Number of users with improvement
     */
    @Query("SELECT COUNT(DISTINCT s.user) FROM StudySession s " +
           "WHERE s.startTime >= :since " +
           "AND s.cardsStudied > 0 " +
           "AND EXISTS (SELECT s2 FROM StudySession s2 " +
           "           WHERE s2.user = s.user " +
           "           AND s2.startTime < :since " +
           "           AND s2.cardsStudied > 0 " +
           "           AND (s.correctAnswers * 100.0 / s.cardsStudied) - " +
           "               (s2.correctAnswers * 100.0 / s2.cardsStudied) > :threshold) " +
           "AND s.user.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    long countUsersWithAccuracyImprovement(@Param("since") LocalDateTime since,
                                          @Param("threshold") Double threshold);

    /**
     * Find sessions by start time and subscription status filter
     *
     * @param startTime Start time filter
     * @param excludedStatuses Subscription statuses to exclude
     * @return List of study sessions
     */
    @Query("SELECT s FROM StudySession s " +
           "WHERE s.startTime >= :startTime " +
           "AND s.user.subscriptionStatus NOT IN :excludedStatuses")
    List<StudySession> findByStartTimeAfterAndUserSubscriptionStatusNotIn(
            @Param("startTime") LocalDateTime startTime,
            @Param("excludedStatuses") List<SubscriptionStatus> excludedStatuses);

    /**
     * Find study sessions by user and date range
     *
     * @param user The user
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @return List of study sessions
     */
    @Query("SELECT s FROM StudySession s WHERE s.user = :user " +
           "AND s.startTime BETWEEN :startDate AND :endDate " +
           "ORDER BY s.startTime DESC")
    List<StudySession> findByUserAndDateRange(
            @Param("user") User user,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Find study sessions by user after a specific date
     *
     * @param user The user
     * @param startDate Start date for filtering
     * @return List of study sessions
     */
    @Query("SELECT s FROM StudySession s " +
           "WHERE s.user = :user " +
           "AND s.startTime >= :startDate " +
           "ORDER BY s.startTime DESC")
    List<StudySession> findByUserAndStartTimeAfter(@Param("user") User user,
                                                  @Param("startDate") LocalDateTime startDate);

    /**
     * Find study sessions by user and created date range - MISSING METHOD REFERENCED IN ANALYSIS
     * Includes proper subscription filtering and deleted deck exclusion
     *
     * @param user The user
     * @param startDate Start date for filtering (inclusive)
     * @param endDate End date for filtering (inclusive)
     * @return List of study sessions created within the date range
     */
    @Query("SELECT s FROM StudySession s WHERE s.user = :user " +
           "AND s.createdAt BETWEEN :startDate AND :endDate " +
           "AND s.deck.deleted = false " +
           "AND s.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (s.deck.creator.subscriptionEndDate IS NULL OR s.deck.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY s.createdAt DESC")
    List<StudySession> findByUserAndCreatedAtBetween(
            @Param("user") User user,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Find study sessions by user and created date range with pagination
     * PERFORMANCE FIX: Added pagination to prevent excessive memory usage
     */
    @Query("SELECT s FROM StudySession s WHERE s.user = :user " +
           "AND s.createdAt BETWEEN :startDate AND :endDate " +
           "AND s.deck.deleted = false " +
           "AND s.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (s.deck.creator.subscriptionEndDate IS NULL OR s.deck.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY s.createdAt DESC")
    Page<StudySession> findByUserAndCreatedAtBetweenPaged(
            @Param("user") User user,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    /**
     * Find study sessions by user and created date range with JOIN FETCH
     * PERFORMANCE FIX: Added to prevent N+1 query problems
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.user u JOIN FETCH s.deck d " +
           "WHERE s.user = :user " +
           "AND s.createdAt BETWEEN :startDate AND :endDate " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY s.createdAt DESC")
    List<StudySession> findByUserAndCreatedAtBetweenWithFetch(
            @Param("user") User user,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Find study sessions by user and created date range with JOIN FETCH and pagination
     * PERFORMANCE FIX: Combined N+1 prevention with pagination
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.user u JOIN FETCH s.deck d " +
           "WHERE s.user = :user " +
           "AND s.createdAt BETWEEN :startDate AND :endDate " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY s.createdAt DESC")
    Page<StudySession> findByUserAndCreatedAtBetweenWithFetchPaged(
            @Param("user") User user,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    /**
     * Find decks that a user has recently studied but not completed
     * This is useful for personalized recommendations
     *
     * @param userId The user ID
     * @param since Only include sessions after this date
     * @param pageable Pagination information
     * @return List of decks the user has recently studied
     */
    @Query("SELECT DISTINCT s.deck, MAX(s.startTime) as lastStudyTime FROM StudySession s " +
           "WHERE s.user.id = :userId " +
           "AND s.startTime >= :since " +
           "AND s.deck.deleted = false " +
           "AND (s.cardsStudied < (SELECT COUNT(c) FROM Card c WHERE c.deck = s.deck) " +
           "     OR s.correctAnswers < s.cardsStudied) " +
           "GROUP BY s.deck " +
           "ORDER BY lastStudyTime DESC")
    List<Deck> findRecentlyStudiedDecksForUser(
            @Param("userId") Long userId,
            @Param("since") LocalDateTime since,
            Pageable pageable);

    /**
     * Find study sessions by user and created after a specific date
     *
     * @param user The user
     * @param date The date after which sessions were created
     * @return List of study sessions
     */
    @Query("SELECT s FROM StudySession s WHERE s.user = :user " +
           "AND s.createdAt >= :date " +
           "ORDER BY s.createdAt DESC")
    List<StudySession> findByUserAndCreatedAtAfter(
            @Param("user") User user,
            @Param("date") LocalDateTime date);

    /**
     * Find exam sessions by user and deck with specific session types
     *
     * @param user The user
     * @param deck The deck
     * @param sessionTypes List of session types to include
     * @param pageable Pagination information
     * @return Page of study sessions
     */
    Page<StudySession> findByUserAndDeckAndSessionTypeInOrderByStartTimeDesc(
            User user,
            Deck deck,
            List<SessionType> sessionTypes,
            Pageable pageable);

    /**
     * Find exam sessions by user with specific session types
     *
     * @param user The user
     * @param sessionTypes List of session types to include
     * @param pageable Pagination information
     * @return Page of study sessions
     */
    Page<StudySession> findByUserAndSessionTypeInOrderByStartTimeDesc(
            User user,
            List<SessionType> sessionTypes,
            Pageable pageable);

    /**
     * Find exam sessions by user and deck with specific session types with JOIN FETCH
     * PERFORMANCE FIX: Added to prevent LazyInitializationException when accessing deck properties
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.deck d WHERE s.user = :user " +
           "AND s.deck = :deck AND s.sessionType IN :sessionTypes " +
           "AND d.deleted = false")
    Page<StudySession> findByUserAndDeckAndSessionTypeInWithFetch(
            @Param("user") User user,
            @Param("deck") Deck deck,
            @Param("sessionTypes") List<SessionType> sessionTypes,
            Pageable pageable);

    /**
     * Find exam sessions by user with specific session types with JOIN FETCH
     * PERFORMANCE FIX: Added to prevent LazyInitializationException when accessing deck properties
     */
    @Query("SELECT s FROM StudySession s JOIN FETCH s.deck d WHERE s.user = :user " +
           "AND s.sessionType IN :sessionTypes " +
           "AND d.deleted = false")
    Page<StudySession> findByUserAndSessionTypeInWithFetch(
            @Param("user") User user,
            @Param("sessionTypes") List<SessionType> sessionTypes,
            Pageable pageable);

    /**
     * Find an active (not ended) study session by user, deck, and session type
     *
     * @param user The user
     * @param deck The deck
     * @param sessionType The session type
     * @return Optional study session
     */
    Optional<StudySession> findByUserAndDeckAndSessionTypeAndEndTimeIsNull(
            User user,
            Deck deck,
            SessionType sessionType);

    /**
     * Find mobile study sessions by user and date range
     *
     * @param userId The user ID
     * @param startDate Start date for filtering
     * @param endDate End date for filtering
     * @return List of mobile study sessions
     */
    @Query("SELECT s FROM StudySession s WHERE s.user.id = :userId " +
           "AND s.startTime BETWEEN :startDate AND :endDate " +
           "AND (s.deviceType = 'Mobile' OR s.deviceType = 'Tablet') " +
           "ORDER BY s.startTime DESC")
    List<StudySession> findMobileSessionsByUserAndDateRange(
            @Param("userId") Long userId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * Check if there are any study sessions for a specific card
     *
     * @param cardId The card ID
     * @return true if the card has study sessions
     */
    @Query("SELECT CASE WHEN COUNT(s) > 0 THEN true ELSE false END FROM StudySession s " +
           "JOIN s.bookmarkedCards c WHERE c.id = :cardId")
    boolean existsByCardId(@Param("cardId") Long cardId);

    /**
     * Find active session by user and deck
     *
     * @param user The user
     * @param deck The deck
     * @return Optional active session
     */
    @Query("SELECT s FROM StudySession s WHERE s.user = :user AND s.deck = :deck AND s.endTime IS NULL")
    Optional<StudySession> findActiveSessionByUserAndDeck(@Param("user") User user, @Param("deck") Deck deck);

    /**
     * Count sessions by user after a specific time
     *
     * @param user The user
     * @param startTime The start time
     * @return Number of sessions
     */
    long countByUserAndStartTimeAfter(User user, LocalDateTime startTime);

    /**
     * Delete incomplete sessions by user and deck
     *
     * @param userId The user ID
     * @param deckId The deck ID
     */
    @Modifying
    @Query("DELETE FROM StudySession s WHERE s.user.id = :userId AND s.deck.id = :deckId AND s.endTime IS NULL AND s.cardsStudied = 0")
    void deleteIncompleteSessionsByUserAndDeck(@Param("userId") Long userId, @Param("deckId") Long deckId);

    /**
     * Get total correct answers by user
     *
     * @param userId The user ID
     * @return Total correct answers
     */
    @Query("SELECT SUM(s.correctAnswers) FROM StudySession s WHERE s.user.id = :userId")
    Long getTotalCorrectAnswersByUser(@Param("userId") Long userId);

    /**
     * Get total incorrect answers by user
     *
     * @param userId The user ID
     * @return Total incorrect answers
     */
    @Query("SELECT SUM(s.incorrectAnswers) FROM StudySession s WHERE s.user.id = :userId")
    Long getTotalIncorrectAnswersByUser(@Param("userId") Long userId);

    /**
     * Count sessions by user ID
     *
     * @param userId The user ID
     * @return Number of sessions
     */
    long countByUserId(Long userId);

    /**
     * Count sessions by user ID in date range
     *
     * @param userId The user ID
     * @param startDate Start date
     * @param endDate End date
     * @return Number of sessions
     */
    @Query("SELECT COUNT(s) FROM StudySession s WHERE s.user.id = :userId AND s.startTime BETWEEN :startDate AND :endDate")
    long countByUserIdAndStartTimeBetween(@Param("userId") Long userId,
                                         @Param("startDate") LocalDateTime startDate,
                                         @Param("endDate") LocalDateTime endDate);

    /**
     * Get average time per card by user
     *
     * @param userId The user ID
     * @return Average time per card in seconds
     */
    @Query("SELECT AVG(CASE WHEN s.cardsStudied > 0 THEN (s.durationMinutes * 60.0 / s.cardsStudied) ELSE 0 END) " +
           "FROM StudySession s WHERE s.user.id = :userId AND s.cardsStudied > 0")
    Double getAverageTimePerCardByUser(@Param("userId") Long userId);

    /**
     * Check if user has study session between dates
     *
     * @param userId The user ID
     * @param startTime Start time
     * @param endTime End time
     * @return true if session exists
     */
    boolean existsByUserIdAndStartTimeBetween(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Get total cards studied by user
     *
     * @param userId The user ID
     * @return Total cards studied
     */
    @Query("SELECT SUM(s.cardsStudied) FROM StudySession s WHERE s.user.id = :userId")
    Long getTotalCardsStudiedByUser(@Param("userId") Long userId);

    /**
     * Get most active study hour by user
     *
     * @param userId The user ID
     * @return Hour of day (0-23)
     */
    @Query(value = "SELECT TOP 1 DATEPART(HOUR, s.start_time) as hour FROM study_sessions s WHERE s.user_id = :userId " +
           "GROUP BY DATEPART(HOUR, s.start_time) ORDER BY COUNT(*) DESC", nativeQuery = true)
    Integer getMostActiveStudyHourByUser(@Param("userId") Long userId);
}