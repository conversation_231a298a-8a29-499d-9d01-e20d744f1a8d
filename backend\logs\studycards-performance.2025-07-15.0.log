2025-07-15 00:37:32.194 [MessageBroker-5] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [72843240ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 72843240ms
2025-07-15 00:37:32.196 [MessageBroker-5] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [72843242ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredSubscriptions took 72843242ms
2025-07-15 00:37:35.760 [MessageBroker-5] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [72847886ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 72847886ms
2025-07-15 00:37:35.761 [MessageBroker-1] WARN  PERFORMANCE - 
                [hourlyPerformanceReport] [6392ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 6392ms
2025-07-15 00:37:35.956 [MessageBroker-2] WARN  PERFORMANCE - 
                [deleteOldNotifications] [6402ms] [] SLOW_DB_OPERATION: $Proxy220.deleteOldNotifications took 6402ms
2025-07-15 00:37:35.991 [MessageBroker-13] WARN  PERFORMANCE - 
                [cleanupExpiredTokens] [6099ms] [] SLOW_DB_OPERATION: $Proxy234.deleteAllExpiredTokens took 6099ms
2025-07-15 00:37:36.079 [ForkJoinPool.commonPool-worker-32] WARN  PERFORMANCE - 
                [] [72847125ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 72847125ms
2025-07-15 00:37:36.490 [ForkJoinPool.commonPool-worker-33] WARN  PERFORMANCE - 
                [] [749ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 749ms
2025-07-15 00:37:44.206 [MessageBroker-1] WARN  PERFORMANCE - 
                [hourlyPerformanceReport] [8443ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 8443ms
2025-07-15 00:37:44.207 [MessageBroker-13] WARN  PERFORMANCE - 
                [cleanupExpiredTokens] [14317ms] [] SLOW_SERVICE_METHOD: PasswordResetService.cleanupExpiredTokens took 14317ms
2025-07-15 00:37:44.207 [MessageBroker-2] WARN  PERFORMANCE - 
                [deleteOldNotifications] [14656ms] [] SLOW_SERVICE_METHOD: NotificationService.deleteOldNotifications took 14656ms
2025-07-15 00:37:44.246 [ForkJoinPool.commonPool-worker-34] WARN  PERFORMANCE - 
                [] [8409ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 8409ms
2025-07-15 00:37:44.267 [ForkJoinPool.commonPool-worker-33] WARN  PERFORMANCE - 
                [] [7777ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 7777ms
2025-07-15 00:37:44.293 [MessageBroker-11] WARN  PERFORMANCE - 
                [sendVerificationReminders] [8536ms] [] SLOW_DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 8536ms
2025-07-15 00:37:44.309 [MessageBroker-11] WARN  PERFORMANCE - 
                [sendVerificationReminders] [14832ms] [] SLOW_SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 14832ms
2025-07-15 00:37:44.316 [ForkJoinPool.commonPool-worker-32] WARN  PERFORMANCE - 
                [] [8237ms] [] SLOW_DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 8237ms
2025-07-15 00:37:44.331 [MessageBroker-15] WARN  PERFORMANCE - 
                [generateDailyNotifications] [8491ms] [] SLOW_DB_OPERATION: $Proxy194.findAll took 8491ms
2025-07-15 00:37:44.331 [MessageBroker-15] WARN  PERFORMANCE - 
                [generateDailyNotifications] [8570ms] [] SLOW_SERVICE_METHOD: UserService.getAllUsers took 8570ms
2025-07-15 00:37:44.334 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [116ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 116ms
2025-07-15 00:37:44.335 [ForkJoinPool.commonPool-worker-35] INFO  PERFORMANCE - 
                [] [113ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 113ms
2025-07-15 00:37:44.352 [ForkJoinPool.commonPool-worker-34] INFO  PERFORMANCE - 
                [] [106ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 106ms
2025-07-15 00:37:44.353 [MessageBroker-5] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [8593ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 8593ms
2025-07-15 00:37:44.531 [MessageBroker-14] WARN  PERFORMANCE - 
                [periodicHealthCheck] [72856631ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 72856631ms
2025-07-15 00:37:44.548 [MessageBroker-14] WARN  PERFORMANCE - 
                [periodicHealthCheck] [72856685ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 72856685ms
2025-07-15 00:37:44.561 [MessageBroker-15] ERROR PERFORMANCE - 
                [generateDailyNotifications] [129ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 129ms with error: ConversionFailedException
2025-07-15 00:37:44.673 [StudyCards-Async-3] INFO  PERFORMANCE - 
                [createDueCardsReminder] [226ms] [] DB_OPERATION: $Proxy220.save took 226ms
2025-07-15 00:37:44.717 [MessageBroker-15] WARN  PERFORMANCE - 
                [generateDailyNotifications] [15182ms] [] SLOW_SERVICE_METHOD: DashboardService.generateDailyNotifications took 15182ms
2025-07-15 00:37:44.729 [MessageBroker-9] WARN  PERFORMANCE - 
                [cacheWarming] [15375ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 15375ms
2025-07-15 00:37:44.746 [MessageBroker-9] WARN  PERFORMANCE - 
                [cacheWarming] [72855934ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 72855934ms
2025-07-15 00:37:44.883 [ForkJoinPool.commonPool-worker-35] INFO  PERFORMANCE - 
                [] [123ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 123ms
2025-07-15 00:37:45.091 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [524ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 524ms
2025-07-15 00:37:45.095 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [547ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 547ms
2025-07-15 00:37:45.871 [ForkJoinPool.commonPool-worker-35] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 119ms
2025-07-15 00:37:46.120 [ForkJoinPool.commonPool-worker-35] INFO  PERFORMANCE - 
                [] [113ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 113ms
2025-07-15 00:37:49.590 [MessageBroker-10] WARN  PERFORMANCE - 
                [cacheWarming] [3388ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 3388ms
2025-07-15 00:37:49.592 [MessageBroker-10] WARN  PERFORMANCE - 
                [cacheWarming] [3392ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 3392ms
2025-07-15 00:37:49.817 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [223ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 223ms
2025-07-15 00:37:49.837 [ForkJoinPool.commonPool-worker-35] WARN  PERFORMANCE - 
                [] [3632ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 3632ms
2025-07-15 00:37:49.839 [MessageBroker-14] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4234ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4234ms
2025-07-15 00:37:49.890 [MessageBroker-14] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4296ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 4296ms
2025-07-15 00:37:50.266 [MessageBroker-10] INFO  PERFORMANCE - 
                [cacheWarming] [672ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 672ms
2025-07-15 00:37:50.272 [MessageBroker-10] INFO  PERFORMANCE - 
                [cacheWarming] [680ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 680ms
2025-07-15 00:37:50.916 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [244ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 244ms
2025-07-15 00:37:51.180 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [264ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 264ms
2025-07-15 00:37:51.661 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [481ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 481ms
2025-07-15 00:37:53.403 [ForkJoinPool.commonPool-worker-36] WARN  PERFORMANCE - 
                [] [1742ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWithImprovement took 1742ms
2025-07-15 00:38:03.810 [ForkJoinPool.commonPool-worker-35] INFO  PERFORMANCE - 
                [] [443ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 443ms
2025-07-15 00:38:04.532 [ForkJoinPool.commonPool-worker-35] WARN  PERFORMANCE - 
                [] [722ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 722ms
2025-07-15 00:38:04.588 [ForkJoinPool.commonPool-worker-36] WARN  PERFORMANCE - 
                [] [11185ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 11185ms
2025-07-15 00:38:04.681 [ForkJoinPool.commonPool-worker-35] INFO  PERFORMANCE - 
                [] [149ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 149ms
2025-07-15 00:38:11.699 [ForkJoinPool.commonPool-worker-35] WARN  PERFORMANCE - 
                [] [7018ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 7018ms
2025-07-15 00:38:12.049 [ForkJoinPool.commonPool-worker-36] WARN  PERFORMANCE - 
                [] [557ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 557ms
2025-07-15 00:38:12.450 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [401ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 401ms
2025-07-15 00:38:12.652 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [202ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 202ms
2025-07-15 00:38:12.756 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [104ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 104ms
2025-07-15 00:38:12.758 [MessageBroker-15] WARN  PERFORMANCE - 
                [periodicHealthCheck] [22089ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 22089ms
2025-07-15 00:38:12.778 [MessageBroker-15] WARN  PERFORMANCE - 
                [periodicHealthCheck] [22111ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 22111ms
2025-07-15 00:38:13.116 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [239ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 239ms
2025-07-15 00:38:13.226 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [110ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 110ms
2025-07-15 00:38:13.677 [MessageBroker-15] INFO  PERFORMANCE - 
                [periodicHealthCheck] [897ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 897ms
2025-07-15 00:38:13.678 [MessageBroker-15] INFO  PERFORMANCE - 
                [periodicHealthCheck] [900ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 900ms
2025-07-15 00:38:14.039 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [126ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 126ms
2025-07-15 00:38:14.427 [MessageBroker-15] INFO  PERFORMANCE - 
                [periodicHealthCheck] [633ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 633ms
2025-07-15 00:38:14.432 [MessageBroker-15] INFO  PERFORMANCE - 
                [periodicHealthCheck] [639ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 639ms
2025-07-15 00:38:15.974 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 121ms
2025-07-15 00:38:16.080 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [106ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 106ms
2025-07-15 00:38:19.332 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [292ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 292ms
2025-07-15 00:38:19.455 [ForkJoinPool.commonPool-worker-36] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 101ms
2025-07-15 00:38:19.659 [MessageBroker-8] INFO  PERFORMANCE - 
                [periodicHealthCheck] [806ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 806ms
2025-07-15 00:38:19.661 [MessageBroker-8] INFO  PERFORMANCE - 
                [periodicHealthCheck] [810ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 810ms
2025-07-15 00:40:57.323 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getDashboardData] [22ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 22ms with error: ConversionFailedException
2025-07-15 00:40:57.330 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getCardsForReview] [30ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 30ms with error: LazyInitializationException
2025-07-15 00:40:57.331 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [31ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 31ms with error: LazyInitializationException
2025-07-15 00:40:57.379 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getUserDecks] [122ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 122ms
2025-07-15 00:41:22.174 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getCardsForReview] [16ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 16ms with error: LazyInitializationException
2025-07-15 00:41:22.174 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [24ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 24ms with error: LazyInitializationException
2025-07-15 00:41:24.262 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getCardsForReview] [14ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 14ms with error: LazyInitializationException
2025-07-15 00:41:24.262 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [14ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 14ms with error: LazyInitializationException
2025-07-15 00:43:06.099 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [264ms] [] DB_OPERATION: $Proxy194.findByEmail took 264ms
2025-07-15 00:43:09.112 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCurrentUser] [2982ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 2982ms
2025-07-15 00:43:09.112 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCurrentUser] [2982ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 2982ms
2025-07-15 00:43:15.295 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [225ms] [] DB_OPERATION: $Proxy194.findByEmail took 225ms
2025-07-15 00:45:46.651 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getCardsForReview] [38ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 38ms with error: LazyInitializationException
2025-07-15 00:45:46.651 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [38ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 38ms with error: LazyInitializationException
2025-07-15 00:45:46.659 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [8ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 8ms with error: ConversionFailedException
2025-07-15 00:45:47.730 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getCardsForReview] [4ms] [] FAILED_SERVICE_METHOD: DashboardService.getCardsForReview failed after 4ms with error: LazyInitializationException
2025-07-15 00:45:47.730 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [4ms] [] FAILED_ENDPOINT: DashboardController.getCardsForReview failed after 4ms with error: LazyInitializationException
2025-07-15 05:39:19.524 [ForkJoinPool.commonPool-worker-69] WARN  PERFORMANCE - 
                [] [1968ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1968ms
2025-07-15 05:39:19.524 [MessageBroker-3] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [1041ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 1041ms
2025-07-15 05:39:19.526 [MessageBroker-3] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [1046ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 1046ms
2025-07-15 05:39:19.564 [MessageBroker-3] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [2069ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 2069ms
2025-07-15 05:39:20.001 [ForkJoinPool.commonPool-worker-69] INFO  PERFORMANCE - 
                [] [475ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 475ms
2025-07-15 05:39:20.412 [ForkJoinPool.commonPool-worker-69] INFO  PERFORMANCE - 
                [] [411ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 411ms
2025-07-15 05:39:21.332 [ForkJoinPool.commonPool-worker-71] WARN  PERFORMANCE - 
                [] [948ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 948ms
2025-07-15 05:39:21.371 [ForkJoinPool.commonPool-worker-69] WARN  PERFORMANCE - 
                [] [729ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 729ms
2025-07-15 05:39:21.465 [MessageBroker-12] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3938ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 3938ms
2025-07-15 05:39:21.470 [MessageBroker-12] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3982ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 3982ms
2025-07-15 05:39:21.884 [ForkJoinPool.commonPool-worker-71] WARN  PERFORMANCE - 
                [] [552ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 552ms
2025-07-15 05:39:22.216 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [332ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 332ms
2025-07-15 05:39:22.337 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 121ms
2025-07-15 05:39:22.486 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [147ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 147ms
2025-07-15 05:39:22.680 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [190ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 190ms
2025-07-15 05:39:23.059 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [287ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 287ms
2025-07-15 05:39:23.421 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [210ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 210ms
2025-07-15 05:39:23.558 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [4025ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4025ms
2025-07-15 05:39:23.560 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [5574ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 5574ms
2025-07-15 05:39:23.789 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [114ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 114ms
2025-07-15 05:39:24.033 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [150ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 150ms
2025-07-15 05:39:24.317 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [284ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 284ms
2025-07-15 05:39:24.585 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [258ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 258ms
2025-07-15 05:39:24.585 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [1022ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1022ms
2025-07-15 05:39:24.591 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [1029ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1029ms
2025-07-15 05:39:24.828 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [232ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 232ms
2025-07-15 05:39:25.188 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [127ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 127ms
2025-07-15 05:39:25.335 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [147ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 147ms
2025-07-15 05:39:25.494 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [159ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 159ms
2025-07-15 05:39:25.804 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [310ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 310ms
2025-07-15 05:39:26.126 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [322ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 322ms
2025-07-15 05:39:26.355 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [219ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 219ms
2025-07-15 05:39:26.497 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [142ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 142ms
2025-07-15 05:39:26.708 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [144ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 144ms
2025-07-15 05:39:26.709 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [2115ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2115ms
2025-07-15 05:39:26.711 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [2120ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 2120ms
2025-07-15 05:39:27.010 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 119ms
2025-07-15 05:39:27.187 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [134ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 134ms
2025-07-15 05:39:27.328 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 105ms
2025-07-15 05:39:27.543 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [830ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 830ms
2025-07-15 05:39:27.548 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [837ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 837ms
2025-07-15 10:27:51.170 [MessageBroker-1] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [241ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 241ms
2025-07-15 10:27:51.178 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [146ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 146ms
2025-07-15 10:27:51.209 [MessageBroker-7] INFO  PERFORMANCE - 
                [sendVerificationReminders] [124ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 124ms
2025-07-15 10:27:51.253 [MessageBroker-11] INFO  PERFORMANCE - 
                [generateDailyNotifications] [320ms] [] DB_OPERATION: $Proxy194.findAll took 320ms
2025-07-15 10:27:51.375 [MessageBroker-11] INFO  PERFORMANCE - 
                [generateDailyNotifications] [119ms] [] DB_OPERATION: $Proxy207.findAllDueCardsForUser took 119ms
2025-07-15 10:27:51.550 [MessageBroker-1] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [306ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 306ms
2025-07-15 10:27:51.617 [MessageBroker-11] ERROR PERFORMANCE - 
                [generateDailyNotifications] [233ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 233ms with error: ConversionFailedException
2025-07-15 10:27:51.912 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [391ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 391ms
2025-07-15 10:27:53.591 [MessageBroker-1] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [2682ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 2682ms
2025-07-15 10:27:53.749 [MessageBroker-1] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [134ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 134ms
2025-07-15 10:27:53.754 [ForkJoinPool.commonPool-worker-71] WARN  PERFORMANCE - 
                [] [1840ms] [] SLOW_DB_OPERATION: $Proxy208.countDistinctTags took 1840ms
2025-07-15 10:27:53.761 [MessageBroker-11] INFO  PERFORMANCE - 
                [generateDailyNotifications] [170ms] [] DB_OPERATION: $Proxy207.findAllDueCardsForUser took 170ms
2025-07-15 10:27:53.891 [MessageBroker-11] INFO  PERFORMANCE - 
                [generateDailyNotifications] [130ms] [] DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser took 130ms
2025-07-15 10:27:53.947 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [193ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 193ms
2025-07-15 10:27:53.948 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2918ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2918ms
2025-07-15 10:27:53.981 [StudyCards-Async-2] WARN  PERFORMANCE - 
                [createDueCardsReminder] [1858ms] [] SLOW_DB_OPERATION: $Proxy220.save took 1858ms
2025-07-15 10:27:53.982 [StudyCards-Async-2] WARN  PERFORMANCE - 
                [createDueCardsReminder] [2593ms] [] SLOW_SERVICE_METHOD: NotificationService.createDueCardsReminder took 2593ms
2025-07-15 10:27:54.092 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3188ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 3188ms
2025-07-15 10:27:54.336 [MessageBroker-11] INFO  PERFORMANCE - 
                [generateDailyNotifications] [415ms] [] DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser took 415ms
2025-07-15 10:27:54.336 [MessageBroker-11] WARN  PERFORMANCE - 
                [generateDailyNotifications] [3412ms] [] SLOW_SERVICE_METHOD: DashboardService.generateDailyNotifications took 3412ms
2025-07-15 10:27:54.441 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 105ms
2025-07-15 10:27:54.643 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [116ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 116ms
2025-07-15 10:27:54.874 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [135ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 135ms
2025-07-15 10:27:55.132 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [210ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 210ms
2025-07-15 10:27:55.659 [ForkJoinPool.commonPool-worker-71] WARN  PERFORMANCE - 
                [] [527ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 527ms
2025-07-15 10:27:55.935 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [254ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 254ms
2025-07-15 10:27:55.937 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1844ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1844ms
2025-07-15 10:27:55.941 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1849ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1849ms
2025-07-15 10:27:56.167 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [142ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 142ms
2025-07-15 10:27:56.664 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [721ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 721ms
2025-07-15 10:27:56.668 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [725ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 725ms
2025-07-15 10:27:57.536 [ForkJoinPool.commonPool-worker-71] WARN  PERFORMANCE - 
                [] [571ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 571ms
2025-07-15 10:28:02.143 [ForkJoinPool.commonPool-worker-71] WARN  PERFORMANCE - 
                [] [4607ms] [] SLOW_DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 4607ms
2025-07-15 10:28:02.225 [ForkJoinPool.commonPool-worker-69] INFO  PERFORMANCE - 
                [] [271ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 271ms
2025-07-15 10:28:02.259 [ForkJoinPool.commonPool-worker-71] INFO  PERFORMANCE - 
                [] [110ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 110ms
2025-07-15 10:28:02.454 [MessageBroker-9] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5510ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 5510ms
2025-07-15 10:28:02.461 [MessageBroker-9] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5517ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 5517ms
2025-07-15 12:48:23.277 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [359ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 359ms
2025-07-15 12:48:23.381 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [104ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 104ms
2025-07-15 12:48:29.775 [MessageBroker-12] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1542ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationEnd took 1542ms
2025-07-15 12:48:34.069 [MessageBroker-11] WARN  PERFORMANCE - 
                [sendVerificationReminders] [10687ms] [] SLOW_DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 10687ms
2025-07-15 12:48:34.069 [MessageBroker-11] WARN  PERFORMANCE - 
                [sendVerificationReminders] [11153ms] [] SLOW_SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 11153ms
2025-07-15 12:48:37.651 [ForkJoinPool.commonPool-worker-73] WARN  PERFORMANCE - 
                [] [14806ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 14806ms
2025-07-15 12:48:37.681 [ForkJoinPool.commonPool-worker-74] WARN  PERFORMANCE - 
                [] [7466ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 7466ms
2025-07-15 12:48:37.689 [ForkJoinPool.commonPool-worker-75] WARN  PERFORMANCE - 
                [] [912ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 912ms
2025-07-15 12:48:41.692 [MessageBroker-1] WARN  PERFORMANCE - 
                [cacheWarming] [4057ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 4057ms
2025-07-15 12:48:41.702 [MessageBroker-12] WARN  PERFORMANCE - 
                [periodicHealthCheck] [19031ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 19031ms
2025-07-15 12:48:41.712 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [4112ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 4112ms
2025-07-15 12:48:41.712 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [4112ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 4112ms
2025-07-15 12:48:41.927 [ForkJoinPool.commonPool-worker-76] INFO  PERFORMANCE - 
                [] [193ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 193ms
2025-07-15 12:48:41.931 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [19273ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 19273ms
2025-07-15 12:48:41.937 [ForkJoinPool.commonPool-worker-74] WARN  PERFORMANCE - 
                [] [4256ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 4256ms
2025-07-15 12:48:42.117 [ForkJoinPool.commonPool-worker-73] WARN  PERFORMANCE - 
                [] [4466ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 4466ms
2025-07-15 12:48:42.120 [ForkJoinPool.commonPool-worker-76] INFO  PERFORMANCE - 
                [] [193ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 193ms
2025-07-15 12:48:42.129 [ForkJoinPool.commonPool-worker-74] INFO  PERFORMANCE - 
                [] [155ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 155ms
2025-07-15 12:48:42.147 [MessageBroker-12] WARN  PERFORMANCE - 
                [periodicHealthCheck] [19489ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 19489ms
2025-07-15 12:48:42.173 [ForkJoinPool.commonPool-worker-75] WARN  PERFORMANCE - 
                [] [4484ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 4484ms
2025-07-15 12:48:42.201 [MessageBroker-1] WARN  PERFORMANCE - 
                [cacheWarming] [4566ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4566ms
2025-07-15 12:48:42.205 [MessageBroker-1] WARN  PERFORMANCE - 
                [cacheWarming] [19400ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 19400ms
2025-07-15 12:48:42.379 [ForkJoinPool.commonPool-worker-75] INFO  PERFORMANCE - 
                [] [206ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 206ms
2025-07-15 12:48:42.510 [ForkJoinPool.commonPool-worker-75] INFO  PERFORMANCE - 
                [] [113ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 113ms
2025-07-15 15:58:46.725 [MessageBroker-2] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [121ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 121ms
2025-07-15 15:58:46.736 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [128ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 128ms
2025-07-15 15:58:47.535 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [725ms] [] SLOW_DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 725ms
2025-07-15 15:58:48.374 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [756ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 756ms
2025-07-15 15:58:48.808 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [432ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 432ms
2025-07-15 15:58:49.334 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [524ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 524ms
2025-07-15 15:58:49.535 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [198ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 198ms
2025-07-15 15:58:49.727 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 119ms
2025-07-15 15:58:53.269 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [3542ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 3542ms
2025-07-15 15:58:53.277 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [6673ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 6673ms
2025-07-15 15:58:53.309 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [6727ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 6727ms
2025-07-15 15:58:55.020 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [1707ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1707ms
2025-07-15 15:58:55.762 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [742ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 742ms
2025-07-15 15:58:56.113 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [209ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 209ms
2025-07-15 15:58:56.447 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [248ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 248ms
2025-07-15 15:58:56.603 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [154ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 154ms
2025-07-15 19:12:35.479 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [11622168ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 11622168ms
2025-07-15 19:12:35.515 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [11622206ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 11622206ms
2025-07-15 19:12:37.369 [MessageBroker-2] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [1593ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 1593ms
2025-07-15 19:12:37.369 [MessageBroker-2] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [1593ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 1593ms
2025-07-15 19:12:40.195 [ForkJoinPool.commonPool-worker-78] ERROR PERFORMANCE - 
                [] [11623590ms] [] FAILED_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse failed after 11623590ms with error: InvalidDataAccessResourceUsageException
2025-07-15 19:12:46.819 [ForkJoinPool.commonPool-worker-79] WARN  PERFORMANCE - 
                [] [11069ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 11069ms
2025-07-15 19:12:46.859 [ForkJoinPool.commonPool-worker-80] WARN  PERFORMANCE - 
                [] [9738ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 9738ms
2025-07-15 19:12:47.191 [ForkJoinPool.commonPool-worker-83] WARN  PERFORMANCE - 
                [] [882ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 882ms
2025-07-15 19:12:47.549 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [7351ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 7351ms
2025-07-15 19:12:47.568 [ForkJoinPool.commonPool-worker-81] WARN  PERFORMANCE - 
                [] [7012ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 7012ms
2025-07-15 19:12:47.575 [MessageBroker-2] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [12050ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 12050ms
2025-07-15 19:12:47.695 [ForkJoinPool.commonPool-worker-82] WARN  PERFORMANCE - 
                [] [5501ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 5501ms
2025-07-15 19:12:47.821 [ForkJoinPool.commonPool-worker-83] WARN  PERFORMANCE - 
                [] [630ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 630ms
2025-07-15 19:12:47.828 [ForkJoinPool.commonPool-worker-78] INFO  PERFORMANCE - 
                [] [279ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 279ms
2025-07-15 19:12:48.172 [ForkJoinPool.commonPool-worker-80] WARN  PERFORMANCE - 
                [] [1313ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 1313ms
2025-07-15 19:12:48.203 [ForkJoinPool.commonPool-worker-81] WARN  PERFORMANCE - 
                [] [635ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 635ms
2025-07-15 19:12:48.214 [ForkJoinPool.commonPool-worker-79] WARN  PERFORMANCE - 
                [] [1395ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 1395ms
2025-07-15 19:12:48.404 [ForkJoinPool.commonPool-worker-82] WARN  PERFORMANCE - 
                [] [709ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 709ms
2025-07-15 19:12:48.439 [ForkJoinPool.commonPool-worker-84] WARN  PERFORMANCE - 
                [] [983ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 983ms
2025-07-15 19:12:48.450 [ForkJoinPool.commonPool-worker-78] WARN  PERFORMANCE - 
                [] [619ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 619ms
2025-07-15 19:12:48.484 [ForkJoinPool.commonPool-worker-83] WARN  PERFORMANCE - 
                [] [656ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 656ms
2025-07-15 19:12:48.484 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [12962ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 12962ms
2025-07-15 19:12:48.489 [MessageBroker-2] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [248ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 248ms
2025-07-15 19:12:48.495 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [12978ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 12978ms
2025-07-15 19:12:48.510 [ForkJoinPool.commonPool-worker-81] INFO  PERFORMANCE - 
                [] [307ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 307ms
2025-07-15 19:12:48.503 [MessageBroker-2] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [928ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 928ms
2025-07-15 19:12:48.525 [ForkJoinPool.commonPool-worker-79] INFO  PERFORMANCE - 
                [] [311ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 311ms
2025-07-15 19:12:48.542 [ForkJoinPool.commonPool-worker-82] INFO  PERFORMANCE - 
                [] [138ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 138ms
2025-07-15 19:12:48.555 [ForkJoinPool.commonPool-worker-80] INFO  PERFORMANCE - 
                [] [381ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 381ms
2025-07-15 19:12:48.779 [ForkJoinPool.commonPool-worker-84] INFO  PERFORMANCE - 
                [] [340ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 340ms
2025-07-15 19:12:48.779 [ForkJoinPool.commonPool-worker-81] INFO  PERFORMANCE - 
                [] [211ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 211ms
2025-07-15 19:12:48.779 [ForkJoinPool.commonPool-worker-80] INFO  PERFORMANCE - 
                [] [213ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 213ms
2025-07-15 19:12:48.781 [ForkJoinPool.commonPool-worker-82] INFO  PERFORMANCE - 
                [] [237ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 237ms
2025-07-15 19:12:48.785 [ForkJoinPool.commonPool-worker-79] INFO  PERFORMANCE - 
                [] [256ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 256ms
2025-07-15 19:12:48.800 [MessageBroker-4] WARN  PERFORMANCE - 
                [cacheWarming] [11687ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 11687ms
2025-07-15 19:12:48.804 [MessageBroker-4] WARN  PERFORMANCE - 
                [cacheWarming] [13227ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 13227ms
2025-07-15 19:12:49.065 [ForkJoinPool.commonPool-worker-83] INFO  PERFORMANCE - 
                [] [498ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 498ms
2025-07-15 19:12:49.249 [ForkJoinPool.commonPool-worker-83] INFO  PERFORMANCE - 
                [] [181ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 181ms
2025-07-15 19:12:49.400 [MessageBroker-5] INFO  PERFORMANCE - 
                [periodicHealthCheck] [897ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 897ms
2025-07-15 19:12:49.401 [MessageBroker-5] INFO  PERFORMANCE - 
                [periodicHealthCheck] [906ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 906ms
2025-07-15 19:18:37.219 [MessageBroker-8] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-15 19:18:37.494 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 162ms
2025-07-15 19:18:37.494 [MessageBroker-9] INFO  PERFORMANCE - 
                [sendVerificationReminders] [207ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 207ms
2025-07-15 19:18:37.494 [MessageBroker-5] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [207ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 207ms
2025-07-15 19:18:37.494 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 162ms
2025-07-15 19:18:37.506 [main] INFO  PERFORMANCE - 
                [] [149ms] [] DB_OPERATION: $Proxy211.count took 149ms
2025-07-15 19:18:37.695 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [129ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 129ms
2025-07-15 19:18:37.695 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [129ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 129ms
2025-07-15 19:18:37.805 [MessageBroker-7] INFO  PERFORMANCE - 
                [periodicHealthCheck] [589ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 589ms
2025-07-15 19:18:37.805 [MessageBroker-6] INFO  PERFORMANCE - 
                [cacheWarming] [589ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 589ms
2025-07-15 19:19:28.370 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy194.findByEmail took 101ms
2025-07-15 19:19:30.293 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getCurrentUserPreferences] [119ms] [] DB_OPERATION: $Proxy244.findByUser took 119ms
2025-07-15 19:19:30.802 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [104ms] [] DB_OPERATION: $Proxy194.findByEmail took 104ms
2025-07-15 19:19:30.802 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy194.findByEmail took 105ms
2025-07-15 19:19:30.807 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [109ms] [] DB_OPERATION: $Proxy194.findById took 109ms
2025-07-15 19:19:31.022 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [createStudySession] [206ms] [] DB_OPERATION: $Proxy209.save took 206ms
2025-07-15 19:19:31.054 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [348ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 348ms
2025-07-15 19:19:31.129 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCardsByDeckId] [309ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 309ms
2025-07-15 19:19:45.562 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [662ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 662ms
2025-07-15 19:19:45.712 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCurrentUser] [147ms] [] DB_OPERATION: $Proxy194.findById took 147ms
2025-07-15 19:19:46.594 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [130ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 130ms
2025-07-15 19:23:37.739 [ForkJoinPool.commonPool-worker-3] WARN  PERFORMANCE - 
                [] [537ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 537ms
2025-07-15 19:23:38.192 [ForkJoinPool.commonPool-worker-3] INFO  PERFORMANCE - 
                [] [441ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 441ms
2025-07-15 19:23:41.753 [ForkJoinPool.commonPool-worker-3] WARN  PERFORMANCE - 
                [] [3558ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 3558ms
2025-07-15 19:23:41.841 [MessageBroker-7] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4640ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4640ms
2025-07-15 19:23:41.842 [MessageBroker-7] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4656ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 4656ms
2025-07-15 19:28:37.318 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [111ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 111ms
2025-07-15 19:39:28.304 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [8ms] [] FAILED_ENDPOINT: DeckController.getUserDecks failed after 8ms with error: IllegalArgumentException
2025-07-15 19:39:28.316 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [20ms] [] FAILED_ENDPOINT: AnalyticsController.getAllAnalytics failed after 20ms with error: SpelEvaluationException
2025-07-15 19:40:14.260 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [13ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 13ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.266 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.270 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.276 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.281 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.296 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [7ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 7ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.333 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [6ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 6ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.341 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 4ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.341 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.349 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.354 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.359 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.364 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.367 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.374 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.383 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.388 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 4ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.392 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.396 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.401 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.404 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.410 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.414 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.417 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.420 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.426 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.429 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.432 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.438 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [6ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 6ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.438 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.444 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-15 19:40:14.521 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [35ms] [] FAILED_DB_OPERATION: $Proxy209.getSessionCountByDayOfWeek failed after 35ms with error: InvalidDataAccessResourceUsageException
2025-07-15 19:40:14.583 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy209.getSessionCountByDayOfWeek failed after 0ms with error: InvalidDataAccessResourceUsageException
2025-07-15 19:40:14.594 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [469ms] [] FAILED_ENDPOINT: StudySessionController.getStudyStatistics failed after 469ms with error: UnexpectedRollbackException
2025-07-15 19:41:17.388 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [123ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 123ms
2025-07-15 19:41:19.674 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [2233ms] [] SLOW_DB_OPERATION: DeckRepositoryCustomImpl.findRelatedDecksByTagsUsingDeckTags took 2233ms
2025-07-15 19:41:19.674 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [ms] [] SLOW_CUSTOM_QUERY: findRelatedDecksByTagsUsingDeckTags took 2233ms
2025-07-15 19:41:19.674 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [2239ms] [] SLOW_DB_OPERATION: $Proxy206.findRelatedDecksByTags took 2239ms
2025-07-15 19:41:19.694 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [2429ms] [] SLOW_SERVICE_METHOD: DeckService.getEnhancedDeckById took 2429ms
2025-07-15 19:41:19.694 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [2429ms] [] SLOW_ENDPOINT: DeckController.getEnhancedDeckById took 2429ms
2025-07-15 19:43:33.837 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [1569ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1569ms
2025-07-15 19:51:56.743 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [111ms] [] DB_OPERATION: $Proxy194.findByEmail took 111ms
2025-07-15 20:42:46.761 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [543ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 543ms
2025-07-15 20:42:46.827 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [636ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 636ms
2025-07-15 21:14:29.833 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [130ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 130ms
2025-07-15 21:14:29.833 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCardsByDeckId] [132ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 132ms
2025-07-15 21:29:16.049 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [122ms] [] DB_OPERATION: $Proxy194.findByEmail took 122ms
2025-07-15 21:32:22.756 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [122ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 122ms
2025-07-15 22:48:54.330 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [987ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 987ms
2025-07-15 22:48:54.589 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [1272ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 1272ms
2025-07-15 22:48:54.658 [ForkJoinPool.commonPool-worker-44] WARN  PERFORMANCE - 
                [] [1059ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1059ms
2025-07-15 22:48:54.926 [MessageBroker-11] WARN  PERFORMANCE - 
                [hourlyPerformanceReport] [1880ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 1880ms
2025-07-15 22:48:59.343 [MessageBroker-10] WARN  PERFORMANCE - 
                [cacheWarming] [4414ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 4414ms
2025-07-15 22:48:59.343 [MessageBroker-12] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [6084ms] [] SLOW_SERVICE_METHOD: CacheUtilityService.silentClear took 6084ms
2025-07-15 22:48:59.380 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [4441ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 4441ms
2025-07-15 22:48:59.380 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [4441ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredSubscriptions took 4441ms
2025-07-15 22:48:59.393 [ForkJoinPool.commonPool-worker-44] WARN  PERFORMANCE - 
                [] [4735ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 4735ms
2025-07-15 22:48:59.400 [MessageBroker-12] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [6249ms] [] SLOW_SERVICE_METHOD: SearchCacheService.invalidateSearchCaches took 6249ms
2025-07-15 22:48:59.401 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [6279ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 6279ms
2025-07-15 22:48:59.675 [ForkJoinPool.commonPool-worker-45] INFO  PERFORMANCE - 
                [] [132ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 132ms
2025-07-15 22:48:59.690 [ForkJoinPool.commonPool-worker-46] INFO  PERFORMANCE - 
                [] [133ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 133ms
2025-07-15 22:49:00.483 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [754ms] [] SERVICE_METHOD: StatisticsMonitoringService.recordOperationEnd took 754ms
2025-07-15 22:49:00.497 [ForkJoinPool.commonPool-worker-45] WARN  PERFORMANCE - 
                [] [776ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 776ms
2025-07-15 22:49:00.675 [ForkJoinPool.commonPool-worker-45] INFO  PERFORMANCE - 
                [] [138ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 138ms
2025-07-15 22:49:00.690 [ForkJoinPool.commonPool-worker-46] INFO  PERFORMANCE - 
                [] [154ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 154ms
2025-07-15 22:49:00.744 [MessageBroker-10] WARN  PERFORMANCE - 
                [cacheWarming] [6182ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 6182ms
2025-07-15 22:49:00.751 [MessageBroker-14] WARN  PERFORMANCE - 
                [periodicHealthCheck] [7561ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 7561ms
2025-07-15 22:49:00.755 [MessageBroker-10] WARN  PERFORMANCE - 
                [cacheWarming] [7462ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 7462ms
2025-07-15 22:49:00.772 [MessageBroker-14] WARN  PERFORMANCE - 
                [periodicHealthCheck] [7743ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 7743ms
2025-07-15 22:49:01.216 [ForkJoinPool.commonPool-worker-46] INFO  PERFORMANCE - 
                [] [203ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 203ms
2025-07-15 22:49:02.140 [ForkJoinPool.commonPool-worker-46] INFO  PERFORMANCE - 
                [] [114ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 114ms
2025-07-15 22:54:55.173 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [createStudySession] [109ms] [] DB_OPERATION: $Proxy209.save took 109ms
2025-07-15 22:54:55.177 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [164ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 164ms
2025-07-15 22:54:55.179 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getCardsByDeckId] [157ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 157ms
2025-07-15 22:54:59.402 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getCurrentUser] [3999ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 3999ms
2025-07-15 22:54:59.402 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getCurrentUser] [3999ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 3999ms
2025-07-15 22:54:59.402 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getCurrentUser] [4002ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 4002ms
2025-07-15 22:54:59.402 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getCurrentUser] [4002ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 4002ms
2025-07-15 22:54:59.402 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [3986ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 3986ms
2025-07-15 22:54:59.402 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [3986ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 3986ms
2025-07-15 22:54:59.403 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [3987ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.shouldRedirectToSubscription took 3987ms
2025-07-15 23:06:26.310 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [127ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 127ms
2025-07-15 23:08:25.296 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getCurrentUser] [122ms] [] DB_OPERATION: $Proxy194.findById took 122ms
2025-07-15 23:08:25.342 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [160ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 160ms
