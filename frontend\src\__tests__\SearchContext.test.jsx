// src/__tests__/SearchContext.test.jsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { SearchProvider, useSearch } from '../contexts/SearchContext';

// Test component that uses the search context
const TestComponent = () => {
  const { state, actions } = useSearch();
  
  return (
    <div>
      <div data-testid="query">{state.query}</div>
      <div data-testid="search-type">{state.searchType}</div>
      <div data-testid="loading">{state.isLoading.toString()}</div>
      <div data-testid="error">{state.error || 'no-error'}</div>
      <div data-testid="history-count">{state.searchHistory.length}</div>
      <div data-testid="presets-count">{state.filterPresets.length}</div>
      
      <button 
        data-testid="set-query" 
        onClick={() => actions.setQuery('test query')}
      >
        Set Query
      </button>
      <button 
        data-testid="set-type" 
        onClick={() => actions.setSearchType('cards')}
      >
        Set Type
      </button>
      <button 
        data-testid="set-loading" 
        onClick={() => actions.setLoading(true)}
      >
        Set Loading
      </button>
      <button 
        data-testid="set-error" 
        onClick={() => actions.setError('Test error')}
      >
        Set Error
      </button>
      <button 
        data-testid="add-history" 
        onClick={() => actions.addToHistory({
          query: 'history query',
          searchType: 'decks',
          filters: {},
          resultCount: 5
        })}
      >
        Add History
      </button>
      <button 
        data-testid="save-preset" 
        onClick={() => actions.saveFilterPreset({
          name: 'Test Preset',
          searchType: 'decks',
          filters: { isPublic: true }
        })}
      >
        Save Preset
      </button>
      <button 
        data-testid="reset" 
        onClick={() => actions.resetSearch()}
      >
        Reset
      </button>
    </div>
  );
};

const renderWithProviders = (component) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <SearchProvider>
          {component}
        </SearchProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('SearchContext', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  test('provides initial state', () => {
    renderWithProviders(<TestComponent />);
    
    expect(screen.getByTestId('query')).toHaveTextContent('');
    expect(screen.getByTestId('search-type')).toHaveTextContent('decks');
    expect(screen.getByTestId('loading')).toHaveTextContent('false');
    expect(screen.getByTestId('error')).toHaveTextContent('no-error');
    expect(screen.getByTestId('history-count')).toHaveTextContent('0');
    expect(screen.getByTestId('presets-count')).toHaveTextContent('0');
  });

  test('updates query state', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('set-query'));
    
    expect(screen.getByTestId('query')).toHaveTextContent('test query');
  });

  test('updates search type state', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('set-type'));
    
    expect(screen.getByTestId('search-type')).toHaveTextContent('cards');
  });

  test('updates loading state', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('set-loading'));
    
    expect(screen.getByTestId('loading')).toHaveTextContent('true');
  });

  test('updates error state', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('set-error'));
    
    expect(screen.getByTestId('error')).toHaveTextContent('Test error');
  });

  test('adds search history', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('add-history'));
    
    expect(screen.getByTestId('history-count')).toHaveTextContent('1');
  });

  test('saves filter preset', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('save-preset'));
    
    expect(screen.getByTestId('presets-count')).toHaveTextContent('1');
  });

  test('resets search state', () => {
    renderWithProviders(<TestComponent />);
    
    // Set some state
    fireEvent.click(screen.getByTestId('set-query'));
    fireEvent.click(screen.getByTestId('set-type'));
    fireEvent.click(screen.getByTestId('set-loading'));
    fireEvent.click(screen.getByTestId('set-error'));
    
    // Reset
    fireEvent.click(screen.getByTestId('reset'));
    
    expect(screen.getByTestId('query')).toHaveTextContent('');
    expect(screen.getByTestId('search-type')).toHaveTextContent('decks');
    expect(screen.getByTestId('loading')).toHaveTextContent('false');
    expect(screen.getByTestId('error')).toHaveTextContent('no-error');
  });

  test('prevents duplicate search history entries', () => {
    renderWithProviders(<TestComponent />);
    
    // Add same history item twice
    fireEvent.click(screen.getByTestId('add-history'));
    fireEvent.click(screen.getByTestId('add-history'));
    
    // Should only have one entry
    expect(screen.getByTestId('history-count')).toHaveTextContent('1');
  });

  test('limits search history to 50 entries', () => {
    const TestComponentWithManyHistory = () => {
      const { state, actions } = useSearch();
      
      const addManyHistory = () => {
        for (let i = 0; i < 60; i++) {
          actions.addToHistory({
            query: `query ${i}`,
            searchType: 'decks',
            filters: {},
            resultCount: i
          });
        }
      };
      
      return (
        <div>
          <div data-testid="history-count">{state.searchHistory.length}</div>
          <button data-testid="add-many" onClick={addManyHistory}>
            Add Many
          </button>
        </div>
      );
    };

    renderWithProviders(<TestComponentWithManyHistory />);
    
    fireEvent.click(screen.getByTestId('add-many'));
    
    // Should be limited to 50
    expect(screen.getByTestId('history-count')).toHaveTextContent('50');
  });

  test('persists state to localStorage', async () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('add-history'));
    fireEvent.click(screen.getByTestId('save-preset'));
    
    await waitFor(() => {
      const savedHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
      const savedPresets = JSON.parse(localStorage.getItem('filterPresets') || '[]');
      
      expect(savedHistory).toHaveLength(1);
      expect(savedPresets).toHaveLength(1);
    });
  });

  test('loads state from localStorage on mount', () => {
    // Pre-populate localStorage
    const mockHistory = [{
      query: 'saved query',
      searchType: 'decks',
      filters: {},
      timestamp: new Date().toISOString(),
      resultCount: 3
    }];
    
    const mockPresets = [{
      id: '123',
      name: 'Saved Preset',
      searchType: 'decks',
      filters: { isPublic: true },
      createdAt: new Date().toISOString()
    }];
    
    localStorage.setItem('searchHistory', JSON.stringify(mockHistory));
    localStorage.setItem('filterPresets', JSON.stringify(mockPresets));
    
    renderWithProviders(<TestComponent />);
    
    // Note: The actual loading happens in useEffect, so we might need to wait
    // This is a simplified test - in practice, you might need to mock the localStorage
    // or use a more sophisticated testing approach
  });

  test('handles localStorage errors gracefully', () => {
    // Mock localStorage to throw an error
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = jest.fn(() => {
      throw new Error('localStorage error');
    });
    
    // Should not crash
    expect(() => {
      renderWithProviders(<TestComponent />);
      fireEvent.click(screen.getByTestId('add-history'));
    }).not.toThrow();
    
    // Restore original localStorage
    localStorage.setItem = originalSetItem;
  });

  test('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const originalError = console.error;
    console.error = jest.fn();
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useSearch must be used within a SearchProvider');
    
    console.error = originalError;
  });
});
