<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a8325347-44e3-4b29-a545-5271d05e5c24" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/logs/studycards-performance.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/studycards-performance.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/studycards-security.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/studycards-security.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/studycards.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/studycards.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/config/CacheConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/config/CacheConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/config/SpacedRepetitionConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/config/SpacedRepetitionConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/config/WebSecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/config/WebSecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/controller/CardTagController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/controller/CardTagController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/controller/CollaborationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/controller/CollaborationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/controller/DeckController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/controller/DeckController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/controller/DiscoveryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/controller/DiscoveryController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/controller/StudySessionController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/controller/StudySessionController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/dto/CardPerformanceRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/dto/CardPerformanceRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/dto/DeckSearchRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/dto/DeckSearchRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/dto/EnhancedCardPerformanceRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/dto/EnhancedCardPerformanceRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/Annotation.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/Annotation.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/BaseEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/BaseEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/Card.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/Card.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/CardPerformanceHistory.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/CardPerformanceHistory.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/CardTag.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/CardTag.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/CollaborationActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/CollaborationActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/CollaborationInvitation.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/CollaborationInvitation.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/Deck.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/Deck.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/DeckCollaborator.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/DeckCollaborator.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/DeckTag.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/DeckTag.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/ExamPerformanceHistory.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/ExamPerformanceHistory.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/Notification.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/Notification.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/NotificationPreferences.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/NotificationPreferences.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/PasswordHistory.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/PasswordHistory.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/PaymentTransaction.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/PaymentTransaction.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/StudySession.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/StudySession.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/SubscriptionPlan.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/SubscriptionPlan.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/User.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/model/UserPreferences.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/model/UserPreferences.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/repository/CardPerformanceHistoryRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/repository/CardPerformanceHistoryRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/repository/CardRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/repository/CardRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/repository/DeckRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/repository/DeckRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/repository/DeckRepositoryCustomImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/repository/DeckRepositoryCustomImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/repository/SearchHistoryRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/repository/SearchHistoryRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/repository/StudySessionRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/repository/StudySessionRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/security/OAuth2AuthenticationSuccessHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/security/OAuth2AuthenticationSuccessHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/AnalyticsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/AnalyticsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/AuditLoggingService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/AuditLoggingService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/CardService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/CardService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/ContentVisibilityService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/ContentVisibilityService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/DashboardService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/DashboardService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckAuthorizationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckAuthorizationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckCardOperationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckCardOperationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckDiscoveryService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckDiscoveryService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/DeckService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/EnhancedAnalyticsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/EnhancedAnalyticsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/ImprovedAnalyticsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/ImprovedAnalyticsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/PublicStatisticsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/PublicStatisticsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/ReportsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/ReportsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/SearchPerformanceService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/SearchPerformanceService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/SearchService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/SearchService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/SpacedRepetitionService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/SpacedRepetitionService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/StudySessionService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/StudySessionService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/SubscriptionStatusService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/SubscriptionStatusService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/service/UserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/service/UserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/util/PaginationUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/util/PaginationUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/studycards/validation/ProductionValidationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/studycards/validation/ProductionValidationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/templates/email-verification.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/templates/email-verification.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/templates/password-change-notification.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/templates/password-change-notification.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/studycards/integration/MobileStudyIntegrationTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/studycards/integration/MobileStudyIntegrationTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/studycards/integration/ValidationIntegrationTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/studycards/integration/ValidationIntegrationTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/studycards/service/MobileStudyServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/studycards/service/MobileStudyServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/studycards/service/ResponseMappingServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/studycards/service/ResponseMappingServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/studycards/service/SpacedRepetitionServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/studycards/service/SpacedRepetitionServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/studycards/service/UnifiedScoringServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/studycards/service/UnifiedScoringServiceTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/App.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/App.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/api/axios.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/api/axios.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/api/search.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/api/search.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/api/study.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/api/study.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/AdvancedAnalyticsDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/AdvancedAnalyticsDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/AdvancedSearchForm.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/AdvancedSearchForm.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/AnimatedFeedback.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/AnimatedFeedback.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/CsvImporter.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/CsvImporter.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/EnhancedDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/EnhancedDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/EnhancedFlashCard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/EnhancedFlashCard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/EnhancedProgressTracker.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/EnhancedProgressTracker.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/EnhancedStudySession.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/EnhancedStudySession.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/FeaturesSection.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/FeaturesSection.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/FlashCard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/FlashCard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/HeroSection.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/HeroSection.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/ImprovedAnalyticsDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/ImprovedAnalyticsDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/Layout.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/Layout.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/LoadingScreen.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/LoadingScreen.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/MobileDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/MobileDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/MobileOptimizedFlashCard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/MobileOptimizedFlashCard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/Pagination.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/Pagination.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/PasswordStrengthMeter.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/PasswordStrengthMeter.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/ProgressiveImage.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/ProgressiveImage.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/ProtectedRoute.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/ProtectedRoute.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/QuestionTypes/RichShortAnswer.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/QuestionTypes/RichShortAnswer.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/SearchAutocomplete.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/SearchAutocomplete.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/SearchInput.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/SearchInput.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/SearchPerformanceMonitor.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/SearchPerformanceMonitor.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/SessionDurationSelector.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/SessionDurationSelector.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/SessionsTable.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/SessionsTable.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/StudyCalendar.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/StudyCalendar.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/TextArea.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/TextArea.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/ThemeToggle.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/ThemeToggle.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/analytics/DeckPerformanceCard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/analytics/DeckPerformanceCard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/study/StatisticsDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/study/StatisticsDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/study/SyncManager.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/study/SyncManager.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/study/statistics/LearningProgressCard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/study/statistics/LearningProgressCard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/components/study/statistics/RecommendationsCard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/components/study/statistics/RecommendationsCard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/config/validationConfig.js" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/config/validationConfig.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/contexts/AnimationContext.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/contexts/AnimationContext.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/contexts/AuthContext.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/contexts/AuthContext.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/contexts/ThemeContext.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/contexts/ThemeContext.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/main.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/main.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/CollaborationDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/CollaborationDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/Dashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/Dashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/DeckDetail.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/DeckDetail.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/DeckDiscovery.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/DeckDiscovery.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/DeckList.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/DeckList.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/DeckSearch.jsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/ForgotPassword.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/ForgotPassword.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/LandingPage.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/LandingPage.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/Login.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/Login.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/NotFound.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/NotFound.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/OAuth2RedirectHandler.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/OAuth2RedirectHandler.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/PaymentMethods.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/PaymentMethods.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/Register.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/Register.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/ResetPassword.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/ResetPassword.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/SpacedRepetitionStudy.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/SpacedRepetitionStudy.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/StudyHistory.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/StudyHistory.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/StudySession.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/StudySession.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/UnifiedAnalytics.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/UnifiedAnalytics.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/pages/UnifiedSearch.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/pages/UnifiedSearch.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/utils/csrfProtection.js" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/utils/csrfProtection.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/utils/enums.js" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/utils/enums.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/utils/passwordUtils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/utils/passwordUtils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/src/utils/subscriptionValidator.js" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/src/utils/subscriptionValidator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../frontend/vite.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/../frontend/vite.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zVt4gVvQpszLMZ9dq5AEufPFjy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.StudycardsApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Maven.study-cards-app [clean].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;Fix/invoicespdf&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/studyCards/backend&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="StudycardsApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.studycards.StudycardsApplication" />
      <module name="study-cards-app" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.studycards.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.StudycardsApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a8325347-44e3-4b29-a545-5271d05e5c24" name="Changes" comment="" />
      <created>1751827879303</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751827879303</updated>
    </task>
    <servers />
  </component>
</project>