-- Migration script to fix card performance history constraints
-- Version: 1002
-- Description: Add unique constraint to prevent duplicate performance entries and improve error handling

-- Add unique constraint to prevent duplicate performance entries for the same card in the same session
-- This will prevent the transaction commit failures we've been seeing
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'uk_card_performance_session_card_user')
BEGIN
    CREATE UNIQUE INDEX uk_card_performance_session_card_user 
    ON card_performance_history (study_session_id, card_id, user_id)
    WHERE study_session_id IS NOT NULL;
END;

-- Add index for performance queries without session (for standalone card reviews)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_card_performance_no_session')
BEGIN
    CREATE INDEX idx_card_performance_no_session 
    ON card_performance_history (card_id, user_id, review_date DESC)
    WHERE study_session_id IS NULL;
END;

-- Add constraint to ensure review_date is not in the future (prevent data integrity issues)
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'chk_review_date_not_future')
BEGIN
    ALTER TABLE card_performance_history 
    ADD CONSTRAINT chk_review_date_not_future 
    CHECK (review_date <= GETDATE());
END;

-- Add constraint to ensure notes field doesn't exceed database column limit
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'chk_notes_length')
BEGIN
    ALTER TABLE card_performance_history 
    ADD CONSTRAINT chk_notes_length 
    CHECK (notes IS NULL OR LEN(notes) <= 500);
END;

-- Add constraint to ensure error_type field doesn't exceed database column limit
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'chk_error_type_length')
BEGIN
    ALTER TABLE card_performance_history 
    ADD CONSTRAINT chk_error_type_length 
    CHECK (error_type IS NULL OR LEN(error_type) <= 50);
END;

-- Add constraint to ensure answer_quality field doesn't exceed database column limit
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'chk_answer_quality_length')
BEGIN
    ALTER TABLE card_performance_history 
    ADD CONSTRAINT chk_answer_quality_length 
    CHECK (answer_quality IS NULL OR LEN(answer_quality) <= 50);
END;

-- Update statistics for better query performance after adding constraints
UPDATE STATISTICS card_performance_history;

-- Add comment to document the purpose of this migration
EXEC sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'Unique constraint to prevent duplicate performance entries for the same card in the same session',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'card_performance_history',
    @level2type = N'INDEX', @level2name = N'uk_card_performance_session_card_user';
