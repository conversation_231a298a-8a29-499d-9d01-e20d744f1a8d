# Ease Factor Validation Fix

## Problem
The application was throwing a `ConstraintViolationException` when recording card performance:
```
ConstraintViolationImpl{interpolatedMessage='Ease factor must be at least 1.3', propertyPath=easeFactor, rootBeanClass=class com.studycards.model.Card, messageTemplate='Ease factor must be at least 1.3'}
```

## Root Cause
1. **Type Mismatch in ProductionValidationService**: The `ProductionValidationService.validateCard()` method was trying to assign a `BigDecimal` to a `float` variable and then calling `setEaseFactor()` with a `float` instead of `BigDecimal`.

2. **Floating Point Precision Issues**: When calculating ease factor reductions for poor performance (quality 1-2), the algorithm could calculate values below 1.3, but floating-point precision errors during BigDecimal conversion could cause the value to be slightly below the minimum constraint.

## Fixes Applied

### 1. Fixed ProductionValidationService.java
**Before:**
```java
if (card.getEaseFactor() != 0.0f) {
    float currentEaseFactor = card.getEaseFactor(); // Type mismatch!
    float validatedEaseFactor = Math.max(1.3f, Math.min(5.0f, currentEaseFactor));
    card.setEaseFactor(validatedEaseFactor); // Wrong type!
} else {
    card.setEaseFactor(2.5f); // Wrong type!
}
```

**After:**
```java
if (card.getEaseFactor() != null && card.getEaseFactor().compareTo(BigDecimal.ZERO) != 0) {
    BigDecimal currentEaseFactor = card.getEaseFactor();
    BigDecimal validatedEaseFactor = currentEaseFactor
        .max(BigDecimal.valueOf(1.3))
        .min(BigDecimal.valueOf(5.0));
    card.setEaseFactor(validatedEaseFactor);
} else {
    card.setEaseFactor(BigDecimal.valueOf(2.5)); // Default value
}
```

### 2. Added Safety Check in SpacedRepetitionService.java
Added an additional safety check when setting ease factor to prevent constraint violations:

```java
// Ensure BigDecimal conversion respects minimum constraint to prevent validation errors
BigDecimal easeFactorBigDecimal = BigDecimal.valueOf(newEaseFactor);
if (easeFactorBigDecimal.compareTo(BigDecimal.valueOf(config.getMinEaseFactor())) < 0) {
    logger.warn("Ease factor {} below minimum {}, setting to minimum for card {}", 
               easeFactorBigDecimal, config.getMinEaseFactor(), card.getId());
    easeFactorBigDecimal = BigDecimal.valueOf(config.getMinEaseFactor());
}
card.setEaseFactor(easeFactorBigDecimal);
```

### 3. Fixed Test Files
Updated test files to use `BigDecimal.valueOf()` instead of float literals when setting ease factors:
- `SpacedRepetitionServiceTest.java`
- `ResponseMappingServiceTest.java`

## Testing the Fix
To test that the fix works:

1. Start the backend server
2. Create a study session with cards
3. Rate cards with very poor performance (rating 1-2) multiple times
4. Verify that no constraint violation exceptions occur
5. Check that ease factors are properly clamped to minimum values

## Configuration
The ease factor constraints are defined in:
- **Minimum**: 1.3 (configured in `application.properties`)
- **Maximum**: 2.5 (configured in `application.properties`)
- **Default**: 2.5 (for new cards)

The algorithm now properly handles edge cases where calculated ease factors would fall below the minimum constraint.
