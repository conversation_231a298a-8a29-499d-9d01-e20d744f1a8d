com\studycards\dto\DeckHierarchyResponse.class
com\studycards\service\TransactionManagementService.class
com\studycards\model\CardPerformanceHistory.class
com\studycards\service\CollaborationService.class
com\studycards\service\MobileStudyCacheService$CacheStats.class
com\studycards\dto\paymob\PaymobAuthRequest.class
com\studycards\dto\AdaptiveLearningPath$AdaptiveLearningPathBuilder.class
com\studycards\dto\ExamHistoryResponse$ExamConfiguration$ExamConfigurationBuilder.class
com\studycards\dto\BatchCardCreateRequest.class
com\studycards\service\AuditService.class
com\studycards\config\ProductionCacheConfig.class
com\studycards\dto\StudySessionsPageResponse$StudyAggregation$StudyAggregationBuilder.class
com\studycards\circuitbreaker\FallbackStrategy$FallbackStrategyBuilder.class
com\studycards\dto\ScoringResult$ScoringResultBuilder.class
com\studycards\exception\InvalidCaptchaException.class
com\studycards\dto\BulkPermissionUpdateRequest$PermissionUpdate$PermissionUpdateBuilder.class
com\studycards\dto\LearningEfficiencyMetrics$LearningEfficiencyMetricsBuilder.class
com\studycards\dto\CardResponse$CardResponseBuilder.class
com\studycards\dto\CardValidationRequest$CardValidationRequestBuilder.class
com\studycards\dto\BulkReviewResponse$CardUpdateResult.class
com\studycards\dto\BulkReviewResponse$ReviewError$ReviewErrorBuilder.class
com\studycards\dto\CollaborationTrendItem$CollaborationTrendItemBuilder.class
com\studycards\dto\PaymentDataValidationResponse$PaymentDataValidationResponseBuilder.class
com\studycards\dto\UserProgressResponse$OverallProgress$OverallProgressBuilder.class
com\studycards\model\SubscriptionPlan.class
com\studycards\dto\NotificationPreferencesRequest.class
com\studycards\exception\DataIntegrityException.class
com\studycards\service\PasswordGenerationService$1.class
com\studycards\entity\AnswerReport.class
com\studycards\dto\DeckPerformanceResponse$MasteredCard$MasteredCardBuilder.class
com\studycards\dto\SemanticPerformanceMetrics$SemanticPerformanceMetricsBuilder.class
com\studycards\service\CaptchaService.class
com\studycards\dto\UserProgressResponse.class
com\studycards\controller\AnswerComparisonMetricsController.class
com\studycards\dto\SemanticComparisonResponse$SemanticComparisonResponseBuilder.class
com\studycards\repository\AuditLogRepository.class
com\studycards\dto\BatchCardOperationRequest$CardToCreate$CardToCreateBuilder.class
com\studycards\dto\StudySessionAccessValidationResponse.class
com\studycards\dto\BulkRemoveCollaboratorsRequest$BulkRemoveCollaboratorsRequestBuilder.class
com\studycards\dto\PaymentRequest.class
com\studycards\service\SearchService.class
com\studycards\repository\DeckRepository.class
com\studycards\config\ValidationConfig$ScoringConfig$ExamScoringConfig.class
com\studycards\monitoring\AlertingStatus.class
com\studycards\dto\BulkReviewResponse$ReviewError.class
com\studycards\config\RecommendationConfig.class
com\studycards\service\AnalyticsService$AnalyticsEvent.class
com\studycards\validation\EnumValidator.class
com\studycards\service\TokenBlacklistService.class
com\studycards\converter\QuestionTypeConverter.class
com\studycards\dto\PublicStatisticsResponse$ContentStatistics$ContentStatisticsBuilder.class
com\studycards\event\StudySessionStartedEvent$StudySessionStartedEventBuilder.class
com\studycards\service\UserLearningProfileService.class
com\studycards\dto\StudyStatisticsResponse$DeckPerformance.class
com\studycards\security\AuthEntryPointJwt.class
com\studycards\service\SubscriptionStatusService$1.class
com\studycards\event\CacheInvalidationEventListener.class
com\studycards\controller\UserController.class
com\studycards\service\UserService.class
com\studycards\controller\CaptchaController.class
com\studycards\model\Notification$NotificationType.class
com\studycards\dto\CollaborationExportResponse$CollaborationExportResponseBuilder.class
com\studycards\dto\DeckPerformanceResponse$ComparisonMetrics$ComparisonMetricsBuilder.class
com\studycards\dto\paymob\PaymobPaymentKeyResponse.class
com\studycards\dto\EnhancedExamSessionRequest.class
com\studycards\dto\ExamAnalyticsData.class
com\studycards\exception\ConcurrentOperationException.class
com\studycards\dto\SemanticAnalysisDetails.class
com\studycards\config\ValidationConfig$BusinessRulesConfig.class
com\studycards\dto\CognitiveLoadAnalysis.class
com\studycards\service\SpacedRepetitionService.class
com\studycards\dto\StudySchedulePrediction.class
com\studycards\dto\EnhancedDeckResponse$StudyStatistics$StudyStatisticsBuilder.class
com\studycards\service\DeckMappingContext.class
com\studycards\service\StudyCardsMonitoringService.class
com\studycards\controller\ReportsController.class
com\studycards\dto\DeckPerformanceResponse$MasteredCard.class
com\studycards\dto\CardDeletionResponse$CardDeletionResponseBuilder.class
com\studycards\dto\UpdatePermissionRequest.class
com\studycards\dto\StudyRecommendation$StudyRecommendationBuilder.class
com\studycards\dto\EmailVerificationRequest.class
com\studycards\controller\CardTagController$RemoveTagRequest.class
com\studycards\dto\MobileStudySessionRequest$MobileStudySessionRequestBuilder.class
com\studycards\event\StudySessionStartedEvent.class
com\studycards\service\DeckValidationService$1.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobShippingData.class
com\studycards\dto\DeckPerformanceResponse$RecommendedAction$RecommendedActionBuilder.class
com\studycards\service\AdaptiveTestingService$QuestionParameters$QuestionParametersBuilder.class
com\studycards\dto\paymob\PaymobPaymentKeyRequest$PaymobBillingData.class
com\studycards\model\BillingTransaction.class
com\studycards\dto\NotificationPreferencesResponse.class
com\studycards\dto\StudyStatisticsResponse$OverallStatistics$OverallStatisticsBuilder.class
com\studycards\circuitbreaker\CircuitBreakerTestResult$CircuitBreakerTestResultBuilder.class
com\studycards\dto\AdaptiveTestingRequest.class
com\studycards\dto\BatchCardOperationRequest.class
com\studycards\controller\SearchPerformanceController.class
com\studycards\dto\LearningStyleProfile$LearningStyleProfileBuilder.class
com\studycards\dto\UserActivityResponse$UserActivityResponseBuilder.class
com\studycards\validation\EnumValidatorImpl.class
com\studycards\service\CollaborationService$1.class
com\studycards\service\EmailVerificationReminderService$VerificationReminderStats$VerificationReminderStatsBuilder.class
com\studycards\exception\AnswerComparisonException.class
com\studycards\dto\PermissionMatrixResponse$PermissionMatrixResponseBuilder.class
com\studycards\dto\LoginRequest$LoginRequestBuilder.class
com\studycards\dto\PublicStatisticsResponse$UserStatistics$UserStatisticsBuilder.class
com\studycards\dto\SemanticPerformanceMetrics$PerformanceDataPoint$PerformanceDataPointBuilder.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobOrderRequestBuilder.class
com\studycards\dto\AnswerEvaluationReportRequest.class
com\studycards\dto\StudySessionStartResponse$StudySessionMetadata$StudySessionMetadataBuilder.class
com\studycards\dto\AIGradingResponse$AIGradingResponseBuilder.class
com\studycards\dto\SemanticComparisonResponse.class
com\studycards\model\DeckCollaborator.class
com\studycards\service\DeckRecommendationService.class
com\studycards\config\ValidationConfig$ErrorMessages.class
com\studycards\dto\CollaboratorRequest$CollaboratorRequestBuilder.class
com\studycards\service\StudySessionServiceExtension.class
com\studycards\dto\ExamConfigRecommendation.class
com\studycards\repository\SecurityAuditLogRepository.class
com\studycards\dto\InvitationItem$InvitationItemBuilder.class
com\studycards\security\UserDetailsImpl.class
com\studycards\dto\CollaborationActivityItem$CollaborationActivityItemBuilder.class
com\studycards\monitoring\WebhookPayload.class
com\studycards\dto\StudySessionsPageResponse$StudyRecommendation$StudyRecommendationBuilder.class
com\studycards\dto\CardDeletionResponse.class
com\studycards\config\ValidationConfig$ErrorMessages$Security.class
com\studycards\dto\BulkPermissionUpdateRequest.class
com\studycards\service\PerformanceMonitoringService$OperationTracker.class
com\studycards\model\CardPerformanceHistory$CardPerformanceHistoryBuilder.class
com\studycards\dto\BillingStatusMetadata.class
com\studycards\controller\PublicStatisticsController.class
com\studycards\dto\EnhancedExamSessionResponse.class
com\studycards\dto\ErrorResponse.class
com\studycards\dto\PasswordGenerationResponse$PasswordMetadata.class
com\studycards\dto\StudyStatisticsResponse$ProgressPoint$ProgressPointBuilder.class
com\studycards\service\PasswordGenerationService.class
com\studycards\dto\BulkReviewResponse$CardUpdateResult$CardUpdateResultBuilder.class
com\studycards\model\CollaborationActivity.class
com\studycards\repository\NotificationPreferencesRepository.class
com\studycards\service\PerformanceMonitoringService.class
com\studycards\dto\PublicStatisticsResponse$ContentStatistics.class
com\studycards\dto\AIGradingRequest.class
com\studycards\model\Notification$NotificationBuilder.class
com\studycards\service\SearchPerformanceService$SearchPerformanceMetrics.class
com\studycards\service\UserBlockService.class
com\studycards\dto\CardOwnershipValidationResponse.class
com\studycards\dto\MotivationFactors$MotivationFactorsBuilder.class
com\studycards\dto\AnswerReportRequest$AnswerReportRequestBuilder.class
com\studycards\dto\UserActivityResponse$UserStatistics.class
com\studycards\controller\AuditController.class
com\studycards\dto\StudyStatisticsResponse$StudyPatterns$StudyPatternsBuilder.class
com\studycards\controller\ExamTemplateController.class
com\studycards\dto\SemanticAnalysisResponse.class
com\studycards\dto\SharedDecksResponse$SharedDeckItem.class
com\studycards\exception\ValidationException.class
com\studycards\dto\CollaborationActivityItem.class
com\studycards\service\StudyRecommendationService.class
com\studycards\config\ValidationConfig$BusinessRulesConfig$AuditConfig.class
com\studycards\dto\paymob\PaymobAuthRequest$PaymobAuthRequestBuilder.class
com\studycards\dto\paymob\PaymobPaymentKeyRequest$PaymobPaymentKeyRequestBuilder.class
com\studycards\model\Card$CardBuilder.class
com\studycards\dto\QuickSearchResponse.class
com\studycards\dto\RiskAnalysis$RiskAnalysisBuilder.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobShippingDetails.class
com\studycards\service\AlertingService.class
com\studycards\dto\ConceptAnalysis$LearningPattern$LearningPatternBuilder.class
com\studycards\dto\PaymentRequest$PaymentRequestBuilder.class
com\studycards\dto\UserSummary$UserSummaryBuilder.class
com\studycards\dto\RealTimeInsights$RealTimeInsightsBuilder.class
com\studycards\config\WebMvcConfig.class
com\studycards\converter\BillingStatusConverter.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobOrderItem$PaymobOrderItemBuilder.class
com\studycards\dto\QuestionTypeMetadata$QuestionTypeMetadataBuilder.class
com\studycards\exception\SecurityException.class
com\studycards\repository\ExamPerformanceHistoryRepository.class
com\studycards\exception\AuthenticationException.class
com\studycards\controller\StudySessionController.class
com\studycards\dto\DeckSearchRequest$DeckSearchRequestBuilder.class
com\studycards\exception\SubscriptionLimitException.class
com\studycards\dto\EnhancedExamSessionResponse$LearningRecommendation.class
com\studycards\monitoring\AlertRateLimiter.class
com\studycards\service\PasswordSecurityService$PasswordValidationResult.class
com\studycards\controller\CollaborationManagementController.class
com\studycards\entity\CardHint$CardHintBuilder.class
com\studycards\dto\CreatePaymentIntentRequest.class
com\studycards\dto\QuestionTypeMetadata.class
com\studycards\dto\ReviewCardsResponse$ReviewCardsResponseBuilder.class
com\studycards\dto\UserSearchResponse.class
com\studycards\controller\NotificationController.class
com\studycards\service\SearchCacheService.class
com\studycards\service\NotificationPreferencesService$NotificationPreferencesStats.class
com\studycards\dto\DeckRequest$InitialCardRequest$InitialCardRequestBuilder.class
com\studycards\dto\AnswerFeedbackResponse.class
com\studycards\service\AdaptiveTestingService.class
com\studycards\type\JsonNodeType.class
com\studycards\dto\JwtResponse$JwtResponseBuilder.class
com\studycards\dto\CollaborationInsightsResponse.class
com\studycards\config\SpacedRepetitionConfig.class
com\studycards\dto\StudySessionFilterRequest$StudySessionFilterRequestBuilder.class
com\studycards\dto\RegistrationResponse.class
com\studycards\dto\StrategyRecommendation.class
com\studycards\config\ValidationConfig$ExamConfig.class
com\studycards\dto\BulkUpdateResponse$BulkUpdateResult.class
com\studycards\dto\AutoSaveDraftRequest.class
com\studycards\monitoring\MonitoringStatus$MonitoringStatusBuilder.class
com\studycards\model\StudySession.class
com\studycards\dto\AdaptiveLearningPath.class
com\studycards\config\RateLimitingConfig.class
com\studycards\dto\JwtResponse.class
com\studycards\util\PaginationUtil$ValidationResult.class
com\studycards\dto\DeckPerformanceResponse$PerformancePoint.class
com\studycards\model\ExamTemplate$ExamTemplateBuilder.class
com\studycards\dto\NotificationPreferencesRequest$NotificationPreferencesRequestBuilder.class
com\studycards\repository\CardTagRepository.class
com\studycards\event\DeckCreatedEvent$DeckCreatedEventBuilder.class
com\studycards\dto\BulkUpdateResponse.class
com\studycards\dto\EnhancedDeckResponse$EnhancedDeckResponseBuilder.class
com\studycards\utils\EnumValidationUtils.class
com\studycards\dto\paymob\PaymobAuthResponse$PaymobProfile$PaymobProfileBuilder.class
com\studycards\controller\DeckController$1.class
com\studycards\model\StudySession$StudySessionBuilder.class
com\studycards\dto\BulkUpdateResponse$BulkUpdateResponseBuilder.class
com\studycards\dto\PaymentIntentResponse$PaymentIntentResponseBuilder.class
com\studycards\util\CircuitBreaker$CircuitBreakerOpenException.class
com\studycards\dto\EnhancedDeckResponse$StudyStatistics.class
com\studycards\dto\CompleteStudySessionRequest$CompleteStudySessionRequestBuilder.class
com\studycards\circuitbreaker\CircuitBreakerDashboard$CircuitBreakerDashboardBuilder.class
com\studycards\service\DeckService$1.class
com\studycards\model\SecurityAuditLog.class
com\studycards\dto\DeckPerformanceResponse$OverallPerformance.class
com\studycards\config\MobileStudyCacheConfig.class
com\studycards\dto\PublicStatisticsResponse.class
com\studycards\dto\ExamPerformanceMetrics$ExamPerformanceMetricsBuilder.class
com\studycards\circuitbreaker\CircuitBreakerInfo.class
com\studycards\dto\CollaborationTemplateResponse.class
com\studycards\dto\StudySessionsPageResponse$StudyAggregation.class
com\studycards\i18n\EnumMessageService.class
com\studycards\dto\UserResponse$UserResponseBuilder.class
com\studycards\service\UserPreferencesService.class
com\studycards\dto\QuickSearchResponse$QuickSearchResponseBuilder.class
com\studycards\dto\LearningStyleProfile.class
com\studycards\config\ValidationConfig$AnswerConfig.class
com\studycards\dto\DashboardResponse.class
com\studycards\dto\EnhancedInvitationDetails.class
com\studycards\dto\PasswordGenerationResponse$PasswordStrengthInfo.class
com\studycards\model\AnswerEvaluationReport.class
com\studycards\service\AuthService.class
com\studycards\dto\PasswordGenerationResponse$PasswordGenerationResponseBuilder.class
com\studycards\model\Deck$DeckBuilder.class
com\studycards\repository\PasswordHistoryRepository.class
com\studycards\controller\CardTagController.class
com\studycards\event\CacheInvalidationEvent$SubscriptionChangedEvent.class
com\studycards\monitoring\AlertingStatus$AlertingStatusBuilder.class
com\studycards\service\EmailService.class
com\studycards\model\PasswordHistory.class
com\studycards\service\ErrorMessageService.class
com\studycards\dto\StudyStatisticsResponse.class
com\studycards\controller\AdaptiveExamController.class
com\studycards\dto\ErrorPatternAnalysis$ErrorPatternAnalysisBuilder.class
com\studycards\dto\LearningProgressionAnalysis.class
com\studycards\enums\QuestionType.class
com\studycards\dto\ShareDeckResponse.class
com\studycards\dto\EnhancedInvitationDetails$EnhancedInvitationDetailsBuilder.class
com\studycards\dto\StudySchedulePrediction$StudySchedulePredictionBuilder.class
com\studycards\service\ValidationMonitoringService.class
com\studycards\dto\ApiResponse$ApiResponseBuilder.class
com\studycards\repository\AnswerReportRepository.class
com\studycards\dto\PasswordValidationRequest.class
com\studycards\config\PerformanceConfig.class
com\studycards\dto\StudyActivityResponse.class
com\studycards\dto\EnhancedCardPerformanceRequest$EnhancedCardPerformanceRequestBuilder.class
com\studycards\dto\SemanticInsightsResponse.class
com\studycards\circuitbreaker\CircuitBreakerHealthCheck$CircuitBreakerHealthCheckBuilder.class
com\studycards\dto\ScoringResult.class
com\studycards\circuitbreaker\CircuitBreakerEvent$CircuitBreakerEventBuilder.class
com\studycards\controller\StripeWebhookController.class
com\studycards\dto\CreateCollaborationTemplateRequest$CreateCollaborationTemplateRequestBuilder.class
com\studycards\dto\EnhancedStudySessionResponse.class
com\studycards\entity\UserReport.class
com\studycards\service\NotificationService.class
com\studycards\service\CardHintService.class
com\studycards\dto\CardDeletionResponse$DeletionType.class
com\studycards\dto\StudySessionsPageResponse$StudyRecommendation.class
com\studycards\config\PasswordEncoderConfig.class
com\studycards\dto\MasteryPrediction$MasteryPredictionBuilder.class
com\studycards\dto\SharedDecksResponse$SharedDecksResponseBuilder.class
com\studycards\utils\Utils.class
com\studycards\controller\UserPreferencesController.class
com\studycards\service\SecurityAuditService$SecurityEventType.class
com\studycards\service\AuditLoggingService.class
com\studycards\dto\AdaptiveExamSession$AdaptiveExamSessionBuilder.class
com\studycards\audit\EnumAuditEvent$AuditEventType.class
com\studycards\service\EmailVerificationReminderService.class
com\studycards\dto\ShareDeckRequest$ShareDeckRequestBuilder.class
com\studycards\dto\paymob\PaymobAuthResponse$PaymobProfile.class
com\studycards\dto\EnhancedExamSessionResponse$ExamPerformanceAnalysis$ExamPerformanceAnalysisBuilder.class
com\studycards\entity\CardHint.class
com\studycards\dto\DailyGoalResponse$DailyGoalResponseBuilder.class
com\studycards\dto\BulkUpdateResponse$BulkUpdateResult$BulkUpdateResultBuilder.class
com\studycards\dto\CollaborationTemplateResponse$CollaborationTemplateResponseBuilder.class
com\studycards\security\JwtUtils.class
com\studycards\dto\CollaborationInsightsResponse$CollaborationInsightsResponseBuilder.class
com\studycards\dto\PermissionMatrixResponse$DeckPermissionInfo$DeckPermissionInfoBuilder.class
com\studycards\dto\paymob\PaymobOrderRequest.class
com\studycards\model\AnswerEvaluationReport$AnswerEvaluationReportBuilder.class
com\studycards\enums\ErrorType.class
com\studycards\dto\EnhancedAcceptanceResponse.class
com\studycards\dto\AIGradingResponse.class
com\studycards\dto\StudyStatisticsResponse$LearningProgress.class
com\studycards\event\CardDeletedEvent.class
com\studycards\dto\ExamTemplateRequest.class
com\studycards\dto\UserProfileResponse.class
com\studycards\dto\UserProgressResponse$UserProgressResponseBuilder.class
com\studycards\repository\UserPreferencesRepository.class
com\studycards\dto\StudyActivityResponse$DailyActivity.class
com\studycards\model\Tag.class
com\studycards\repository\DeckRepositoryCustom.class
com\studycards\service\SearchCacheService$CacheStats.class
com\studycards\enums\BillingStatus.class
com\studycards\dto\StrategyRecommendation$StrategyRecommendationBuilder.class
com\studycards\config\ValidationConfig$SecurityConfig.class
com\studycards\dto\SemanticAnalysisRequest$SemanticAnalysisRequestBuilder.class
com\studycards\event\CacheInvalidationEvent$CardChangedEvent.class
com\studycards\service\FraudDetectionService$FraudRiskAssessment.class
com\studycards\dto\ExamSecurityMetrics$ExamSecurityMetricsBuilder.class
com\studycards\monitoring\MonitoringConfig.class
com\studycards\exception\ResourceAlreadyExistsException.class
com\studycards\dto\SemanticInsightsResponse$SemanticInsightsResponseBuilder.class
com\studycards\exception\TokenRefreshException.class
com\studycards\model\BaseEntity.class
com\studycards\monitoring\EnumMonitoringService.class
com\studycards\dto\DeckPerformanceResponse$CardPerformance.class
com\studycards\dto\StudySessionStartResponse$StudySessionStartResponseBuilder.class
com\studycards\dto\BatchCardOperationRequest$CardToUpdate$CardToUpdateBuilder.class
com\studycards\service\ResponseMappingService.class
com\studycards\dto\ApiResponse.class
com\studycards\exception\SubscriptionRequiredException.class
com\studycards\dto\StudySessionStartResponse$SessionConfiguration.class
com\studycards\config\ValidationConfig$BusinessRulesConfig$SubscriptionLimits$SubscriptionLimit.class
com\studycards\dto\DeckResponse$DeckResponseBuilder.class
com\studycards\controller\NotificationPreferencesController.class
com\studycards\dto\PermissionMatrixResponse$DeckPermissionInfo.class
com\studycards\dto\DeckAccessValidationResponse$DeckAccessValidationResponseBuilder.class
com\studycards\controller\AnnotationController.class
com\studycards\dto\NextQuestionRecommendation$NextQuestionRecommendationBuilder.class
com\studycards\dto\EnhancedAcceptanceResponse$EnhancedAcceptanceResponseBuilder.class
com\studycards\dto\DashboardResponse$UpcomingReview$UpcomingReviewBuilder.class
com\studycards\repository\LoginActivityRepository.class
com\studycards\exception\StudyCardsCacheConfigurationException.class
com\studycards\dto\DashboardResponse$RecentDeck$RecentDeckBuilder.class
com\studycards\dto\UpdateUserProfileRequest$UpdateUserProfileRequestBuilder.class
com\studycards\dto\ErrorResponse$ErrorResponseBuilder.class
com\studycards\service\JwtRateLimitService.class
com\studycards\dto\DeckSummaryResponse.class
com\studycards\model\UserPreferences.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobOrderItem.class
com\studycards\enums\AlertSeverity.class
com\studycards\model\SearchHistory.class
com\studycards\dto\SemanticPatternAnalysis$SemanticPatternAnalysisBuilder.class
com\studycards\dto\MobileStudyProgressRequest.class
com\studycards\dto\PasswordGenerationResponse$PasswordMetadata$PasswordMetadataBuilder.class
com\studycards\dto\InvitationListResponse.class
com\studycards\dto\BillingHistoryResponse.class
com\studycards\dto\TopCollaboratorItem.class
com\studycards\dto\BatchCardOperationRequest$CardToUpdate.class
com\studycards\dto\StudySessionResponse.class
com\studycards\service\UnifiedSearchService.class
com\studycards\dto\ApplyTemplateRequest.class
com\studycards\controller\PaymobWebhookController.class
com\studycards\dto\CardRequest.class
com\studycards\util\TextProcessingUtil$SimilarityConfig.class
com\studycards\dto\TopCollaboratorItem$TopCollaboratorItemBuilder.class
com\studycards\model\RefreshToken.class
com\studycards\dto\CardPerformanceResponse.class
com\studycards\dto\UserProgressResponse$ProgressPoint$ProgressPointBuilder.class
com\studycards\service\RateLimitService.class
com\studycards\dto\DeckPerformanceResponse$CardPerformance$CardPerformanceBuilder.class
com\studycards\dto\DeckSummaryResponse$DeckSummaryResponseBuilder.class
com\studycards\dto\PredictiveInsights$PredictiveInsightsBuilder.class
com\studycards\service\ErrorMessageService$ErrorMessageTemplate.class
com\studycards\dto\ExamSessionResponse.class
com\studycards\service\SemanticAnalysisService.class
com\studycards\util\PaginationUtil.class
com\studycards\dto\ExamValidationResult$ExamValidationResultBuilder.class
com\studycards\dto\SemanticPerformanceMetrics.class
com\studycards\dto\AnswerEvaluationReportResponse$AnswerEvaluationReportResponseBuilder.class
com\studycards\StudycardsApplication.class
com\studycards\dto\ExamTemplateResponse$ExamConfiguration$ExamConfigurationBuilder.class
com\studycards\dto\UserBlockResponse.class
com\studycards\entity\AnswerFeedback.class
com\studycards\dto\DashboardResponse$DashboardResponseBuilder.class
com\studycards\dto\ExamSessionRequest.class
com\studycards\dto\UserBlockResponse$UserBlockResponseBuilder.class
com\studycards\monitoring\MonitoringStatus.class
com\studycards\service\EnhancedAnalyticsService.class
com\studycards\dto\AnswerComparisonResult$AnswerComparisonResultBuilder.class
com\studycards\service\MobileStudyRateLimitingService.class
com\studycards\service\RateLimitingService$RateLimitData.class
com\studycards\dto\CaptchaResponse.class
com\studycards\model\Notification.class
com\studycards\dto\PermissionMatrixResponse.class
com\studycards\dto\CollaborationDashboardResponse.class
com\studycards\dto\SubscriptionPlanResponse.class
com\studycards\dto\AdaptiveExamSession.class
com\studycards\dto\TagSummary.class
com\studycards\event\CacheInvalidationEvent$DeckTagChangedEvent.class
com\studycards\dto\AdaptiveExamResponse$AdaptiveExamResponseBuilder.class
com\studycards\dto\LoginRequest.class
com\studycards\repository\SubscriptionPlanRepository.class
com\studycards\dto\EnhancedExamSessionResponse$ExamPerformanceAnalysis.class
com\studycards\dto\EnhancedExamSessionResponse$LearningRecommendation$LearningRecommendationBuilder.class
com\studycards\event\SyncEvent$SyncEventBuilder.class
com\studycards\model\CollaborationInvitation.class
com\studycards\dto\BulkReviewRequest$ReviewItem.class
com\studycards\dto\DashboardResponse$RecentDeck.class
com\studycards\model\ExamTemplate$ExamConfiguration.class
com\studycards\controller\AdminSubscriptionController.class
com\studycards\dto\ExamSessionResponse$ExamSessionResponseBuilder.class
com\studycards\dto\KnowledgeGap.class
com\studycards\config\ValidationConfig$BusinessRulesConfig$SubscriptionLimits.class
com\studycards\dto\paymob\PaymobOrderResponse$PaymobOrderItem.class
com\studycards\dto\CognitiveLoadAnalysis$CognitiveLoadAnalysisBuilder.class
com\studycards\dto\CardPerformanceRequest$CardPerformanceRequestBuilder.class
com\studycards\dto\PasswordGenerationResponse$PasswordStrengthInfo$PasswordStrengthInfoBuilder.class
com\studycards\service\EnhancedNotificationService.class
com\studycards\repository\AnnotationRepository.class
com\studycards\controller\AnswerFeedbackController.class
com\studycards\dto\DeckResponse.class
com\studycards\dto\StudyStatisticsResponse$ProgressPoint.class
com\studycards\service\CollaborationActivityService.class
com\studycards\controller\CollaborationController.class
com\studycards\model\RefreshToken$RefreshTokenBuilder.class
com\studycards\dto\ShareDeckRequest.class
com\studycards\service\PasswordResetService.class
com\studycards\service\SubscriptionValidationService$SubscriptionValidationResult$SubscriptionValidationResultBuilder.class
com\studycards\dto\EnhancedStudySessionResponse$SessionComparison$SessionComparisonBuilder.class
com\studycards\repository\AnswerEvaluationReportRepository.class
com\studycards\service\FileStorageService.class
com\studycards\config\ValidationConfig$ScoringConfig$AnswerComparisonConfig$SimilarityWeights.class
com\studycards\service\SearchPerformanceService$SearchMetrics.class
com\studycards\dto\CaptchaVerificationResponse.class
com\studycards\monitoring\AlertContext$AlertContextBuilder.class
com\studycards\config\JwtProperties.class
com\studycards\dto\ExamCompletionResult.class
com\studycards\repository\NotificationRepository.class
com\studycards\service\SecurityValidationService.class
com\studycards\dto\StudyStatisticsResponse$StudyStatisticsResponseBuilder.class
com\studycards\model\NotificationPreferences$NotificationPreferencesBuilder.class
com\studycards\service\CardService$1.class
com\studycards\service\CaptchaService$CaptchaData.class
com\studycards\service\LoginActivityService.class
com\studycards\dto\ExamPerformanceMetrics.class
com\studycards\dto\DifficultyProgression$DifficultyProgressionBuilder.class
com\studycards\dto\AnswerFeedbackRequest.class
com\studycards\model\User$UserBuilder.class
com\studycards\service\StudyCardsMonitoringService$MetricEntry.class
com\studycards\dto\PaymentIntentResponse.class
com\studycards\dto\PasswordGenerationRequest.class
com\studycards\dto\UserActivityResponse$DeckSummary.class
com\studycards\dto\SubscriptionStatusValidationResponse$SubscriptionStatusValidationResponseBuilder.class
com\studycards\dto\DeckCreationResponse$DeckCreationMetadata.class
com\studycards\dto\paymob\PaymobPaymentKeyResponse$PaymobPaymentKeyResponseBuilder.class
com\studycards\dto\PublicStatisticsResponse$StudyStatistics$StudyStatisticsBuilder.class
com\studycards\dto\SubscriptionResponse.class
com\studycards\enums\BillingInterval.class
com\studycards\service\DeckDiscoveryService.class
com\studycards\service\CollaborationAnalyticsService.class
com\studycards\dto\StudyActivityResponse$StudyActivityResponseBuilder.class
com\studycards\dto\DeckCreationResponse$DeckCreationMetadata$DeckCreationMetadataBuilder.class
com\studycards\dto\ExamAnalyticsData$ExamAnalyticsDataBuilder.class
com\studycards\dto\DeckRequest$DeckRequestBuilder.class
com\studycards\service\MobileStudyRateLimitingService$RateLimitStats.class
com\studycards\service\JwtMetricsService.class
com\studycards\config\MetricsConfig.class
com\studycards\dto\DeckPerformanceResponse$StudyPatterns$StudyPatternsBuilder.class
com\studycards\config\WebSocketConfig.class
com\studycards\dto\BillingHistoryResponse$BillingHistoryResponseBuilder.class
com\studycards\dto\RegistrationValidationResponse.class
com\studycards\service\UserStatisticsService.class
com\studycards\dto\SharedDecksResponse$SharedDeckItem$SharedDeckItemBuilder.class
com\studycards\model\ExamTemplate.class
com\studycards\dto\SemanticPatternAnalysis.class
com\studycards\dto\AdaptiveExamResponse.class
com\studycards\controller\PasswordGenerationController.class
com\studycards\dto\AIInsight$AIInsightBuilder.class
com\studycards\enums\CollaboratorPermission.class
com\studycards\security\UserDetailsServiceImpl.class
com\studycards\circuitbreaker\CircuitBreakerEvent.class
com\studycards\config\StripeConfig.class
com\studycards\event\CardDeletedEvent$CardDeletedEventBuilder.class
com\studycards\circuitbreaker\FallbackType.class
com\studycards\dto\BulkPermissionUpdateRequest$BulkPermissionUpdateRequestBuilder.class
com\studycards\dto\CompleteStudySessionRequest.class
com\studycards\interceptor\SubscriptionInterceptor.class
com\studycards\dto\ExamTemplateResponse.class
com\studycards\service\PerformanceMonitoringService$PerformanceSummary.class
com\studycards\config\CacheConfig.class
com\studycards\dto\AIEnhancedFeedback$AIEnhancedFeedbackBuilder.class
com\studycards\service\AdvancedAnalyticsService.class
com\studycards\controller\OAuth2Controller.class
com\studycards\exception\TokenExpiredException.class
com\studycards\dto\CollaborationInvitationResponse$CollaborationInvitationResponseBuilder.class
com\studycards\circuitbreaker\CircuitBreakerAlertConfig$CircuitBreakerAlertConfigBuilder.class
com\studycards\dto\DeckPerformanceResponse$OverallPerformance$OverallPerformanceBuilder.class
com\studycards\service\AnalyticsService.class
com\studycards\config\ValidationConfig$ScoringConfig$PasswordStrengthConfig$LengthScores.class
com\studycards\dto\paymob\PaymobPaymentKeyRequest$PaymobBillingData$PaymobBillingDataBuilder.class
com\studycards\model\Annotation$AnnotationBuilder.class
com\studycards\dto\MasteryPrediction.class
com\studycards\service\UserReportService.class
com\studycards\event\SyncEvent.class
com\studycards\dto\CollaborationHealthResponse.class
com\studycards\monitoring\AlertingService$1.class
com\studycards\dto\DeckRequest.class
com\studycards\enums\ValidationEventType.class
com\studycards\dto\NotificationPreferencesResponse$NotificationPreferencesResponseBuilder.class
com\studycards\service\StudySessionServiceExtension$1.class
com\studycards\dto\CardRequest$CardRequestBuilder.class
com\studycards\dto\ConceptAnalysis$LearningPattern.class
com\studycards\service\PasswordSecurityService$PasswordRequirements.class
com\studycards\exception\StudyCardsCacheException.class
com\studycards\model\Annotation.class
com\studycards\dto\CollaboratorResponse.class
com\studycards\monitoring\AlertingService.class
com\studycards\service\AdaptiveTestingService$PerformanceMetrics.class
com\studycards\exception\DuplicateDeckException.class
com\studycards\dto\ApplyTemplateRequest$ApplyTemplateRequestBuilder.class
com\studycards\dto\BatchCardOperationRequest$CardToCreate.class
com\studycards\dto\CardHintDto$CardHintDtoBuilder.class
com\studycards\controller\SearchController.class
com\studycards\service\SubscriptionLimitService.class
com\studycards\dto\PasswordResetRequest.class
com\studycards\dto\StudyStatisticsResponse$StudyRecommendation$StudyRecommendationBuilder.class
com\studycards\dto\EnhancedInvitationRequest$EnhancedInvitationRequestBuilder.class
com\studycards\dto\BulkReviewResponse$BulkReviewResponseBuilder.class
com\studycards\security\OAuth2AuthenticationSuccessHandler.class
com\studycards\dto\ForgotPasswordRequest.class
com\studycards\dto\StudySessionStartResponse$StudySessionMetadata.class
com\studycards\controller\EnumController.class
com\studycards\dto\RealTimeInsights.class
com\studycards\exception\AnswerComparisonException$ErrorResult.class
com\studycards\dto\AnswerReportResponse.class
com\studycards\audit\EnumAuditService.class
com\studycards\dto\ExamTemplateResponse$ExamConfiguration.class
com\studycards\dto\UserProgressResponse$OverallProgress.class
com\studycards\service\SubscriptionLimitService$1.class
com\studycards\exception\ForbiddenException.class
com\studycards\repository\StudySessionRepository.class
com\studycards\config\CacheConfig$1.class
com\studycards\config\ValidationConfig$ScoringConfig$PasswordStrengthConfig$CharacterTypeScores.class
com\studycards\dto\EnhancedDeckResponse.class
com\studycards\service\RecommendationService.class
com\studycards\dto\CardResponse.class
com\studycards\monitoring\AlertSeverity.class
com\studycards\dto\UserProgressResponse$EfficiencyPoint.class
com\studycards\controller\CircuitBreakerController.class
com\studycards\audit\EnumAuditEvent$AuditedEnumType.class
com\studycards\dto\ExamCompletionResult$ExamCompletionResultBuilder.class
com\studycards\dto\RegistrationValidationResponse$RegistrationValidationResponseBuilder.class
com\studycards\dto\SearchSuggestionResponse$Suggestion$SuggestionBuilder.class
com\studycards\dto\paymob\PaymobAuthResponse.class
com\studycards\model\NotificationPreferences$DigestFrequency.class
com\studycards\dto\PermissionMatrixResponse$UserPermissionInfo.class
com\studycards\service\AdvancedAnalyticsService$LearningDataCollection$LearningDataCollectionBuilder.class
com\studycards\controller\LoginActivityController.class
com\studycards\dto\AdDisplayResponse$AdDisplayResponseBuilder.class
com\studycards\enums\AuditEventType.class
com\studycards\circuitbreaker\RecommendationType.class
com\studycards\dto\UpdatePermissionRequest$UpdatePermissionRequestBuilder.class
com\studycards\dto\PasswordGenerationResponse.class
com\studycards\exception\ConflictException.class
com\studycards\dto\UpdateUserProfileRequest.class
com\studycards\dto\DeckPerformanceResponse$DifficultCard$DifficultCardBuilder.class
com\studycards\dto\EnhancedStudySessionResponse$SessionStatistics.class
com\studycards\repository\CollaborationActivityRepository.class
com\studycards\dto\SemanticGradingResponse$SemanticGradingResponseBuilder.class
com\studycards\service\JwtConfigurationValidationService.class
com\studycards\audit\EnumAuditEvent$EnumAuditEventBuilder.class
com\studycards\model\CollaborationInvitation$CollaborationInvitationBuilder.class
com\studycards\dto\ExamHistoryResponse$ExamHistoryResponseBuilder.class
com\studycards\config\ValidationConfig$PasswordConfig.class
com\studycards\service\DashboardService.class
com\studycards\dto\CardValidationRequest.class
com\studycards\dto\UserAchievementResponse.class
com\studycards\controller\NotificationController$2.class
com\studycards\dto\PasswordGenerationRequest$PasswordGenerationRequestBuilder.class
com\studycards\dto\StudyRecommendation.class
com\studycards\config\ValidationConfig$ScoringConfig.class
com\studycards\dto\UserActivityResponse$UserStatistics$UserStatisticsBuilder.class
com\studycards\exception\ActiveSessionExistsException.class
com\studycards\service\SubscriptionValidationService.class
com\studycards\dto\CollaborationTrendItem.class
com\studycards\dto\LearningAnalyticsResponse.class
com\studycards\circuitbreaker\BulkCircuitBreakerResult$BulkCircuitBreakerResultBuilder.class
com\studycards\dto\LearningPatternAnalysis.class
com\studycards\dto\EnhancedExamSessionRequest$EnhancedExamSessionRequestBuilder.class
com\studycards\dto\paymob\PaymobOrderResponse$PaymobOrderResponseBuilder.class
com\studycards\dto\EnhancedExamSessionResponse$EnhancedExamSessionResponseBuilder.class
com\studycards\dto\ReviewCardsResponse$ReviewSessionMetadata$ReviewSessionMetadataBuilder.class
com\studycards\service\StatisticsScheduledMonitor.class
com\studycards\dto\AttachPaymentMethodRequest$AttachPaymentMethodRequestBuilder.class
com\studycards\model\Card.class
com\studycards\dto\MobileStudyProgressRequest$MobileStudyProgressRequestBuilder.class
com\studycards\dto\InvitationResult$InvitationStatus.class
com\studycards\dto\BulkReviewRequest.class
com\studycards\dto\EnhancedExamSessionResponse$ExamHistoryComparison$ExamHistoryComparisonBuilder.class
com\studycards\dto\NextQuestionRecommendation.class
com\studycards\security\CustomOAuth2UserService.class
com\studycards\dto\CardSearchRequest$CardSearchRequestBuilder.class
com\studycards\service\AdaptiveTestingService$PerformanceMetrics$PerformanceMetricsBuilder.class
com\studycards\controller\DeckController.class
com\studycards\dto\StudySessionsPageResponse.class
com\studycards\dto\EnhancedStudySessionResponse$SessionStatistics$SessionStatisticsBuilder.class
com\studycards\config\ValidationConfig$BusinessRulesConfig$CacheConfig.class
com\studycards\dto\ScheduleRecommendation.class
com\studycards\dto\ExamHistoryResponse$ExamConfiguration.class
com\studycards\service\EmailVerificationReminderService$VerificationReminderStats.class
com\studycards\repository\PaymentTransactionRepository.class
com\studycards\controller\PaymentController.class
com\studycards\model\User.class
com\studycards\circuitbreaker\CircuitBreakerEventType.class
com\studycards\service\ReportsService.class
com\studycards\dto\AIInsight.class
com\studycards\model\ExamTemplate$ExamConfiguration$ExamConfigurationBuilder.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobShippingDetails$PaymobShippingDetailsBuilder.class
com\studycards\config\ValidationConfig$ScoringConfig$PasswordStrengthConfig.class
com\studycards\exception\InsufficientSubscriptionException.class
com\studycards\service\PasswordSecurityService$PasswordStrengthLevel.class
com\studycards\service\FraudDetectionService$RiskLevel.class
com\studycards\dto\SemanticComparisonRequest$SemanticComparisonRequestBuilder.class
com\studycards\exception\BusinessRuleException.class
com\studycards\dto\SemanticInsightsRequest$SemanticInsightsRequestBuilder.class
com\studycards\enums\SubscriptionStatus.class
com\studycards\dto\AdaptiveTestingResponse$AdaptiveTestingResponseBuilder.class
com\studycards\dto\LearningEfficiencyMetrics.class
com\studycards\dto\PublicStatisticsResponse$UserStatistics.class
com\studycards\dto\LearningPatternAnalysis$LearningPatternAnalysisBuilder.class
com\studycards\dto\ExamConfigRecommendation$ExamConfigRecommendationBuilder.class
com\studycards\model\CardTag.class
com\studycards\service\DeckCardOperationService.class
com\studycards\dto\ReviewCardsResponse$ReviewSessionMetadata.class
com\studycards\config\RateLimitingFilter.class
com\studycards\exception\ServiceException.class
com\studycards\model\SecurityAuditLog$SecurityAuditLogBuilder.class
com\studycards\dto\InvitationItem.class
com\studycards\repository\UserRepository.class
com\studycards\repository\UserBlockRepository.class
com\studycards\config\PaymobConfig.class
com\studycards\entity\AnswerReport$AnswerReportBuilder.class
com\studycards\service\CacheSecurityService.class
com\studycards\controller\FileController.class
com\studycards\service\AnswerComparisonService.class
com\studycards\validation\ProductionValidationService.class
com\studycards\dto\DeckPerformanceResponse$DeckPerformanceResponseBuilder.class
com\studycards\model\NotificationPreferences$StudyReminderFrequency.class
com\studycards\repository\BillingTransactionRepository.class
com\studycards\dto\SessionPatterns$SessionPatternsBuilder.class
com\studycards\dto\UserActivityResponse$ActivityItem$ActivityItemBuilder.class
com\studycards\service\SemanticAnalysisService$1.class
com\studycards\dto\ConceptAnalysis$ConceptAnalysisBuilder.class
com\studycards\controller\SemanticAnalysisController.class
com\studycards\exception\PaymentException.class
com\studycards\dto\CollaboratorRequest.class
com\studycards\dto\SubscriptionResponse$SubscriptionResponseBuilder.class
com\studycards\dto\paymob\PaymobAuthResponse$PaymobAuthResponseBuilder.class
com\studycards\dto\ProcessAnswerRequest.class
com\studycards\dto\StudySessionResponse$StudySessionResponseBuilder.class
com\studycards\dto\ExamValidationResult.class
com\studycards\service\SearchPerformanceService$SearchPerformanceMetrics$SearchPerformanceMetricsBuilder.class
com\studycards\dto\paymob\PaymobOrderResponse$PaymobOrderItem$PaymobOrderItemBuilder.class
com\studycards\repository\CardHintRepository.class
com\studycards\dto\SemanticGradingRequest$SemanticGradingRequestBuilder.class
com\studycards\dto\UserSummary.class
com\studycards\dto\CollaborationAnalyticsResponse.class
com\studycards\controller\CardTagController$1.class
com\studycards\controller\SubscriptionController.class
com\studycards\config\ValidationConfig$ErrorMessages$Answer.class
com\studycards\repository\PaymentMethodRepository.class
com\studycards\dto\NotificationResponse.class
com\studycards\service\StripePaymentService.class
com\studycards\service\LanguageDetectionService.class
com\studycards\service\SubscriptionStatusService.class
com\studycards\config\DataInitializer.class
com\studycards\service\RateLimitingService.class
com\studycards\circuitbreaker\EnumCircuitBreakerService$CircuitBreakerState.class
com\studycards\config\ValidationConfig$ScoringConfig$ExamScoringConfig$MasteryThresholds.class
com\studycards\model\NotificationPreferences$1.class
com\studycards\dto\UserReportResponse$UserReportResponseBuilder.class
com\studycards\service\DeckAuthorizationService.class
com\studycards\config\ValidationConfig$ScoringConfig$AnswerComparisonConfig$EssayScoring.class
com\studycards\service\AlertingService$1.class
com\studycards\controller\SubscriptionValidationController.class
com\studycards\service\SubscriptionValidationService$SubscriptionFeatures.class
com\studycards\circuitbreaker\FallbackStrategy.class
com\studycards\dto\CollaborationInvitationResponse.class
com\studycards\dto\AnswerFeedbackRequest$AnswerFeedbackRequestBuilder.class
com\studycards\dto\AdaptiveTestingResponse.class
com\studycards\exception\ResourceNotFoundException.class
com\studycards\dto\AnswerReportRequest.class
com\studycards\dto\InvitationListResponse$InvitationListResponseBuilder.class
com\studycards\listener\SyncEventListener.class
com\studycards\monitoring\MonitoringConfig$MonitoringConfigBuilder.class
com\studycards\service\UserProfileService.class
com\studycards\dto\CardHintDto.class
com\studycards\dto\PersonalizedRecommendations$PersonalizedRecommendationsBuilder.class
com\studycards\service\JwtRateLimitService$RateLimitCheck.class
com\studycards\controller\PerformanceMonitoringController.class
com\studycards\dto\UserReportRequest$UserReportRequestBuilder.class
com\studycards\repository\AnswerFeedbackRepository.class
com\studycards\dto\LoginActivityDTO.class
com\studycards\dto\CardPerformanceRequest.class
com\studycards\dto\ExamTemplateResponse$ExamTemplateResponseBuilder.class
com\studycards\service\ExamValidationService.class
com\studycards\dto\SharedDecksResponse.class
com\studycards\dto\CollaborationHealthResponse$CollaborationHealthResponseBuilder.class
com\studycards\service\ContentVisibilityService.class
com\studycards\dto\PaymentMethodResponse.class
com\studycards\service\SubscriptionValidationService$SubscriptionValidationResult.class
com\studycards\dto\DeckRequest$InitialCardRequest.class
com\studycards\service\SearchAnalyticsService.class
com\studycards\dto\PerformancePrediction.class
com\studycards\exception\CardOperationException.class
com\studycards\dto\SpacedRepetitionAnalysis.class
com\studycards\model\Category.class
com\studycards\dto\PublicStatisticsResponse$PublicStatisticsResponseBuilder.class
com\studycards\dto\SignupRequest$SignupRequestBuilder.class
com\studycards\dto\UserReportRequest.class
com\studycards\dto\BatchCardOperationRequest$BatchCardOperationRequestBuilder.class
com\studycards\service\AdaptiveTestingService$QuestionParameters.class
com\studycards\service\SubscriptionSchedulerService.class
com\studycards\dto\RiskAnalysis.class
com\studycards\config\SearchOptimizationConfig$SearchOptimizationProperties.class
com\studycards\dto\StudySessionFilterRequest.class
com\studycards\dto\UserProgressResponse$EfficiencyPoint$EfficiencyPointBuilder.class
com\studycards\controller\UserReportController.class
com\studycards\dto\MobileStudySessionRequest.class
com\studycards\dto\SemanticGradingResponse.class
com\studycards\service\DeckRecommendationService$1.class
com\studycards\service\EmailVerificationService.class
com\studycards\dto\SemanticPerformanceMetrics$PerformanceDataPoint.class
com\studycards\dto\CaptchaVerificationResponse$CaptchaVerificationResponseBuilder.class
com\studycards\dto\DeckPerformanceResponse$DeckInfo$DeckInfoBuilder.class
com\studycards\enums\SharingMethod.class
com\studycards\exception\AnswerComparisonException$ErrorType.class
com\studycards\dto\SearchSuggestionResponse.class
com\studycards\dto\NotificationResponse$NotificationResponseBuilder.class
com\studycards\dto\CardSearchRequest.class
com\studycards\dto\PaymentDataValidationResponse.class
com\studycards\dto\PersonalizedRecommendation.class
com\studycards\service\NotificationPreferencesService$NotificationPreferencesStats$NotificationPreferencesStatsBuilder.class
com\studycards\model\DeckTag$DeckTagBuilder.class
com\studycards\config\AsyncConfig.class
com\studycards\repository\DeckRepositoryCustomImpl.class
com\studycards\audit\EnumAuditEvent$AuditSeverityLevel.class
com\studycards\model\PasswordHistory$ChangeReason.class
com\studycards\circuitbreaker\CircuitBreakerRecommendation$CircuitBreakerRecommendationBuilder.class
com\studycards\model\PaymentTransaction.class
com\studycards\dto\SignupRequest.class
com\studycards\service\PublicStatisticsService.class
com\studycards\dto\ProcessAnswerRequest$ProcessAnswerRequestBuilder.class
com\studycards\dto\SpacedRepetitionAnalysis$SpacedRepetitionAnalysisBuilder.class
com\studycards\util\TextProcessingUtil.class
com\studycards\audit\EnumAuditEvent.class
com\studycards\aspect\PerformanceMonitoringAspect.class
com\studycards\dto\ReviewCardsRequest.class
com\studycards\monitoring\AlertType.class
com\studycards\event\CacheInvalidationEvent$DeckFavoriteChangedEvent.class
com\studycards\dto\EnhancedExamSessionRequest$ExamConfiguration$ExamConfigurationBuilder.class
com\studycards\dto\PublicStatisticsResponse$StudyStatistics.class
com\studycards\dto\UserProfileResponse$UserProfileResponseBuilder.class
com\studycards\model\CollaborationActivity$CollaborationActivityBuilder.class
com\studycards\dto\StudySessionStartResponse$SessionConfiguration$SessionConfigurationBuilder.class
com\studycards\monitoring\SystemHealthMetrics.class
com\studycards\dto\PermissionMatrixResponse$UserPermissionInfo$UserPermissionInfoBuilder.class
com\studycards\dto\DashboardResponse$DeckProgress$DeckProgressBuilder.class
com\studycards\dto\ContentRecommendation$ContentRecommendationBuilder.class
com\studycards\circuitbreaker\CircuitBreakerPerformanceProfile.class
com\studycards\interceptor\EmailVerificationInterceptor.class
com\studycards\exception\GlobalExceptionHandler.class
com\studycards\config\ValidationConfig$RateLimitConfig.class
com\studycards\dto\CollaborationActivityResponse$CollaborationActivityResponseBuilder.class
com\studycards\entity\UserBlock.class
com\studycards\repository\TagRepository.class
com\studycards\dto\CreatePaymentIntentRequest$CreatePaymentIntentRequestBuilder.class
com\studycards\dto\StudySessionAccessValidationResponse$StudySessionAccessValidationResponseBuilder.class
com\studycards\circuitbreaker\CircuitBreakerMetrics.class
com\studycards\dto\DeckCreationResponse.class
com\studycards\dto\BulkReviewResponse.class
com\studycards\service\NotificationPreferencesService.class
com\studycards\dto\ReviewCardsResponse.class
com\studycards\dto\UserPreferencesResponse.class
com\studycards\repository\RefreshTokenRepository.class
com\studycards\model\PaymentMethod.class
com\studycards\controller\CardTagController$AddTagRequest.class
com\studycards\dto\ExamTemplateRequest$ExamTemplateRequestBuilder.class
com\studycards\dto\InvitationResult.class
com\studycards\dto\ExamValidationResult$ExamValidationMetadata.class
com\studycards\dto\EnhancedInvitationResponse.class
com\studycards\service\JwtConfigurationValidationService$ValidationResult.class
com\studycards\service\MobileStudyCacheService.class
com\studycards\dto\BulkPermissionUpdateRequest$PermissionUpdate.class
com\studycards\controller\StatisticsHealthController.class
com\studycards\dto\SemanticInsightsRequest.class
com\studycards\dto\EnhancedInvitationRequest.class
com\studycards\service\PasswordSecurityService$PasswordRequirements$PasswordRequirementsBuilder.class
com\studycards\config\SearchOptimizationConfig.class
com\studycards\security\AuthTokenFilter.class
com\studycards\dto\ChangePasswordRequest.class
com\studycards\dto\SemanticAnalysisDetails$SemanticAnalysisDetailsBuilder.class
com\studycards\dto\CollaborationExportResponse.class
com\studycards\dto\AdaptiveTestingRequest$AdaptiveTestingRequestBuilder.class
com\studycards\service\SearchPerformanceService.class
com\studycards\service\RepositoryUsageExampleService.class
com\studycards\repository\CardPerformanceRepository.class
com\studycards\service\MobileStudyService.class
com\studycards\config\ValidationConfig.class
com\studycards\repository\SearchHistoryRepository.class
com\studycards\dto\SubscriptionValidationRequest$SubscriptionValidationRequestBuilder.class
com\studycards\dto\UserProgressResponse$LearningEfficiency.class
com\studycards\dto\CardOwnershipValidationResponse$CardOwnershipValidationResponseBuilder.class
com\studycards\dto\CollaborationActivityResponse.class
com\studycards\service\StudySessionService.class
com\studycards\service\SubscriptionValidationService$SubscriptionFeatures$SubscriptionFeaturesBuilder.class
com\studycards\dto\ExamTemplateRequest$ExamConfiguration$ExamConfigurationBuilder.class
com\studycards\util\CircuitBreaker.class
com\studycards\dto\UserPreferencesRequest.class
com\studycards\service\SubscriptionService.class
com\studycards\service\ExamTemplateService.class
com\studycards\service\SecurityValidationService$SecurityHealthStatus.class
com\studycards\service\CacheEvictionService.class
com\studycards\dto\UserAchievementResponse$Achievement$AchievementBuilder.class
com\studycards\dto\BulkRemoveCollaboratorsRequest$RemoveCollaborator$RemoveCollaboratorBuilder.class
com\studycards\exception\StudySessionException.class
com\studycards\service\DeckService.class
com\studycards\model\ExamPerformanceHistory.class
com\studycards\dto\StudyStatisticsResponse$CalendarDay.class
com\studycards\service\CaptchaService$CaptchaSolution.class
com\studycards\dto\EnhancedExamSessionRequest$ExamConfiguration.class
com\studycards\exception\BadRequestException.class
com\studycards\event\CacheInvalidationEvent$StudySessionRecordedEvent.class
com\studycards\service\CacheInvalidationService.class
com\studycards\dto\BulkReviewRequest$ReviewItem$ReviewItemBuilder.class
com\studycards\dto\EnhancedExamSessionResponse$ExamHistoryComparison.class
com\studycards\config\AnswerComparisonCacheConfig$CacheStats.class
com\studycards\dto\DeckCreationResponse$ParentFolderInfo.class
com\studycards\service\SpacedRepetitionService$1.class
com\studycards\controller\CardController.class
com\studycards\model\AuditLog$1.class
com\studycards\dto\SessionPatterns.class
com\studycards\dto\StartAdaptiveExamRequest.class
com\studycards\dto\UserAchievementResponse$UserAchievementResponseBuilder.class
com\studycards\enums\Difficulty.class
com\studycards\dto\CardPerformanceResponse$CardPerformanceResponseBuilder.class
com\studycards\dto\StudySessionStartResponse.class
com\studycards\entity\UserBlock$UserBlockBuilder.class
com\studycards\exception\JwtException.class
com\studycards\dto\DeckAccessValidationResponse.class
com\studycards\model\LoginActivity$LoginActivityBuilder.class
com\studycards\dto\LoginActivityDTO$LoginActivityDTOBuilder.class
com\studycards\exception\ErrorResponse.class
com\studycards\dto\AIGradingRequest$AIGradingRequestBuilder.class
com\studycards\dto\AnswerEvaluationReportResponse.class
com\studycards\dto\EnhancedCardPerformanceRequest.class
com\studycards\service\AIGradingService.class
com\studycards\dto\EnhancedStudySessionResponse$EnhancedStudySessionResponseBuilder.class
com\studycards\dto\UserAchievementResponse$Achievement.class
com\studycards\dto\PaymentMethodResponse$PaymentMethodResponseBuilder.class
com\studycards\dto\SemanticComparisonRequest.class
com\studycards\dto\StudyActivityResponse$DailyActivity$DailyActivityBuilder.class
com\studycards\dto\DeckCollaborationStatsResponse.class
com\studycards\circuitbreaker\CircuitBreakerHealthCheck.class
com\studycards\repository\CardPerformanceHistoryRepository.class
com\studycards\repository\PasswordResetTokenRepository.class
com\studycards\dto\ErrorPatternAnalysis.class
com\studycards\config\WebSecurityConfig.class
com\studycards\entity\UserReport$UserReportBuilder.class
com\studycards\repository\CollaborationInvitationRepository.class
com\studycards\service\JwtRateLimitService$RateLimitResult.class
com\studycards\repository\ExamTemplateRepository.class
com\studycards\model\PasswordResetToken.class
com\studycards\model\UserPreferences$UserPreferencesBuilder.class
com\studycards\model\CardTag$CardTagBuilder.class
com\studycards\config\CircuitBreakerConfig.class
com\studycards\dto\SemanticGradingRequest.class
com\studycards\controller\UnifiedSearchController.class
com\studycards\service\DeckValidationService.class
com\studycards\entity\AnswerFeedback$AnswerFeedbackBuilder.class
com\studycards\dto\DifficultyProgression.class
com\studycards\circuitbreaker\CircuitBreakerTestResult.class
com\studycards\dto\UserPreferencesRequest$UserPreferencesRequestBuilder.class
com\studycards\service\MetricsService.class
com\studycards\dto\ExamSecurityMetrics.class
com\studycards\dto\CollaborationAnalyticsResponse$CollaborationAnalyticsResponseBuilder.class
com\studycards\service\RefreshTokenService.class
com\studycards\controller\CardHintController.class
com\studycards\audit\EnumAuditRepository.class
com\studycards\dto\ShareDeckResponse$ShareDeckResponseBuilder.class
com\studycards\dto\KnowledgeGap$KnowledgeGapBuilder.class
com\studycards\dto\BulkRemoveCollaboratorsRequest$RemoveCollaborator.class
com\studycards\dto\StartAdaptiveExamRequest$StartAdaptiveExamRequestBuilder.class
com\studycards\dto\ReviewCardsRequest$ReviewCardsRequestBuilder.class
com\studycards\service\SecurityAuditService.class
com\studycards\dto\ExamConfigRequest$ExamConfigRequestBuilder.class
com\studycards\event\DeckCreatedEvent.class
com\studycards\repository\UserReportRepository.class
com\studycards\dto\paymob\PaymobAuthResponse$PaymobUser$PaymobUserBuilder.class
com\studycards\dto\SearchSuggestionResponse$Suggestion.class
com\studycards\config\SubscriptionMigrationStartup.class
com\studycards\dto\DeckHierarchyResponse$DeckHierarchyResponseBuilder.class
com\studycards\dto\EnhancedStudySessionResponse$SessionComparison.class
com\studycards\service\SyncConflictResolver.class
com\studycards\monitoring\MonitoringController.class
com\studycards\enums\SessionType.class
com\studycards\service\SubscriptionMigrationService.class
com\studycards\dto\AnswerComparisonResult.class
com\studycards\service\ImprovedCollaborationService.class
com\studycards\model\NotificationPreferences.class
com\studycards\config\ValidationConfig$RateLimitConfig$RateLimitRule.class
com\studycards\dto\LearningAnalyticsResponse$LearningAnalyticsResponseBuilder.class
com\studycards\model\Deck.class
com\studycards\model\PasswordResetToken$PasswordResetTokenBuilder.class
com\studycards\dto\DeckPerformanceResponse.class
com\studycards\dto\StudyStatisticsResponse$StudyRecommendation.class
com\studycards\dto\FavoriteResponse.class
com\studycards\dto\AnswerEvaluationReportRequest$AnswerEvaluationReportRequestBuilder.class
com\studycards\dto\DeckPerformanceResponse$ComparisonMetrics.class
com\studycards\circuitbreaker\CircuitBreakerConfigModel$CircuitBreakerConfigModelBuilder.class
com\studycards\dto\BillingStatusMetadata$BillingStatusMetadataBuilder.class
com\studycards\dto\StudyStatisticsResponse$CalendarDay$CalendarDayBuilder.class
com\studycards\dto\AuthenticationValidationResponse.class
com\studycards\service\AdvancedAnalyticsService$LearningDataCollection.class
com\studycards\circuitbreaker\EnumCircuitBreakerService.class
com\studycards\dto\UserReportResponse.class
com\studycards\dto\DeckPerformanceResponse$DifficultCard.class
com\studycards\exception\AccountLockedException.class
com\studycards\dto\CardPerformanceResponse$PerformanceHistoryItem$PerformanceHistoryItemBuilder.class
com\studycards\dto\StudyStatisticsResponse$LearningProgress$LearningProgressBuilder.class
com\studycards\service\PerformanceMonitoringService$PerformanceMetrics.class
com\studycards\dto\ExamSessionRequest$ExamSessionRequestBuilder.class
com\studycards\dto\UserProgressResponse$ProgressPoint.class
com\studycards\circuitbreaker\CircuitBreakerInfo$CircuitBreakerInfoBuilder.class
com\studycards\service\WordEmbeddingService.class
com\studycards\dto\SemanticAnalysisResponse$SemanticAnalysisResponseBuilder.class
com\studycards\dto\DeckCollaborationStatsResponse$DeckCollaborationStatsResponseBuilder.class
com\studycards\dto\StudyStatisticsResponse$StudyPatterns.class
com\studycards\config\AnswerComparisonConfig.class
com\studycards\model\ExamPerformanceHistory$ExamPerformanceHistoryBuilder.class
com\studycards\dto\paymob\PaymobOrderRequest$PaymobShippingData$PaymobShippingDataBuilder.class
com\studycards\dto\UserSummaryResponse.class
com\studycards\dto\BulkReviewRequest$BulkReviewRequestBuilder.class
com\studycards\service\AnswerFeedbackService.class
com\studycards\service\RepositoryValidationService.class
com\studycards\service\PasswordSecurityService.class
com\studycards\service\JwtMetricsService$TimerContext.class
com\studycards\monitoring\AlertContext.class
com\studycards\circuitbreaker\CircuitBreakerDashboard.class
com\studycards\dto\AnswerFeedbackResponse$AnswerFeedbackResponseBuilder.class
com\studycards\dto\SemanticAnalysisRequest.class
com\studycards\service\ContentVisibilityService$1.class
com\studycards\circuitbreaker\CircuitBreakerAlertConfig.class
com\studycards\service\SubscriptionValidationService$UserUsageTracker.class
com\studycards\repository\DeckCollaboratorRepository.class
com\studycards\service\SearchAnalyticsService$SearchPerformanceMetrics.class
com\studycards\dto\paymob\PaymobPaymentKeyRequest.class
com\studycards\dto\UserActivityResponse$ActivityItem.class
com\studycards\circuitbreaker\CircuitBreakerStatistics$CircuitBreakerStatisticsBuilder.class
com\studycards\controller\AnalyticsController.class
com\studycards\dto\ContentRecommendation.class
com\studycards\circuitbreaker\CircuitBreakerStatistics.class
com\studycards\controller\MobileStudyController.class
com\studycards\dto\PerformancePrediction$PerformancePredictionBuilder.class
com\studycards\event\CacheInvalidationEvent$DeckChangedEvent.class
com\studycards\dto\AttachPaymentMethodRequest.class
com\studycards\dto\DeckCreationResponse$ParentFolderInfo$ParentFolderInfoBuilder.class
com\studycards\dto\DeckPerformanceResponse$StudyPatterns.class
com\studycards\dto\paymob\PaymobOrderResponse$PaymobMerchant.class
com\studycards\dto\AutoSaveDraftRequest$AutoSaveDraftRequestBuilder.class
com\studycards\dto\BatchCardCreateRequest$BatchCardCreateRequestBuilder.class
com\studycards\dto\CaptchaVerificationRequest$CaptchaVerificationRequestBuilder.class
com\studycards\dto\CreateStudySessionRequest.class
com\studycards\dto\UserActivityResponse$DeckSummary$DeckSummaryBuilder.class
com\studycards\dto\paymob\PaymobOrderResponse.class
com\studycards\controller\EmailVerificationController.class
com\studycards\dto\TokenRefreshResponse.class
com\studycards\service\PaymobPaymentService.class
com\studycards\circuitbreaker\CircuitBreakerStatus.class
com\studycards\circuitbreaker\CircuitBreakerPerformanceProfile$CircuitBreakerPerformanceProfileBuilder.class
com\studycards\dto\CaptchaVerificationRequest.class
com\studycards\dto\AdDisplayResponse.class
com\studycards\dto\ExamValidationResult$ExamValidationMetadata$ExamValidationMetadataBuilder.class
com\studycards\dto\SubscriptionValidationRequest.class
com\studycards\controller\AuthController.class
com\studycards\dto\RegistrationResponse$RegistrationResponseBuilder.class
com\studycards\exception\DataAccessException.class
com\studycards\service\RateLimitService$RateLimitEntry.class
com\studycards\circuitbreaker\CircuitBreakerStatus$CircuitBreakerStatusBuilder.class
com\studycards\repository\CardRepository.class
com\studycards\controller\DashboardController.class
com\studycards\service\FraudDetectionService.class
com\studycards\dto\CollaborationDashboardResponse$CollaborationDashboardResponseBuilder.class
com\studycards\event\CacheInvalidationEvent.class
com\studycards\dto\AnswerReportResponse$AnswerReportResponseBuilder.class
com\studycards\dto\FavoriteResponse$FavoriteResponseBuilder.class
com\studycards\service\UnifiedScoringService.class
com\studycards\dto\DailyGoalResponse.class
com\studycards\dto\DeckPerformanceResponse$RecommendedAction.class
com\studycards\dto\LearningProgressionAnalysis$LearningProgressionAnalysisBuilder.class
com\studycards\service\StatisticsMonitoringService.class
com\studycards\service\JwtMetricsService$JwtMetricsSummary.class
com\studycards\circuitbreaker\CircuitBreakerMetrics$CircuitBreakerMetricsBuilder.class
com\studycards\monitoring\WebhookPayload$WebhookPayloadBuilder.class
com\studycards\dto\paymob\PaymobAuthResponse$PaymobUser.class
com\studycards\dto\StudySessionsPageResponse$StudySessionsPageResponseBuilder.class
com\studycards\util\TextProcessingUtil$KeywordAnalysisResult.class
com\studycards\dto\DeckPerformanceResponse$PerformancePoint$PerformancePointBuilder.class
com\studycards\dto\DeckCreationResponse$DeckCreationResponseBuilder.class
com\studycards\dto\PersonalizedRecommendations.class
com\studycards\config\ValidationConfig$ErrorMessages$Password.class
com\studycards\service\BillingDataInitializerService.class
com\studycards\config\ValidationConfig$ErrorMessages$Exam.class
com\studycards\service\ImprovedCollaborationService$1.class
com\studycards\controller\DiscoveryController.class
com\studycards\dto\PersonalizedRecommendation$PersonalizedRecommendationBuilder.class
com\studycards\dto\UserActivityResponse.class
com\studycards\config\AnswerComparisonCacheConfig.class
com\studycards\controller\NotificationController$1.class
com\studycards\dto\DashboardResponse$DeckProgress.class
com\studycards\dto\CreateStudySessionRequest$CreateStudySessionRequestBuilder.class
com\studycards\circuitbreaker\CircuitBreakerRecommendation.class
com\studycards\dto\CardPerformanceResponse$PerformanceHistoryItem.class
com\studycards\circuitbreaker\CircuitBreakerConfigModel.class
com\studycards\model\CollaborationActivity$ActivityType.class
com\studycards\dto\SearchSuggestionResponse$SearchSuggestionResponseBuilder.class
com\studycards\config\AnswerComparisonCacheConfig$CacheProperties.class
com\studycards\exception\RateLimitExceededException.class
com\studycards\service\ImprovedAnalyticsService.class
com\studycards\monitoring\SystemHealthMetrics$SystemHealthMetricsBuilder.class
com\studycards\exception\DeckCreationException.class
com\studycards\service\AnnotationService.class
com\studycards\service\EnhancedNotificationService$1.class
com\studycards\dto\CreateCollaborationTemplateRequest.class
com\studycards\dto\paymob\PaymobOrderResponse$PaymobMerchant$PaymobMerchantBuilder.class
com\studycards\dto\AnalyticsRequest$AnalyticsRequestBuilder.class
com\studycards\dto\StudyStatisticsResponse$DeckPerformance$DeckPerformanceBuilder.class
com\studycards\dto\StudyStatisticsResponse$OverallStatistics.class
com\studycards\circuitbreaker\BulkCircuitBreakerResult.class
com\studycards\dto\SubscriptionStatusValidationResponse.class
com\studycards\config\ValidationConfig$ScoringConfig$ExamScoringConfig$GradeThresholds.class
com\studycards\dto\InvitationResult$InvitationResultBuilder.class
com\studycards\dto\ConceptAnalysis.class
com\studycards\dto\EnhancedInvitationResponse$EnhancedInvitationResponseBuilder.class
com\studycards\model\SearchHistory$SearchHistoryBuilder.class
com\studycards\dto\AIEnhancedFeedback.class
com\studycards\dto\UserPreferencesResponse$UserPreferencesResponseBuilder.class
com\studycards\service\TokenBlacklistService$BlacklistStats.class
com\studycards\event\CardDeletedEvent$DeletionType.class
com\studycards\model\PasswordHistory$PasswordHistoryBuilder.class
com\studycards\dto\UserResponse.class
com\studycards\dto\BulkRemoveCollaboratorsRequest.class
com\studycards\dto\DashboardResponse$UpcomingReview.class
com\studycards\dto\ExamHistoryResponse.class
com\studycards\dto\ExamConfigRequest.class
com\studycards\model\DeckTag.class
com\studycards\config\ValidationConfig$ScoringConfig$AnswerComparisonConfig.class
com\studycards\dto\AuthenticationValidationResponse$AuthenticationValidationResponseBuilder.class
com\studycards\dto\CaptchaResponse$CaptchaResponseBuilder.class
com\studycards\dto\TokenRefreshRequest.class
com\studycards\dto\ExamTemplateRequest$ExamConfiguration.class
com\studycards\service\SearchAnalyticsService$SearchPerformanceMetrics$SearchPerformanceMetricsBuilder.class
com\studycards\dto\DeckSearchRequest.class
com\studycards\dto\UserSummaryResponse$UserSummaryResponseBuilder.class
com\studycards\service\SecurityService.class
com\studycards\security\OAuth2AuthenticationFailureHandler.class
com\studycards\repository\DeckTagRepository.class
com\studycards\security\UserDetailsImpl$1.class
com\studycards\circuitbreaker\EnumCircuitBreakerService$CircuitBreaker.class
com\studycards\model\AuditLog$AuditLogBuilder.class
com\studycards\exception\ValidationErrorResponse.class
com\studycards\aspect\RepositorySecurityAspect.class
com\studycards\config\RateLimitingFilter$RateLimitData.class
com\studycards\service\RateLimitingService$RateLimitType.class
com\studycards\model\LoginActivity.class
com\studycards\config\AnswerComparisonCacheConfig$1.class
com\studycards\dto\UserSearchResponse$UserSearchResponseBuilder.class
com\studycards\dto\CollaboratorResponse$CollaboratorResponseBuilder.class
com\studycards\dto\DeckPerformanceResponse$DeckInfo.class
com\studycards\dto\AnalyticsRequest.class
com\studycards\event\CacheInvalidationEvent$CollaborationChangedEvent.class
com\studycards\config\InternationalizationConfig.class
com\studycards\service\CacheUtilityService.class
com\studycards\dto\MotivationFactors.class
com\studycards\model\AuditLog.class
com\studycards\service\AnswerReportService.class
com\studycards\dto\SubscriptionPlanResponse$SubscriptionPlanResponseBuilder.class
com\studycards\util\CircuitBreaker$State.class
com\studycards\dto\UserProgressResponse$LearningEfficiency$LearningEfficiencyBuilder.class
com\studycards\service\CardService.class
com\studycards\dto\PredictiveInsights.class
com\studycards\dto\ScheduleRecommendation$ScheduleRecommendationBuilder.class
