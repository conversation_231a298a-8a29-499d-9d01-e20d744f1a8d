package com.studycards.service;

import com.studycards.model.SearchHistory;
import com.studycards.model.User;
import com.studycards.repository.SearchHistoryRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for tracking and analyzing search behavior
 */
@Slf4j
@Service
public class SearchAnalyticsService {

    @Autowired
    private SearchHistoryRepository searchHistoryRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired(required = false)
    private HttpServletRequest httpServletRequest;

    /**
     * Track search request asynchronously
     */
    @Async
    @Transactional
    public CompletableFuture<Void> trackSearch(Object searchRequest, String searchType) {
        try {
            User currentUser = userService.getCurrentUser();
            
            SearchHistory searchHistory = SearchHistory.builder()
                .user(currentUser)
                .queryText(extractQueryText(searchRequest))
                .searchType(searchType)
                .searchTimestamp(LocalDateTime.now())
                .selectedFilters(serializeFilters(searchRequest))
                .clientIp(getClientIp())
                .userAgent(getUserAgent())
                .build();

            searchHistoryRepository.save(searchHistory);
            
            log.debug("Tracked search: user={}, type={}, query={}", 
                currentUser.getId(), searchType, extractQueryText(searchRequest));
                
        } catch (Exception e) {
            log.error("Failed to track search analytics", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Update search result metrics
     */
    @Async
    @Transactional
    public CompletableFuture<Void> updateSearchResults(Long searchHistoryId, int resultCount, boolean clickedResult) {
        try {
            searchHistoryRepository.findById(searchHistoryId).ifPresent(searchHistory -> {
                searchHistory.setResultCount(resultCount);
                searchHistory.setClickedResult(clickedResult);
                searchHistoryRepository.save(searchHistory);
            });
        } catch (Exception e) {
            log.error("Failed to update search result metrics", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Get recent searches for a user
     */
    @Transactional(readOnly = true)
    public List<SearchHistory> getRecentSearches(User user, int limit) {
        return searchHistoryRepository.findByUserOrderBySearchTimestampDesc(user, 
            org.springframework.data.domain.PageRequest.of(0, limit));
    }

    /**
     * Get popular search terms
     */
    @Transactional(readOnly = true)
    public List<String> getPopularSearchTerms(int limit) {
        return searchHistoryRepository.findPopularSearchTerms(
            org.springframework.data.domain.PageRequest.of(0, limit));
    }

    /**
     * Get search performance metrics
     */
    @Transactional(readOnly = true)
    public SearchPerformanceMetrics getSearchPerformanceMetrics(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> metrics = searchHistoryRepository.getSearchMetrics(startDate, endDate);
        
        return SearchPerformanceMetrics.builder()
            .totalSearches(getTotalSearches(metrics))
            .averageResultCount(getAverageResultCount(metrics))
            .clickThroughRate(getClickThroughRate(metrics))
            .topSearchTerms(getTopSearchTerms(metrics))
            .build();
    }

    /**
     * Extract query text from search request
     */
    private String extractQueryText(Object searchRequest) {
        try {
            if (searchRequest instanceof com.studycards.dto.DeckSearchRequest) {
                return ((com.studycards.dto.DeckSearchRequest) searchRequest).getQuery();
            } else if (searchRequest instanceof com.studycards.dto.CardSearchRequest) {
                return ((com.studycards.dto.CardSearchRequest) searchRequest).getQuery();
            }
        } catch (Exception e) {
            log.warn("Failed to extract query text from search request", e);
        }
        return null;
    }

    /**
     * Serialize search filters to JSON
     */
    private String serializeFilters(Object searchRequest) {
        try {
            return objectMapper.writeValueAsString(searchRequest);
        } catch (Exception e) {
            log.warn("Failed to serialize search filters", e);
            return null;
        }
    }

    /**
     * Get client IP address
     */
    private String getClientIp() {
        if (httpServletRequest == null) return null;
        
        String xForwardedFor = httpServletRequest.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = httpServletRequest.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return httpServletRequest.getRemoteAddr();
    }

    /**
     * Get user agent
     */
    private String getUserAgent() {
        if (httpServletRequest == null) return null;
        return httpServletRequest.getHeader("User-Agent");
    }

    // Helper methods for metrics calculation
    private long getTotalSearches(List<Object[]> metrics) {
        return metrics.size();
    }

    private double getAverageResultCount(List<Object[]> metrics) {
        return metrics.stream()
            .filter(row -> row[1] != null)
            .mapToInt(row -> ((Number) row[1]).intValue())
            .average()
            .orElse(0.0);
    }

    private double getClickThroughRate(List<Object[]> metrics) {
        long totalSearches = metrics.size();
        long clickedSearches = metrics.stream()
            .filter(row -> row[2] != null && (Boolean) row[2])
            .count();
        
        return totalSearches > 0 ? (double) clickedSearches / totalSearches : 0.0;
    }

    private List<String> getTopSearchTerms(List<Object[]> metrics) {
        return metrics.stream()
            .filter(row -> row[0] != null)
            .map(row -> (String) row[0])
            .distinct()
            .limit(10)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * DTO for search performance metrics
     */
    @lombok.Data
    @lombok.Builder
    public static class SearchPerformanceMetrics {
        private long totalSearches;
        private double averageResultCount;
        private double clickThroughRate;
        private List<String> topSearchTerms;
    }
}
