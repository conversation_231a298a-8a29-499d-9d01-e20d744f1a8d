2025-07-19 00:00:00.235 [MessageBroker-10] INFO  PERFORMANCE - 
                [cleanupExpiredTokens] [170ms] [] DB_OPERATION: $Proxy234.deleteAllExpiredTokens took 170ms
2025-07-19 00:00:00.236 [MessageBroker-2] INFO  PERFORMANCE - 
                [deleteOldNotifications] [171ms] [] DB_OPERATION: $Proxy220.deleteOldNotifications took 171ms
2025-07-19 00:06:40.935 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [189ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 189ms
2025-07-19 00:06:41.272 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [178ms] [] DB_OPERATION: DeckRepositoryCustomImpl.findRelatedDecksByTagsUsingDeckTags took 178ms
2025-07-19 00:06:41.284 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [214ms] [] DB_OPERATION: $Proxy206.findRelatedDecksByTags took 214ms
2025-07-19 00:06:41.319 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [576ms] [] SERVICE_METHOD: DeckService.getEnhancedDeckById took 576ms
2025-07-19 00:06:44.693 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [144ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 144ms
2025-07-19 00:06:44.693 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCardsByDeckId] [142ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 142ms
2025-07-19 00:06:45.148 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [createStudySession] [357ms] [] DB_OPERATION: $Proxy209.save took 357ms
2025-07-19 00:07:24.766 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [createStudySession] [25568ms] [] SLOW_DB_OPERATION: $Proxy209.save took 25568ms
2025-07-19 00:07:24.766 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [15947ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 15947ms
2025-07-19 00:07:24.766 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [createStudySession] [25575ms] [] SLOW_SERVICE_METHOD: StudySessionServiceExtension.createStudySession took 25575ms
2025-07-19 00:07:24.766 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [createStudySession] [25575ms] [] SLOW_SERVICE_METHOD: StudySessionService.createStudySession took 25575ms
2025-07-19 00:07:24.769 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [25578ms] [] SLOW_ENDPOINT: StudySessionController.createStudySession took 25578ms
2025-07-19 00:07:25.771 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [157ms] [] DB_OPERATION: $Proxy194.findByEmail took 157ms
2025-07-19 00:07:25.792 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getCurrentUser] [1021ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1021ms
2025-07-19 00:07:25.792 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getCurrentUser] [1021ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1021ms
2025-07-19 00:07:28.708 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getCurrentUser] [2933ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 2933ms
2025-07-19 00:07:28.708 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getCurrentUser] [2933ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 2933ms
2025-07-19 00:07:28.708 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [2933ms] [] SLOW_ENDPOINT: UserController.getCurrentUser took 2933ms
2025-07-19 00:07:28.854 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [29657ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 29657ms
2025-07-19 00:07:28.854 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [29657ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 29657ms
2025-07-19 00:07:28.883 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [29687ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 29687ms
2025-07-19 00:07:29.179 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [157ms] [] DB_OPERATION: $Proxy194.findByEmail took 157ms
2025-07-19 00:07:29.191 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [169ms] [] DB_OPERATION: $Proxy194.findByEmail took 169ms
2025-07-19 00:07:29.294 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getCurrentUser] [101ms] [] DB_OPERATION: $Proxy194.findById took 101ms
2025-07-19 00:07:29.303 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [185ms] [] DB_OPERATION: $Proxy194.findByEmail took 185ms
2025-07-19 00:07:29.437 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [384ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 384ms
2025-07-19 00:07:31.832 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [2450ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 2450ms
2025-07-19 00:07:31.840 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [mapToDeckResponse] [2395ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckId took 2395ms
2025-07-19 00:07:31.852 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [mapToDeckResponse] [2407ms] [] SLOW_SERVICE_METHOD: DeckService.mapToDeckResponse took 2407ms
2025-07-19 00:07:31.852 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [2800ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 2800ms
2025-07-19 00:12:34.897 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [113ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 113ms
2025-07-19 00:12:51.755 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy194.findByEmail took 105ms
2025-07-19 00:12:51.755 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy194.findByEmail took 105ms
2025-07-19 00:12:51.756 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [106ms] [] DB_OPERATION: $Proxy194.findByEmail took 106ms
2025-07-19 00:23:15.403 [MessageBroker-15] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-19 00:23:15.745 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [262ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 262ms
2025-07-19 00:23:15.745 [MessageBroker-12] INFO  PERFORMANCE - 
                [sendVerificationReminders] [262ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 262ms
2025-07-19 00:23:15.745 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [218ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 218ms
2025-07-19 00:23:15.745 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [218ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 218ms
2025-07-19 00:23:15.752 [main] INFO  PERFORMANCE - 
                [] [138ms] [] DB_OPERATION: $Proxy211.count took 138ms
2025-07-19 00:23:15.922 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [112ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 112ms
2025-07-19 00:23:15.922 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [112ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 112ms
2025-07-19 00:23:16.023 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [521ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 521ms
2025-07-19 00:23:16.023 [MessageBroker-16] INFO  PERFORMANCE - 
                [cacheWarming] [521ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 521ms
2025-07-19 00:23:16.023 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [621ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 621ms
2025-07-19 00:23:16.023 [MessageBroker-16] INFO  PERFORMANCE - 
                [cacheWarming] [621ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 621ms
2025-07-19 00:25:45.221 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [createStudySession] [245ms] [] DB_OPERATION: $Proxy209.save took 245ms
2025-07-19 00:25:45.257 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCardsByDeckId] [329ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 329ms
2025-07-19 00:25:45.257 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [346ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 346ms
2025-07-19 00:26:27.110 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [341ms] [] DB_OPERATION: $Proxy194.findByEmail took 341ms
2025-07-19 00:26:27.514 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [181ms] [] DB_OPERATION: $Proxy207.findById took 181ms
2025-07-19 00:26:27.897 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [112ms] [] DB_OPERATION: $Proxy214.countByCardIdAndUserId took 112ms
2025-07-19 00:26:28.139 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [110ms] [] DB_OPERATION: $Proxy214.findByStudySessionAndCardAndUser took 110ms
2025-07-19 00:26:29.357 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1212ms] [] SLOW_DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 1212ms
2025-07-19 00:26:29.382 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [2049ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 2049ms
2025-07-19 00:26:29.516 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [2231ms] [] SLOW_ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 2231ms
2025-07-19 00:26:30.152 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCurrentUser] [112ms] [] DB_OPERATION: $Proxy194.findById took 112ms
2025-07-19 00:26:30.296 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [143ms] [] DB_OPERATION: $Proxy194.findById took 143ms
2025-07-19 00:26:31.555 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getCardsByDeckId] [1204ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 1204ms
2025-07-19 00:26:31.675 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCardsByDeckId] [118ms] [] DB_OPERATION: $Proxy207.findByDeckId took 118ms
2025-07-19 00:26:31.795 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getCardsByDeckId] [1445ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 1445ms
2025-07-19 00:26:31.837 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [1498ms] [] ENDPOINT: CardController.getCardsByDeckId took 1498ms
2025-07-19 00:28:51.921 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [189ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 189ms
2025-07-19 00:39:05.959 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [112ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 112ms
2025-07-19 00:39:33.538 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [] [5540ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 5540ms
2025-07-19 00:39:33.593 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCardsByDeckId] [27542ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 27542ms
2025-07-19 00:39:33.603 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCardsByDeckId] [27552ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 27552ms
2025-07-19 00:39:33.603 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [27553ms] [] SLOW_ENDPOINT: CardController.getCardsByDeckId took 27553ms
2025-07-19 00:40:04.392 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [696ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 696ms
2025-07-19 00:40:07.063 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [2666ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 2666ms
2025-07-19 00:40:07.063 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [2666ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 2666ms
2025-07-19 00:40:07.096 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [2699ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 2699ms
2025-07-19 00:54:59.295 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [138ms] [] DB_OPERATION: $Proxy194.findByEmail took 138ms
2025-07-19 00:55:02.958 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [141ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 141ms
2025-07-19 00:56:19.387 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [245ms] [] DB_OPERATION: $Proxy194.findByEmail took 245ms
2025-07-19 00:56:19.586 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCurrentUser] [169ms] [] DB_OPERATION: $Proxy194.findById took 169ms
2025-07-19 00:56:19.791 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [113ms] [] DB_OPERATION: $Proxy207.findById took 113ms
2025-07-19 00:56:19.932 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [119ms] [] DB_OPERATION: $Proxy209.findById took 119ms
2025-07-19 00:56:20.301 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [209ms] [] DB_OPERATION: $Proxy214.findRecentByUserId took 209ms
2025-07-19 00:56:20.453 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [144ms] [] DB_OPERATION: $Proxy214.getAverageStudyTimeByCardId took 144ms
2025-07-19 00:56:20.604 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [150ms] [] DB_OPERATION: $Proxy214.countByCardIdAndUserId took 150ms
2025-07-19 00:56:20.719 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [783ms] [] SERVICE_METHOD: SpacedRepetitionService.calculateEnhancedNextReview took 783ms
2025-07-19 00:56:20.948 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [159ms] [] DB_OPERATION: $Proxy214.save took 159ms
2025-07-19 00:56:21.071 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1395ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 1395ms
2025-07-19 00:56:21.438 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [1772ms] [] ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 1772ms
2025-07-19 00:56:22.407 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [824ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 824ms
2025-07-19 00:56:22.883 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCurrentUser] [457ms] [] DB_OPERATION: $Proxy194.findById took 457ms
2025-07-19 00:56:30.151 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getCardsByDeckId] [7222ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 7222ms
2025-07-19 00:56:30.292 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getCardsByDeckId] [7364ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 7364ms
2025-07-19 00:56:30.325 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [7401ms] [] SLOW_ENDPOINT: CardController.getCardsByDeckId took 7401ms
2025-07-19 00:56:40.789 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [252ms] [] DB_OPERATION: $Proxy194.findByEmail took 252ms
2025-07-19 00:56:40.996 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCurrentUser] [197ms] [] DB_OPERATION: $Proxy194.findById took 197ms
2025-07-19 00:56:42.055 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1035ms] [] SLOW_DB_OPERATION: $Proxy207.findById took 1035ms
2025-07-19 00:56:42.319 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1299ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 1299ms
2025-07-19 00:56:42.383 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [1374ms] [] ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 1374ms
2025-07-19 00:59:57.195 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [374ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 374ms
2025-07-19 01:00:34.786 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [104ms] [] DB_OPERATION: $Proxy194.findByEmail took 104ms
2025-07-19 01:00:34.786 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [107ms] [] DB_OPERATION: $Proxy194.findByEmail took 107ms
2025-07-19 01:00:34.786 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [107ms] [] DB_OPERATION: $Proxy194.findByEmail took 107ms
2025-07-19 01:00:34.952 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [140ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 140ms
2025-07-19 01:01:10.435 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [3726ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 3726ms
2025-07-19 01:01:10.435 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [3726ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 3726ms
2025-07-19 01:01:10.451 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [3742ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 3742ms
2025-07-19 01:03:43.035 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [339ms] [] DB_OPERATION: $Proxy194.findByEmail took 339ms
2025-07-19 01:03:43.036 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [340ms] [] DB_OPERATION: $Proxy194.findByEmail took 340ms
2025-07-19 01:03:43.041 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [345ms] [] DB_OPERATION: $Proxy194.findByEmail took 345ms
2025-07-19 01:11:12.265 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [171ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 171ms
2025-07-19 01:11:45.340 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [32824ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 32824ms
2025-07-19 01:11:45.342 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [29962ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 29962ms
2025-07-19 01:11:49.749 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [197ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 197ms
2025-07-19 01:11:56.865 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getCurrentUser] [122ms] [] DB_OPERATION: $Proxy194.findById took 122ms
2025-07-19 01:14:16.480 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [215ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 215ms
2025-07-19 01:14:29.937 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [440ms] [] DB_OPERATION: $Proxy194.findByEmail took 440ms
2025-07-19 01:14:29.939 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [441ms] [] DB_OPERATION: $Proxy194.findByEmail took 441ms
2025-07-19 01:14:29.940 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getCurrentUser] [1372ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1372ms
2025-07-19 01:14:29.940 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getCurrentUser] [1372ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1372ms
2025-07-19 01:14:29.941 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [444ms] [] DB_OPERATION: $Proxy194.findByEmail took 444ms
2025-07-19 01:14:29.943 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [445ms] [] DB_OPERATION: $Proxy194.findByEmail took 445ms
2025-07-19 01:14:30.128 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [174ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 174ms
2025-07-19 01:14:40.599 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [3882ms] [] SLOW_DB_OPERATION: $Proxy214.findByStudySessionAndCardAndUser took 3882ms
2025-07-19 01:14:40.755 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [156ms] [] DB_OPERATION: $Proxy214.save took 156ms
2025-07-19 01:15:13.087 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [32326ms] [] SLOW_DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 32326ms
2025-07-19 01:15:13.087 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [17042ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 17042ms
2025-07-19 01:15:13.101 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [] [2874ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 2874ms
2025-07-19 01:15:13.106 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [36488ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 36488ms
2025-07-19 01:15:13.257 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [] [36640ms] [] SLOW_ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 36640ms
2025-07-19 01:15:27.193 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getCardsByDeckId] [13822ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 13822ms
2025-07-19 01:15:27.216 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getCardsByDeckId] [13846ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 13846ms
2025-07-19 01:15:27.217 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [13847ms] [] SLOW_ENDPOINT: CardController.getCardsByDeckId took 13847ms
2025-07-19 01:16:41.041 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [6333ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 6333ms
2025-07-19 01:16:41.219 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCurrentUser] [175ms] [] DB_OPERATION: $Proxy194.findById took 175ms
2025-07-19 01:16:41.221 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [] [6513ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 6513ms
2025-07-19 01:16:41.221 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [1392ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1392ms
2025-07-19 01:16:41.223 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [] [6516ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 6516ms
2025-07-19 01:16:41.225 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [6495ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 6495ms
2025-07-19 01:16:41.480 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getCurrentUserPreferences] [107ms] [] DB_OPERATION: $Proxy244.findByUser took 107ms
2025-07-19 01:16:41.657 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [432ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 432ms
2025-07-19 01:16:43.417 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [753ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 753ms
2025-07-19 01:16:43.437 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [449ms] [] DB_OPERATION: $Proxy194.findByEmail took 449ms
2025-07-19 01:16:51.743 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [10168ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 10168ms
2025-07-19 01:16:51.744 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [10169ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 10169ms
2025-07-19 01:16:51.943 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [mapToDeckResponse] [177ms] [] DB_OPERATION: $Proxy207.countByDeckId took 177ms
2025-07-19 01:16:51.943 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [completeStudySession] [125ms] [] DB_OPERATION: $Proxy209.findById took 125ms
2025-07-19 01:16:51.943 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getSessionRatings] [168ms] [] DB_OPERATION: $Proxy209.findById took 168ms
2025-07-19 01:16:52.067 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [10492ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 10492ms
2025-07-19 01:16:52.516 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [192ms] [] DB_OPERATION: $Proxy194.findByEmail took 192ms
2025-07-19 01:16:52.877 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [205ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 205ms
2025-07-19 01:17:59.846 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getCurrentUser] [1075ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1075ms
2025-07-19 01:17:59.846 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getCurrentUser] [1075ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1075ms
2025-07-19 01:22:04.981 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [282ms] [] DB_OPERATION: $Proxy194.findByEmail took 282ms
2025-07-19 01:22:04.981 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [283ms] [] DB_OPERATION: $Proxy194.findByEmail took 283ms
2025-07-19 01:22:05.027 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [334ms] [] DB_OPERATION: $Proxy194.findByEmail took 334ms
2025-07-19 01:22:08.369 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [3311ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 3311ms
2025-07-19 01:22:08.370 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [3312ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 3312ms
2025-07-19 01:22:08.389 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [3332ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 3332ms
2025-07-19 01:25:29.398 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [9656ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 9656ms
2025-07-19 01:25:29.401 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [9659ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 9659ms
2025-07-19 01:25:29.413 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [9671ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 9671ms
2025-07-19 01:25:48.922 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [mapToDeckResponse] [11083ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 11083ms
2025-07-19 01:25:48.930 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [mapToDeckResponse] [11091ms] [] SLOW_SERVICE_METHOD: UserService.isDeckFavorited took 11091ms
2025-07-19 01:25:48.934 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [mapToDeckResponse] [11144ms] [] SLOW_SERVICE_METHOD: DeckService.mapToDeckResponse took 11144ms
2025-07-19 01:25:48.934 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [11208ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 11208ms
2025-07-19 01:32:33.315 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [386ms] [] DB_OPERATION: $Proxy194.findByEmail took 386ms
2025-07-19 01:33:05.219 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCurrentUser] [200ms] [] DB_OPERATION: $Proxy194.findById took 200ms
2025-07-19 01:34:13.728 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCardsByDeckId] [119ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 119ms
2025-07-19 01:36:36.941 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [238ms] [] DB_OPERATION: $Proxy194.findByEmail took 238ms
2025-07-19 01:36:36.941 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [238ms] [] DB_OPERATION: $Proxy194.findByEmail took 238ms
2025-07-19 01:36:36.943 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [240ms] [] DB_OPERATION: $Proxy194.findByEmail took 240ms
2025-07-19 01:36:36.945 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [224ms] [] DB_OPERATION: $Proxy194.findByEmail took 224ms
2025-07-19 01:36:37.329 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [365ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 365ms
2025-07-19 01:37:57.597 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [3828ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 3828ms
2025-07-19 01:37:57.719 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [111ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 111ms
2025-07-19 02:08:15.736 [ForkJoinPool.commonPool-worker-34] INFO  PERFORMANCE - 
                [] [111ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 111ms
2025-07-19 02:08:16.370 [ForkJoinPool.commonPool-worker-34] INFO  PERFORMANCE - 
                [] [470ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 470ms
2025-07-19 02:08:16.537 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1016ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1016ms
2025-07-19 02:08:16.540 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1040ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1040ms
2025-07-19 02:14:48.651 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [624ms] [] FAILED_ENDPOINT: StudySessionController.recordEnhancedCardPerformance failed after 624ms with error: TransactionSystemException
2025-07-19 02:18:46.968 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [357ms] [] DB_OPERATION: $Proxy194.findByEmail took 357ms
2025-07-19 02:18:47.437 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCurrentUserPreferences] [126ms] [] DB_OPERATION: $Proxy244.findByUser took 126ms
2025-07-19 02:18:47.768 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [createStudySession] [102ms] [] DB_OPERATION: $Proxy209.save took 102ms
2025-07-19 02:18:47.803 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCardsByDeckId] [183ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 183ms
2025-07-19 02:18:47.905 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [285ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 285ms
2025-07-19 02:18:52.195 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getCurrentUser] [4203ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 4203ms
2025-07-19 02:18:52.197 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getCurrentUser] [4203ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 4203ms
2025-07-19 12:08:46.299 [MessageBroker-13] INFO  PERFORMANCE - 
                [invalidateSearchCaches] [623ms] [] SERVICE_METHOD: SearchCacheService.invalidateSearchCaches took 623ms
2025-07-19 12:08:47.246 [MessageBroker-13] INFO  PERFORMANCE - 
                [invalidateSearchCaches] [777ms] [] SERVICE_METHOD: CacheUtilityService.silentClear took 777ms
2025-07-19 12:08:47.246 [MessageBroker-13] INFO  PERFORMANCE - 
                [invalidateSearchCaches] [777ms] [] SERVICE_METHOD: SearchCacheService.invalidateSearchCaches took 777ms
2025-07-19 12:08:51.234 [MessageBroker-16] INFO  PERFORMANCE - 
                [sendVerificationReminders] [180ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 180ms
2025-07-19 12:08:55.143 [ForkJoinPool.commonPool-worker-41] WARN  PERFORMANCE - 
                [] [4781ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 4781ms
2025-07-19 12:09:01.485 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [8146ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 8146ms
2025-07-19 12:09:01.499 [MessageBroker-16] WARN  PERFORMANCE - 
                [sendVerificationReminders] [10265ms] [] SLOW_DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 10265ms
2025-07-19 12:09:01.560 [ForkJoinPool.commonPool-worker-41] WARN  PERFORMANCE - 
                [] [6417ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 6417ms
2025-07-19 12:09:01.561 [MessageBroker-16] WARN  PERFORMANCE - 
                [sendVerificationReminders] [10510ms] [] SLOW_SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 10510ms
2025-07-19 12:09:01.599 [MessageBroker-12] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [7166ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 7166ms
2025-07-19 12:09:01.599 [MessageBroker-12] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [7171ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 7171ms
2025-07-19 12:09:01.799 [MessageBroker-12] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [16166ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 16166ms
2025-07-19 12:09:01.805 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [245ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 245ms
2025-07-19 12:09:01.837 [ForkJoinPool.commonPool-worker-42] INFO  PERFORMANCE - 
                [] [253ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 253ms
2025-07-19 12:09:02.040 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [232ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 232ms
2025-07-19 12:09:02.271 [MessageBroker-9] WARN  PERFORMANCE - 
                [generateDailyNotifications] [15952ms] [] SLOW_DB_OPERATION: $Proxy194.findAll took 15952ms
2025-07-19 12:09:02.272 [MessageBroker-9] WARN  PERFORMANCE - 
                [generateDailyNotifications] [16224ms] [] SLOW_SERVICE_METHOD: UserService.getAllUsers took 16224ms
2025-07-19 12:09:07.089 [ForkJoinPool.commonPool-worker-43] WARN  PERFORMANCE - 
                [] [5048ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWithImprovement took 5048ms
2025-07-19 12:09:07.093 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [5135ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 5135ms
2025-07-19 12:09:07.094 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [5136ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 5136ms
2025-07-19 12:09:07.130 [ForkJoinPool.commonPool-worker-42] WARN  PERFORMANCE - 
                [] [5136ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 5136ms
2025-07-19 12:09:07.132 [MessageBroker-9] WARN  PERFORMANCE - 
                [generateDailyNotifications] [4834ms] [] SLOW_DB_OPERATION: $Proxy207.findAllDueCardsForUser took 4834ms
2025-07-19 12:09:14.203 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [23843ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 23843ms
2025-07-19 12:09:14.350 [ForkJoinPool.commonPool-worker-42] WARN  PERFORMANCE - 
                [] [7220ms] [] SLOW_DB_OPERATION: $Proxy208.countDistinctTags took 7220ms
2025-07-19 12:09:14.408 [ForkJoinPool.commonPool-worker-43] WARN  PERFORMANCE - 
                [] [7319ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 7319ms
2025-07-19 12:09:14.473 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [267ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 267ms
2025-07-19 12:09:14.474 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [276ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 276ms
2025-07-19 12:09:14.557 [ForkJoinPool.commonPool-worker-42] INFO  PERFORMANCE - 
                [] [206ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 206ms
2025-07-19 12:09:15.020 [MessageBroker-9] ERROR PERFORMANCE - 
                [generateDailyNotifications] [7814ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 7814ms with error: ConversionFailedException
2025-07-19 12:09:21.248 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [35622ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 35622ms
2025-07-19 12:09:21.251 [MessageBroker-13] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [19297ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 19297ms
2025-07-19 12:09:21.305 [ForkJoinPool.commonPool-worker-41] WARN  PERFORMANCE - 
                [] [6831ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 6831ms
2025-07-19 12:09:21.376 [ForkJoinPool.commonPool-worker-42] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 121ms
2025-07-19 12:09:21.383 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [119ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 119ms
2025-07-19 12:09:21.403 [MessageBroker-9] INFO  PERFORMANCE - 
                [generateDailyNotifications] [133ms] [] DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser took 133ms
2025-07-19 12:09:21.414 [MessageBroker-9] WARN  PERFORMANCE - 
                [generateDailyNotifications] [35495ms] [] SLOW_SERVICE_METHOD: DashboardService.generateDailyNotifications took 35495ms
2025-07-19 12:09:21.426 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 121ms
2025-07-19 12:09:21.483 [ForkJoinPool.commonPool-worker-42] INFO  PERFORMANCE - 
                [] [106ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 106ms
2025-07-19 12:09:21.555 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [28216ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 28216ms
2025-07-19 12:09:21.567 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [35936ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 35936ms
2025-07-19 12:09:28.387 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [6676ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 6676ms
2025-07-19 12:09:28.409 [ForkJoinPool.commonPool-worker-42] WARN  PERFORMANCE - 
                [] [6705ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWithImprovement took 6705ms
2025-07-19 12:09:29.346 [ForkJoinPool.commonPool-worker-43] WARN  PERFORMANCE - 
                [] [814ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalStudyHours took 814ms
2025-07-19 12:09:29.372 [ForkJoinPool.commonPool-worker-41] WARN  PERFORMANCE - 
                [] [860ms] [] SLOW_DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 860ms
2025-07-19 12:09:29.521 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCurrentUser] [124ms] [] DB_OPERATION: $Proxy194.findById took 124ms
2025-07-19 12:09:29.524 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [178ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 178ms
2025-07-19 12:09:29.550 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [172ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 172ms
2025-07-19 12:09:29.697 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [114ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 114ms
2025-07-19 12:09:29.701 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCurrentUser] [112ms] [] DB_OPERATION: $Proxy194.findById took 112ms
2025-07-19 12:09:29.767 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 162ms
2025-07-19 12:09:29.883 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [116ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 116ms
2025-07-19 12:09:29.910 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getNotifications] [191ms] [] DB_OPERATION: $Proxy220.findByUserOrderByCreatedAtDesc took 191ms
2025-07-19 12:09:30.090 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [206ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 206ms
2025-07-19 12:09:30.130 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [405ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 405ms
2025-07-19 12:09:30.136 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [8560ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 8560ms
2025-07-19 12:09:30.141 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [8573ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 8573ms
2025-07-19 12:09:30.287 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [129ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 129ms
2025-07-19 12:09:30.328 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [204ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 204ms
2025-07-19 12:09:30.365 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [8655ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 8655ms
2025-07-19 12:09:30.378 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [9130ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 9130ms
2025-07-19 12:09:30.507 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [115ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 115ms
2025-07-19 12:09:31.063 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [415ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 415ms
2025-07-19 12:09:31.265 [MessageBroker-6] INFO  PERFORMANCE - 
                [periodicHealthCheck] [887ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 887ms
2025-07-19 12:09:31.373 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [108ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 108ms
2025-07-19 12:09:31.574 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [125ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 125ms
2025-07-19 12:09:31.607 [ForkJoinPool.commonPool-worker-43] INFO  PERFORMANCE - 
                [] [233ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 233ms
2025-07-19 12:09:31.685 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [1531ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1531ms
2025-07-19 12:09:31.689 [MessageBroker-2] WARN  PERFORMANCE - 
                [cacheWarming] [1547ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1547ms
2025-07-19 12:09:32.035 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [170ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 170ms
2025-07-19 12:09:32.117 [MessageBroker-6] INFO  PERFORMANCE - 
                [periodicHealthCheck] [839ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 839ms
2025-07-19 12:09:32.128 [MessageBroker-6] INFO  PERFORMANCE - 
                [periodicHealthCheck] [863ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 863ms
2025-07-19 12:09:32.668 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [199ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 199ms
2025-07-19 12:09:32.917 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [159ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 159ms
2025-07-19 12:09:33.142 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [115ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 115ms
2025-07-19 12:09:33.258 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [961ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 961ms
2025-07-19 12:09:33.261 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [973ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 973ms
2025-07-19 12:09:33.647 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [131ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 131ms
2025-07-19 12:09:33.887 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [124ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 124ms
2025-07-19 12:09:34.140 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [253ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 253ms
2025-07-19 12:09:34.202 [MessageBroker-1] INFO  PERFORMANCE - 
                [periodicHealthCheck] [898ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 898ms
2025-07-19 12:09:34.204 [MessageBroker-1] INFO  PERFORMANCE - 
                [periodicHealthCheck] [902ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 902ms
2025-07-19 12:09:34.703 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [201ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 201ms
2025-07-19 12:09:34.817 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [102ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 102ms
2025-07-19 12:09:36.138 [ForkJoinPool.commonPool-worker-41] WARN  PERFORMANCE - 
                [] [1321ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 1321ms
2025-07-19 12:09:36.303 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [109ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 109ms
2025-07-19 12:09:36.304 [MessageBroker-1] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2000ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2000ms
2025-07-19 12:09:36.306 [MessageBroker-1] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2003ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 2003ms
2025-07-19 12:09:36.508 [ForkJoinPool.commonPool-worker-41] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 121ms
2025-07-19 12:09:36.853 [MessageBroker-1] INFO  PERFORMANCE - 
                [periodicHealthCheck] [546ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 546ms
2025-07-19 12:09:36.854 [MessageBroker-1] INFO  PERFORMANCE - 
                [periodicHealthCheck] [547ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 547ms
2025-07-19 12:10:47.762 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [3ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 3ms with error: NullPointerException
2025-07-19 12:10:48.048 [MessageBroker-12] INFO  PERFORMANCE - 
                [sendVerificationReminders] [245ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 245ms
2025-07-19 12:10:48.048 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [245ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 245ms
2025-07-19 12:10:48.048 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [183ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 183ms
2025-07-19 12:10:48.053 [main] INFO  PERFORMANCE - 
                [] [107ms] [] DB_OPERATION: $Proxy211.count took 107ms
2025-07-19 12:10:48.053 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [188ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 188ms
2025-07-19 12:10:48.254 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [122ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 122ms
2025-07-19 12:10:48.254 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 119ms
2025-07-19 12:10:48.397 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [543ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 543ms
2025-07-19 12:10:48.398 [MessageBroker-7] INFO  PERFORMANCE - 
                [cacheWarming] [549ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 549ms
2025-07-19 12:10:48.398 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [642ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 642ms
2025-07-19 12:10:48.398 [MessageBroker-7] INFO  PERFORMANCE - 
                [cacheWarming] [642ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 642ms
2025-07-19 12:14:49.150 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getStudyActivityData] [154ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 154ms
2025-07-19 12:14:49.195 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserStudySessions] [169ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 169ms
2025-07-19 12:14:49.236 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getUserDecks] [113ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 113ms
2025-07-19 12:14:49.247 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getDashboardData] [110ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 110ms with error: ConversionFailedException
2025-07-19 12:14:49.268 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [161ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 161ms
2025-07-19 12:14:49.647 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getUserDecks] [533ms] [] SERVICE_METHOD: DeckService.getUserDecks took 533ms
2025-07-19 12:14:49.781 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDashboardData] [722ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 722ms
2025-07-19 12:16:30.348 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [119ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 119ms
2025-07-19 12:17:00.566 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getCardsByDeckId] [133ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 133ms
2025-07-19 12:17:00.566 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [141ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 141ms
2025-07-19 12:17:00.772 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [createStudySession] [177ms] [] DB_OPERATION: $Proxy209.save took 177ms
2025-07-19 12:18:02.737 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [645ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 645ms
2025-07-19 12:49:28.015 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCardsByDeckId] [116ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 116ms
2025-07-19 12:49:28.015 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [121ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 121ms
2025-07-19 12:49:40.778 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [168ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 168ms
2025-07-19 13:00:47.697 [ForkJoinPool.commonPool-worker-15] INFO  PERFORMANCE - 
                [] [145ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 145ms
2025-07-19 13:09:41.266 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [createStudySession] [133ms] [] DB_OPERATION: $Proxy209.save took 133ms
2025-07-19 13:09:41.374 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [238ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 238ms
2025-07-19 13:12:14.510 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy194.findByEmail took 162ms
2025-07-19 13:12:14.517 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [170ms] [] DB_OPERATION: $Proxy194.findByEmail took 170ms
2025-07-19 13:12:14.584 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [218ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 218ms
2025-07-19 13:12:18.908 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [108ms] [] DB_OPERATION: $Proxy214.findRecentByUserId took 108ms
2025-07-19 13:12:19.205 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [536ms] [] SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 536ms
2025-07-19 13:12:19.268 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [600ms] [] FAILED_ENDPOINT: StudySessionController.recordEnhancedCardPerformance failed after 600ms with error: TransactionSystemException
2025-07-19 13:35:48.239 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [788ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 788ms
2025-07-19 13:35:48.345 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [912ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 912ms
2025-07-19 13:35:48.346 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [929ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 929ms
2025-07-19 14:00:40.157 [MessageBroker-13] WARN  PERFORMANCE - 
                [cacheWarming] [7541ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 7541ms
2025-07-19 14:00:40.211 [ForkJoinPool.commonPool-worker-27] INFO  PERFORMANCE - 
                [] [280ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 280ms
2025-07-19 14:00:40.695 [ForkJoinPool.commonPool-worker-27] INFO  PERFORMANCE - 
                [] [483ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 483ms
2025-07-19 14:00:41.314 [ForkJoinPool.commonPool-worker-27] WARN  PERFORMANCE - 
                [] [619ms] [] SLOW_DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 619ms
2025-07-19 14:00:41.955 [MessageBroker-1] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2039ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2039ms
2025-07-19 14:00:41.978 [MessageBroker-1] WARN  PERFORMANCE - 
                [periodicHealthCheck] [9364ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 9364ms
2025-07-19 14:25:47.526 [ForkJoinPool.commonPool-worker-33] INFO  PERFORMANCE - 
                [] [180ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 180ms
2025-07-19 17:29:17.299 [MessageBroker-14] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [2ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 2ms with error: NullPointerException
2025-07-19 17:29:17.641 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [256ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 256ms
2025-07-19 17:29:17.641 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [256ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 256ms
2025-07-19 17:29:17.653 [main] INFO  PERFORMANCE - 
                [] [125ms] [] DB_OPERATION: $Proxy211.count took 125ms
2025-07-19 17:29:17.671 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [260ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 260ms
2025-07-19 17:29:17.671 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [260ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 260ms
2025-07-19 17:29:17.889 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [126ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 126ms
2025-07-19 17:29:17.889 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [124ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 124ms
2025-07-19 17:29:18.063 [MessageBroker-9] INFO  PERFORMANCE - 
                [cacheWarming] [673ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 673ms
2025-07-19 17:29:18.064 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [674ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 674ms
2025-07-19 17:29:18.064 [MessageBroker-9] INFO  PERFORMANCE - 
                [cacheWarming] [767ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 767ms
2025-07-19 17:29:18.064 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [767ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 767ms
2025-07-19 17:29:45.018 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCardsByDeckId] [265ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 265ms
2025-07-19 17:29:45.018 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [267ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 267ms
2025-07-19 17:29:45.031 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [createStudySession] [229ms] [] DB_OPERATION: $Proxy209.save took 229ms
2025-07-19 17:29:53.135 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [177ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 177ms
2025-07-19 17:30:22.005 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [23090ms] [] SLOW_DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 23090ms
2025-07-19 17:30:22.006 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [11927ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 11927ms
2025-07-19 17:30:22.043 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [23459ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 23459ms
2025-07-19 17:30:22.101 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [23518ms] [] FAILED_ENDPOINT: StudySessionController.recordEnhancedCardPerformance failed after 23518ms with error: TransactionSystemException
2025-07-19 17:39:17.559 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [124ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 124ms
2025-07-19 17:39:17.883 [MessageBroker-10] INFO  PERFORMANCE - 
                [periodicHealthCheck] [592ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 592ms
2025-07-19 17:42:02.503 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [2ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 2ms with error: NullPointerException
2025-07-19 17:42:02.841 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [296ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 296ms
2025-07-19 17:42:02.841 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [296ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 296ms
2025-07-19 17:42:02.843 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [258ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 258ms
2025-07-19 17:42:02.843 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [258ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 258ms
2025-07-19 17:42:02.849 [main] INFO  PERFORMANCE - 
                [] [166ms] [] DB_OPERATION: $Proxy211.count took 166ms
2025-07-19 17:42:02.985 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [141ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 141ms
2025-07-19 17:42:02.999 [main] INFO  PERFORMANCE - 
                [logSubscriptionStatistics] [134ms] [] DB_OPERATION: $Proxy194.countBySubscriptionStatus took 134ms
2025-07-19 17:42:03.013 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [131ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 131ms
2025-07-19 17:42:03.251 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [103ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 103ms
2025-07-19 17:42:03.436 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [132ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 132ms
2025-07-19 17:42:03.485 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [930ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 930ms
2025-07-19 17:42:03.487 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [991ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 991ms
2025-07-19 17:42:03.494 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [941ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 941ms
2025-07-19 17:42:03.496 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [1000ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1000ms
2025-07-19 17:42:08.137 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getStudyActivityData] [111ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 111ms
2025-07-19 17:42:08.146 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getUserStudySessions] [112ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 112ms
2025-07-19 17:42:08.173 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getDashboardData] [47ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 47ms with error: ConversionFailedException
2025-07-19 17:42:08.198 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [120ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 120ms
2025-07-19 17:42:08.199 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserDecks] [119ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 119ms
2025-07-19 17:42:08.428 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDashboardData] [150ms] [] DB_OPERATION: $Proxy209.getDeckStudyStatistics took 150ms
2025-07-19 17:42:08.600 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserDecks] [526ms] [] SERVICE_METHOD: DeckService.getUserDecks took 526ms
2025-07-19 17:42:08.608 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDashboardData] [532ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 532ms
2025-07-19 17:42:12.991 [MessageBroker-16] WARN  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [501ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 501ms
2025-07-19 17:42:12.991 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [501ms] [] SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 501ms
2025-07-19 17:42:12.993 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [146ms] [] DB_OPERATION: $Proxy194.findByEmail took 146ms
2025-07-19 17:42:13.001 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [512ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptionsOnStartup took 512ms
2025-07-19 17:42:13.768 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [97ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 97ms with error: ConversionFailedException
2025-07-19 17:42:26.895 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [987ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 987ms
2025-07-19 17:42:27.038 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [1132ms] [] SLOW_SERVICE_METHOD: DeckService.getEnhancedDeckById took 1132ms
2025-07-19 17:42:27.042 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [1137ms] [] ENDPOINT: DeckController.getEnhancedDeckById took 1137ms
2025-07-19 17:42:31.328 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [createStudySession] [145ms] [] DB_OPERATION: $Proxy209.save took 145ms
2025-07-19 17:42:35.234 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [114ms] [] DB_OPERATION: $Proxy214.findRecentByUserId took 114ms
2025-07-19 17:42:35.891 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [393ms] [] DB_OPERATION: $Proxy214.flush took 393ms
2025-07-19 17:42:35.987 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1000ms] [] SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 1000ms
2025-07-19 17:42:36.071 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [1088ms] [] ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 1088ms
2025-07-19 17:42:36.407 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getCardsByDeckId] [142ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 142ms
2025-07-19 17:44:22.694 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [20ms] [] FAILED_DB_OPERATION: $Proxy214.flush failed after 20ms with error: ConstraintViolationException
2025-07-19 17:44:22.804 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [6ms] [] FAILED_DB_OPERATION: $Proxy214.flush failed after 6ms with error: ConstraintViolationException
2025-07-19 17:44:23.018 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [4ms] [] FAILED_DB_OPERATION: $Proxy214.flush failed after 4ms with error: ConstraintViolationException
2025-07-19 17:44:23.020 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [419ms] [] FAILED_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance failed after 419ms with error: RuntimeException
2025-07-19 17:44:23.031 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [431ms] [] FAILED_ENDPOINT: StudySessionController.recordEnhancedCardPerformance failed after 431ms with error: RuntimeException
2025-07-19 17:47:02.939 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [196ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 196ms
2025-07-19 17:47:03.228 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [284ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 284ms
2025-07-19 17:47:03.357 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [129ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 129ms
2025-07-19 17:47:03.601 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [189ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 189ms
2025-07-19 17:47:03.914 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [230ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 230ms
2025-07-19 17:47:04.568 [ForkJoinPool.commonPool-worker-4] WARN  PERFORMANCE - 
                [] [650ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalStudyHours took 650ms
2025-07-19 17:47:05.159 [ForkJoinPool.commonPool-worker-4] WARN  PERFORMANCE - 
                [] [590ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 590ms
2025-07-19 17:47:05.752 [ForkJoinPool.commonPool-worker-4] WARN  PERFORMANCE - 
                [] [554ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 554ms
2025-07-19 17:47:05.950 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [197ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 197ms
2025-07-19 17:47:06.146 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [195ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 195ms
2025-07-19 17:47:06.753 [ForkJoinPool.commonPool-worker-4] WARN  PERFORMANCE - 
                [] [607ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 607ms
2025-07-19 17:47:06.763 [MessageBroker-15] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4231ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4231ms
2025-07-19 17:47:06.786 [MessageBroker-15] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4291ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 4291ms
2025-07-19 18:02:34.179 [MessageBroker-15] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-19 18:02:34.585 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [359ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 359ms
2025-07-19 18:02:34.586 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [360ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 360ms
2025-07-19 18:02:34.586 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [288ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 288ms
2025-07-19 18:02:34.586 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [288ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 288ms
2025-07-19 18:02:34.596 [main] INFO  PERFORMANCE - 
                [] [200ms] [] DB_OPERATION: $Proxy211.count took 200ms
2025-07-19 18:02:34.856 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [144ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 144ms
2025-07-19 18:02:34.856 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [144ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 144ms
2025-07-19 18:02:35.007 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [734ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 734ms
2025-07-19 18:02:35.007 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [744ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 744ms
2025-07-19 18:02:35.007 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [829ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 829ms
2025-07-19 18:02:35.007 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [829ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 829ms
2025-07-19 18:03:24.457 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [updateCurrentUserPreferences] [182ms] [] DB_OPERATION: $Proxy244.findByUser took 182ms
2025-07-19 18:03:27.645 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [createStudySession] [3402ms] [] SLOW_DB_OPERATION: $Proxy209.save took 3402ms
2025-07-19 18:03:27.648 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [createStudySession] [3435ms] [] SLOW_SERVICE_METHOD: StudySessionServiceExtension.createStudySession took 3435ms
2025-07-19 18:03:27.648 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [createStudySession] [3435ms] [] SLOW_SERVICE_METHOD: StudySessionService.createStudySession took 3435ms
2025-07-19 18:03:27.652 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [3441ms] [] SLOW_ENDPOINT: StudySessionController.createStudySession took 3441ms
2025-07-19 18:03:27.674 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCardsByDeckId] [3550ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 3550ms
2025-07-19 18:03:27.674 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [3554ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 3554ms
2025-07-19 18:03:27.676 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [3557ms] [] SLOW_SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 3557ms
2025-07-19 18:03:27.736 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCardsByDeckId] [3612ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 3612ms
2025-07-19 18:03:27.739 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [3618ms] [] SLOW_ENDPOINT: CardController.getCardsByDeckId took 3618ms
2025-07-19 18:03:27.760 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [3643ms] [] SLOW_ENDPOINT: DeckController.getDeckById took 3643ms
2025-07-19 18:03:36.954 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [2530ms] [] SLOW_DB_OPERATION: $Proxy214.flush took 2530ms
2025-07-19 18:03:44.723 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [7759ms] [] SLOW_DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 7759ms
2025-07-19 18:03:44.723 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [337ms] [] DB_OPERATION: $Proxy194.findByEmail took 337ms
2025-07-19 18:03:44.762 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [10608ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 10608ms
2025-07-19 18:03:44.771 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [] [10620ms] [] SLOW_ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 10620ms
2025-07-19 18:03:49.979 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getCardsByDeckId] [4974ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 4974ms
2025-07-19 18:03:50.004 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getCardsByDeckId] [4999ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 4999ms
2025-07-19 18:03:50.005 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [] [5001ms] [] SLOW_ENDPOINT: CardController.getCardsByDeckId took 5001ms
2025-07-19 18:04:34.655 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy194.findByEmail took 267ms
2025-07-19 18:04:35.305 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getCurrentUser] [646ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 646ms
2025-07-19 18:04:35.305 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCurrentUser] [646ms] [] SERVICE_METHOD: UserService.getCurrentUser took 646ms
2025-07-19 18:04:43.280 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [88ms] [] FAILED_DB_OPERATION: $Proxy214.flush failed after 88ms with error: ConstraintViolationException
2025-07-19 18:04:43.392 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [5ms] [] FAILED_DB_OPERATION: $Proxy214.flush failed after 5ms with error: ConstraintViolationException
2025-07-19 18:04:43.611 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [4ms] [] FAILED_DB_OPERATION: $Proxy214.flush failed after 4ms with error: ConstraintViolationException
2025-07-19 18:04:43.621 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [recordEnhancedCardPerformance] [486ms] [] FAILED_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance failed after 486ms with error: RuntimeException
2025-07-19 18:04:43.633 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [499ms] [] FAILED_ENDPOINT: StudySessionController.recordEnhancedCardPerformance failed after 499ms with error: RuntimeException
2025-07-19 18:07:38.112 [MessageBroker-11] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2647ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 2647ms
2025-07-19 18:27:24.899 [MessageBroker-15] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-19 18:27:25.249 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [264ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 264ms
2025-07-19 18:27:25.249 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [264ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 264ms
2025-07-19 18:27:25.251 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [236ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 236ms
2025-07-19 18:27:25.251 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [236ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 236ms
2025-07-19 18:27:25.262 [main] INFO  PERFORMANCE - 
                [] [141ms] [] DB_OPERATION: $Proxy211.count took 141ms
2025-07-19 18:27:25.465 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [124ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 124ms
2025-07-19 18:27:25.465 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [127ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 127ms
2025-07-19 18:27:25.629 [MessageBroker-10] INFO  PERFORMANCE - 
                [periodicHealthCheck] [638ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 638ms
2025-07-19 18:27:25.631 [MessageBroker-10] INFO  PERFORMANCE - 
                [periodicHealthCheck] [734ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 734ms
2025-07-19 18:27:25.631 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [641ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 641ms
2025-07-19 18:27:25.634 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [737ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 737ms
2025-07-19 18:29:20.514 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getCardsByDeckId] [310ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 310ms
2025-07-19 18:29:20.595 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [createStudySession] [222ms] [] DB_OPERATION: $Proxy209.save took 222ms
2025-07-19 18:29:20.595 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [createStudySession] [222ms] [] DB_OPERATION: $Proxy209.save took 222ms
2025-07-19 18:29:22.806 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [180ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 180ms
2025-07-19 18:29:34.167 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [109ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 109ms
2025-07-19 18:30:16.934 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [128ms] [] DB_OPERATION: $Proxy214.findRecentByUserId took 128ms
2025-07-19 18:30:17.479 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [271ms] [] DB_OPERATION: $Proxy214.flush took 271ms
2025-07-19 18:30:17.512 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [833ms] [] SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 833ms
2025-07-19 18:30:17.706 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCardsByDeckId] [103ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 103ms
2025-07-19 18:33:21.179 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [104ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 104ms
2025-07-19 18:33:45.104 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [773ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 773ms
2025-07-19 18:33:49.659 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserDecks] [204ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 204ms
2025-07-19 18:33:49.683 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [237ms] [] DB_OPERATION: $Proxy209.findByUserWithFilters took 237ms
2025-07-19 18:33:51.281 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [1453ms] [] SLOW_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized took 1453ms
2025-07-19 18:33:51.516 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [144ms] [] DB_OPERATION: $Proxy209.getAggregatedStatisticsForDateRange took 144ms
2025-07-19 18:33:51.579 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getUserDecks] [2163ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 2163ms
2025-07-19 18:33:51.583 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [2211ms] [] SLOW_ENDPOINT: DeckController.getUserDecks took 2211ms
2025-07-19 18:33:52.141 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [328ms] [] DB_OPERATION: $Proxy209.getAccuracyByHourOfDay took 328ms
2025-07-19 18:33:52.147 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [2731ms] [] SLOW_SERVICE_METHOD: StudySessionService.getEnhancedStudySessions took 2731ms
2025-07-19 18:33:52.147 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [] [2740ms] [] SLOW_ENDPOINT: StudySessionController.getEnhancedStudySessions took 2740ms
2025-07-19 18:34:15.110 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [141ms] [] DB_OPERATION: $Proxy209.getDeckStudyStatistics took 141ms
2025-07-19 18:34:16.464 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [1175ms] [] SLOW_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized took 1175ms
2025-07-19 18:34:18.161 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [1598ms] [] SLOW_DB_OPERATION: $Proxy206.findByCreator took 1598ms
2025-07-19 18:34:18.207 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [4358ms] [] SLOW_SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 4358ms
2025-07-19 18:34:18.209 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [4393ms] [] SLOW_ENDPOINT: StudySessionController.getStudyStatistics took 4393ms
2025-07-19 18:35:25.621 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getUserDecks] [11479ms] [] SLOW_SERVICE_METHOD: UserService.isDeckFavorited took 11479ms
2025-07-19 18:35:25.842 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getUserDecks] [11768ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 11768ms
2025-07-19 18:35:25.846 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [11774ms] [] SLOW_ENDPOINT: DeckController.getUserDecks took 11774ms
2025-07-19 18:52:25.271 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [163ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 163ms
2025-07-19 18:52:25.624 [MessageBroker-16] INFO  PERFORMANCE - 
                [periodicHealthCheck] [605ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 605ms
2025-07-19 18:52:25.637 [MessageBroker-16] INFO  PERFORMANCE - 
                [periodicHealthCheck] [744ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 744ms
2025-07-19 19:01:46.181 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [780ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 780ms
2025-07-19 19:02:25.447 [MessageBroker-11] INFO  PERFORMANCE - 
                [periodicHealthCheck] [548ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 548ms
2025-07-19 19:02:44.693 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [132ms] [] DB_OPERATION: $Proxy194.findByEmail took 132ms
2025-07-19 19:02:44.849 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCurrentUser] [136ms] [] DB_OPERATION: $Proxy194.findById took 136ms
2025-07-19 19:02:45.159 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [159ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 159ms
2025-07-19 19:02:45.263 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [104ms] [] DB_OPERATION: $Proxy207.countByDeckId took 104ms
2025-07-19 19:02:45.717 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [791ms] [] SERVICE_METHOD: DeckService.getUserDecks took 791ms
2025-07-19 19:02:47.069 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getUserDecks] [1137ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 1137ms
2025-07-19 19:02:47.083 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [1155ms] [] ENDPOINT: DeckController.getUserDecks took 1155ms
2025-07-19 19:02:48.258 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getUserDecks] [755ms] [] SERVICE_METHOD: DeckService.getUserDecks took 755ms
2025-07-19 19:02:59.856 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getTrendingDecks] [437ms] [] DB_OPERATION: $Proxy206.findTrendingDecks took 437ms
2025-07-19 19:02:59.868 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getTrendingDecks] [521ms] [] SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks took 521ms
2025-07-19 19:03:25.370 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCurrentUser] [292ms] [] DB_OPERATION: $Proxy194.findById took 292ms
2025-07-19 19:03:43.418 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [114ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 114ms
2025-07-19 19:03:44.013 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getEnhancedDeckById] [516ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWhoStudiedDeck took 516ms
2025-07-19 19:03:44.233 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [121ms] [] DB_OPERATION: DeckRepositoryCustomImpl.findRelatedDecksByTagsUsingDeckTags took 121ms
2025-07-19 19:03:44.258 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [183ms] [] DB_OPERATION: $Proxy206.findRelatedDecksByTags took 183ms
2025-07-19 19:03:44.295 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [991ms] [] SERVICE_METHOD: DeckService.getEnhancedDeckById took 991ms
2025-07-19 19:03:44.308 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [1014ms] [] ENDPOINT: DeckController.getEnhancedDeckById took 1014ms
2025-07-19 19:07:35.379 [ForkJoinPool.commonPool-worker-13] WARN  PERFORMANCE - 
                [] [10222ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 10222ms
2025-07-19 19:07:35.384 [ForkJoinPool.commonPool-worker-15] INFO  PERFORMANCE - 
                [] [161ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 161ms
2025-07-19 19:07:35.417 [ForkJoinPool.commonPool-worker-14] WARN  PERFORMANCE - 
                [] [5197ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 5197ms
2025-07-19 19:07:35.490 [MessageBroker-9] WARN  PERFORMANCE - 
                [periodicHealthCheck] [10410ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 10410ms
2025-07-19 19:07:35.499 [MessageBroker-9] WARN  PERFORMANCE - 
                [periodicHealthCheck] [10604ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 10604ms
2025-07-19 19:07:35.641 [ForkJoinPool.commonPool-worker-14] INFO  PERFORMANCE - 
                [] [120ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 120ms
2025-07-19 19:10:05.229 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [257ms] [] DB_OPERATION: $Proxy194.findByEmail took 257ms
2025-07-19 19:12:30.849 [ForkJoinPool.commonPool-worker-16] WARN  PERFORMANCE - 
                [] [5650ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 5650ms
2025-07-19 19:12:30.862 [ForkJoinPool.commonPool-worker-17] WARN  PERFORMANCE - 
                [] [641ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 641ms
2025-07-19 19:12:30.993 [MessageBroker-13] WARN  PERFORMANCE - 
                [periodicHealthCheck] [6095ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 6095ms
2025-07-19 19:12:30.995 [MessageBroker-13] WARN  PERFORMANCE - 
                [periodicHealthCheck] [6113ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 6113ms
2025-07-19 19:16:38.782 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-19 19:16:39.122 [MessageBroker-12] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [285ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 285ms
2025-07-19 19:16:39.122 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [285ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 285ms
2025-07-19 19:16:39.122 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [237ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 237ms
2025-07-19 19:16:39.122 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [237ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 237ms
2025-07-19 19:16:39.131 [main] INFO  PERFORMANCE - 
                [] [144ms] [] DB_OPERATION: $Proxy211.count took 144ms
2025-07-19 19:16:39.378 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [127ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 127ms
2025-07-19 19:16:39.378 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [122ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 122ms
2025-07-19 19:16:39.560 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [716ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 716ms
2025-07-19 19:16:39.562 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [718ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 718ms
2025-07-19 19:16:39.562 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [782ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 782ms
2025-07-19 19:16:39.564 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [784ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 784ms
2025-07-19 19:16:50.800 [MessageBroker-16] WARN  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [1370ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 1370ms
2025-07-19 19:16:50.800 [MessageBroker-16] WARN  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [1370ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 1370ms
2025-07-19 19:16:51.024 [MessageBroker-16] WARN  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [2155ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptionsOnStartup took 2155ms
2025-07-19 19:19:43.176 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCardsByDeckId] [333ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 333ms
2025-07-19 19:19:50.568 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [createStudySession] [265ms] [] DB_OPERATION: $Proxy209.save took 265ms
2025-07-19 19:19:55.887 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [145ms] [] DB_OPERATION: $Proxy214.flush took 145ms
2025-07-19 19:19:55.914 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [532ms] [] SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 532ms
2025-07-19 19:20:04.609 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [261ms] [] DB_OPERATION: $Proxy194.findByEmail took 261ms
2025-07-19 19:20:05.353 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getCardsByDeckId] [291ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 291ms
2025-07-19 19:20:29.051 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [150ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 150ms
2025-07-19 19:23:04.204 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [204ms] [] DB_OPERATION: $Proxy194.findByEmail took 204ms
2025-07-19 19:23:44.296 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [104ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 104ms
2025-07-19 19:24:34.386 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [112ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 112ms
2025-07-19 19:25:06.331 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [137ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 137ms
2025-07-19 19:26:43.940 [ForkJoinPool.commonPool-worker-5] WARN  PERFORMANCE - 
                [] [5028ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 5028ms
2025-07-19 19:26:44.170 [ForkJoinPool.commonPool-worker-6] INFO  PERFORMANCE - 
                [] [255ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 255ms
2025-07-19 19:26:44.179 [ForkJoinPool.commonPool-worker-5] INFO  PERFORMANCE - 
                [] [223ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 223ms
2025-07-19 19:26:48.920 [MessageBroker-16] WARN  PERFORMANCE - 
                [periodicHealthCheck] [10139ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 10139ms
2025-07-19 19:26:48.921 [MessageBroker-16] WARN  PERFORMANCE - 
                [periodicHealthCheck] [10156ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 10156ms
2025-07-19 19:26:49.350 [ForkJoinPool.commonPool-worker-6] WARN  PERFORMANCE - 
                [] [5179ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 5179ms
2025-07-19 19:26:51.826 [ForkJoinPool.commonPool-worker-6] WARN  PERFORMANCE - 
                [] [2454ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 2454ms
2025-07-19 19:34:55.871 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [103ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 103ms
2025-07-19 19:35:16.451 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [303ms] [] DB_OPERATION: $Proxy207.findById took 303ms
2025-07-19 19:35:16.824 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [373ms] [] DB_OPERATION: $Proxy194.findById took 373ms
2025-07-19 19:35:17.328 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1180ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 1180ms
2025-07-19 19:35:17.332 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [1188ms] [] ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 1188ms
2025-07-19 19:35:19.313 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [107ms] [] DB_OPERATION: $Proxy194.findByEmail took 107ms
2025-07-19 19:35:19.504 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [176ms] [] DB_OPERATION: $Proxy194.findById took 176ms
2025-07-19 19:35:19.504 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getCurrentUser] [176ms] [] DB_OPERATION: $Proxy194.findById took 176ms
2025-07-19 19:35:19.913 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [hasActiveSubscription] [102ms] [] DB_OPERATION: $Proxy194.findById took 102ms
2025-07-19 19:35:19.944 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [118ms] [] DB_OPERATION: $Proxy214.getPerformanceStatsByCardId took 118ms
2025-07-19 19:35:20.127 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [170ms] [] DB_OPERATION: $Proxy214.save took 170ms
2025-07-19 19:35:20.186 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCardsByDeckId] [268ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 268ms
2025-07-19 19:35:20.267 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [440ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 440ms
2025-07-19 19:35:20.353 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [226ms] [] DB_OPERATION: $Proxy214.flush took 226ms
2025-07-19 19:35:20.510 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [146ms] [] DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 146ms
2025-07-19 19:35:20.528 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1013ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 1013ms
2025-07-19 19:35:20.533 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [mapToDeckResponse] [207ms] [] DB_OPERATION: $Proxy207.countByDeckId took 207ms
2025-07-19 19:35:20.533 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCardsByDeckId] [345ms] [] DB_OPERATION: $Proxy207.findByDeckId took 345ms
2025-07-19 19:35:20.534 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [1027ms] [] ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 1027ms
2025-07-19 19:35:20.555 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCardsByDeckId] [638ms] [] SERVICE_METHOD: CardService.getCardsByDeckId took 638ms
2025-07-19 19:39:24.553 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserDecks] [178ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 178ms
2025-07-19 19:39:27.262 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [212ms] [] DB_OPERATION: DeckRepositoryCustomImpl.findRelatedDecksByTagsUsingDeckTags took 212ms
2025-07-19 19:39:27.273 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedDeckById] [244ms] [] DB_OPERATION: $Proxy206.findRelatedDecksByTags took 244ms
2025-07-19 19:39:56.769 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [144ms] [] DB_OPERATION: $Proxy194.findByEmail took 144ms
2025-07-19 19:39:56.792 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [165ms] [] DB_OPERATION: $Proxy194.findByEmail took 165ms
2025-07-19 19:39:56.816 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [197ms] [] DB_OPERATION: $Proxy194.findByEmail took 197ms
2025-07-19 19:39:56.976 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [113ms] [] DB_OPERATION: $Proxy194.findById took 113ms
2025-07-19 19:39:57.853 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getCardsByDeckId] [984ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 984ms
2025-07-19 19:39:57.887 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getCardsByDeckId] [1018ms] [] SLOW_SERVICE_METHOD: CardService.getCardsByDeckId took 1018ms
2025-07-19 19:39:57.890 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [1022ms] [] ENDPOINT: CardController.getCardsByDeckId took 1022ms
2025-07-19 19:46:05.287 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [8301ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 8301ms
2025-07-19 19:46:05.288 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [8315ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 8315ms
2025-07-19 19:47:23.605 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCurrentUser] [376ms] [] DB_OPERATION: $Proxy194.findById took 376ms
2025-07-19 19:47:46.919 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getNewlyCreatedDecks] [252ms] [] FAILED_DB_OPERATION: $Proxy206.findByIsPublicTrueAndDeletedFalseOrderByCreatedAtDesc failed after 252ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:47:46.920 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getNewlyCreatedDecks] [266ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getNewlyCreatedDecks failed after 266ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:47:46.921 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [269ms] [] FAILED_ENDPOINT: DiscoveryController.getNewDecks failed after 269ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:30.041 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [109ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 109ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:30.041 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [121ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 121ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:30.041 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [123ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 123ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:57.629 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [12ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 12ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:57.630 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [17ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 17ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:57.631 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [18ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 18ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:58.786 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [searchDecks] [7ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 7ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:58.786 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [searchDecks] [10ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 10ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:48:58.786 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [10ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 10ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:49:00.904 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [11ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 11ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:49:00.905 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [16ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:49:00.905 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [16ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:49:01.360 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [25ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 25ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:01.361 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [30ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 30ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:01.361 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [30ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 30ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:02.433 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [20ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 20ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:02.433 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [24ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 24ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:02.435 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [26ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 26ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:04.572 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [19ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 19ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:04.572 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [22ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 22ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:04.572 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [22ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 22ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:08.691 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [searchDecks] [17ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 17ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:08.691 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [searchDecks] [21ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 21ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:49:08.692 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [] [23ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 23ms with error: InvalidDataAccessApiUsageException
2025-07-19 19:51:33.605 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [searchDecks] [12ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 12ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:33.606 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [searchDecks] [17ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 17ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:33.608 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [19ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 19ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:34.777 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [searchDecks] [11ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 11ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:34.778 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [searchDecks] [16ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:34.778 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [16ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:36.923 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [11ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 11ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:36.924 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [16ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:36.924 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [16ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:40.994 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [9ms] [] FAILED_DB_OPERATION: $Proxy206.advancedSearch failed after 9ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:40.994 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [12ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 12ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:51:40.995 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [13ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 13ms with error: InvalidDataAccessResourceUsageException
2025-07-19 19:56:39.051 [ForkJoinPool.commonPool-worker-17] INFO  PERFORMANCE - 
                [] [170ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 170ms
2025-07-19 19:56:39.379 [MessageBroker-15] INFO  PERFORMANCE - 
                [periodicHealthCheck] [513ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 513ms
2025-07-19 19:56:39.386 [MessageBroker-15] INFO  PERFORMANCE - 
                [periodicHealthCheck] [610ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 610ms
2025-07-19 19:57:55.209 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [322ms] [] DB_OPERATION: $Proxy194.findByEmail took 322ms
2025-07-19 19:58:12.810 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [113ms] [] DB_OPERATION: $Proxy194.findByEmail took 113ms
2025-07-19 19:58:13.813 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [102ms] [] DB_OPERATION: $Proxy194.findByEmail took 102ms
2025-07-19 19:58:14.332 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy194.findByEmail took 101ms
2025-07-19 19:58:24.413 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [483ms] [] DB_OPERATION: $Proxy194.findByEmail took 483ms
2025-07-19 19:58:26.528 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [2018ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 2018ms
2025-07-19 19:58:39.919 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [2738ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 2738ms
2025-07-19 19:59:10.825 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy194.findByEmail took 101ms
2025-07-19 19:59:16.167 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [126ms] [] DB_OPERATION: $Proxy194.findByEmail took 126ms
2025-07-19 19:59:32.839 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [7469ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 7469ms
2025-07-19 19:59:32.852 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [244ms] [] DB_OPERATION: $Proxy194.findByEmail took 244ms
2025-07-19 19:59:32.852 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [238ms] [] DB_OPERATION: $Proxy194.findByEmail took 238ms
2025-07-19 19:59:32.855 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [239ms] [] DB_OPERATION: $Proxy194.findByEmail took 239ms
2025-07-19 19:59:33.414 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCardsForReview] [344ms] [] DB_OPERATION: $Proxy207.findAllDueCardsForUser took 344ms
2025-07-19 19:59:33.414 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [331ms] [] DB_OPERATION: $Proxy206.findByCreatorAndDeletedFalse took 331ms
2025-07-19 19:59:33.618 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getDailyGoalProgress] [540ms] [] SLOW_DB_OPERATION: $Proxy209.findByUserAndDateRange took 540ms
2025-07-19 19:59:33.620 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getUserDecks] [540ms] [] SLOW_DB_OPERATION: $Proxy206.findByCreatorWithFilters took 540ms
2025-07-19 19:59:33.653 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getStudyActivityData] [577ms] [] SLOW_DB_OPERATION: $Proxy209.findByUserAndDateRange took 577ms
2025-07-19 19:59:33.709 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getDailyGoalProgress] [680ms] [] SERVICE_METHOD: DashboardService.getDailyGoalProgress took 680ms
2025-07-19 19:59:33.717 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getUserStudySessions] [630ms] [] SLOW_DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 630ms
2025-07-19 19:59:33.736 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCardsForReview] [707ms] [] SERVICE_METHOD: DashboardService.getCardsForReview took 707ms
2025-07-19 19:59:33.737 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getStudyActivityData] [708ms] [] SERVICE_METHOD: DashboardService.getStudyActivityData took 708ms
2025-07-19 19:59:33.743 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserStudySessions] [709ms] [] SERVICE_METHOD: StudySessionService.getUserStudySessions took 709ms
2025-07-19 19:59:33.745 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [108ms] [] DB_OPERATION: $Proxy207.countByDeckId took 108ms
2025-07-19 19:59:33.960 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [137ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 137ms
2025-07-19 19:59:34.051 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [979ms] [] SERVICE_METHOD: DeckService.getUserDecks took 979ms
2025-07-19 19:59:34.054 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [1074ms] [] ENDPOINT: DeckController.getUserDecks took 1074ms
2025-07-19 19:59:34.399 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [972ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 972ms with error: ConversionFailedException
2025-07-19 19:59:34.971 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [247ms] [] DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 247ms
2025-07-19 19:59:40.768 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [5829ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 5829ms
2025-07-19 19:59:40.779 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDashboardData] [5773ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckId took 5773ms
2025-07-19 19:59:40.971 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDashboardData] [7942ms] [] SLOW_SERVICE_METHOD: DashboardService.getDashboardData took 7942ms
2025-07-19 19:59:40.971 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [7961ms] [] SLOW_ENDPOINT: DashboardController.getDashboardData took 7961ms
2025-07-19 20:01:17.890 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-19 20:01:18.222 [MessageBroker-14] INFO  PERFORMANCE - 
                [sendVerificationReminders] [216ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 216ms
2025-07-19 20:01:18.222 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [217ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 217ms
2025-07-19 20:01:18.222 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [189ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 189ms
2025-07-19 20:01:18.222 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [189ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 189ms
2025-07-19 20:01:18.229 [main] INFO  PERFORMANCE - 
                [] [115ms] [] DB_OPERATION: $Proxy211.count took 115ms
2025-07-19 20:01:18.411 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [104ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 104ms
2025-07-19 20:01:18.411 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [104ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 104ms
2025-07-19 20:01:18.563 [MessageBroker-6] INFO  PERFORMANCE - 
                [periodicHealthCheck] [551ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 551ms
2025-07-19 20:01:18.563 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [551ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 551ms
2025-07-19 20:01:18.565 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [677ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 677ms
2025-07-19 20:01:18.565 [MessageBroker-6] INFO  PERFORMANCE - 
                [periodicHealthCheck] [677ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 677ms
2025-07-19 20:01:28.627 [MessageBroker-16] WARN  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [755ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 755ms
2025-07-19 20:01:28.627 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [755ms] [] SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 755ms
2025-07-19 20:01:28.633 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptionsOnStartup] [763ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptionsOnStartup took 763ms
2025-07-19 20:06:18.403 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [529ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 529ms
2025-07-19 21:10:17.564 [MessageBroker-8] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [5ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 5ms with error: NullPointerException
2025-07-19 21:10:17.972 [MessageBroker-13] INFO  PERFORMANCE - 
                [sendVerificationReminders] [296ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 296ms
2025-07-19 21:10:18.020 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [288ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 288ms
2025-07-19 21:10:18.021 [MessageBroker-14] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [345ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 345ms
2025-07-19 21:10:18.023 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [292ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 292ms
2025-07-19 21:10:18.023 [main] INFO  PERFORMANCE - 
                [] [221ms] [] DB_OPERATION: $Proxy211.count took 221ms
2025-07-19 21:10:18.290 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [149ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 149ms
2025-07-19 21:10:18.290 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [159ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 159ms
2025-07-19 21:10:18.535 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [835ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 835ms
2025-07-19 21:10:18.535 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [835ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 835ms
2025-07-19 21:10:18.538 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [979ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 979ms
2025-07-19 21:10:18.538 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [979ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 979ms
2025-07-19 21:10:54.265 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getStudyActivityData] [128ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 128ms
2025-07-19 21:10:54.280 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserDecks] [125ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 125ms
2025-07-19 21:10:54.344 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [175ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 175ms
2025-07-19 21:10:54.355 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getUserStudySessions] [137ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 137ms
2025-07-19 21:10:54.384 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [136ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 136ms with error: ConversionFailedException
2025-07-19 21:10:54.693 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserDecks] [546ms] [] SERVICE_METHOD: DeckService.getUserDecks took 546ms
2025-07-19 21:10:54.764 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [112ms] [] DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 112ms
2025-07-19 21:10:55.129 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [344ms] [] DB_OPERATION: $Proxy207.findByNextReviewDateBetween took 344ms
2025-07-19 21:10:55.166 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [995ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 995ms
2025-07-19 21:10:57.689 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getUserDecks] [115ms] [] DB_OPERATION: $Proxy207.countByDeckId took 115ms
2025-07-19 21:10:57.704 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getCurrentUser] [106ms] [] DB_OPERATION: $Proxy194.findById took 106ms
2025-07-19 21:10:57.714 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getDailyGoalProgress] [129ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 129ms
2025-07-19 21:10:57.731 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getStudyActivityData] [144ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 144ms
2025-07-19 21:10:57.787 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [12ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 12ms with error: ConversionFailedException
2025-07-19 21:10:58.103 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [117ms] [] DB_OPERATION: $Proxy207.findByNextReviewDateBetween took 117ms
2025-07-19 21:10:58.117 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCurrentUser] [126ms] [] DB_OPERATION: $Proxy194.findById took 126ms
2025-07-19 21:10:58.373 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [252ms] [] DB_OPERATION: $Proxy207.countByDeckId took 252ms
2025-07-19 21:10:58.374 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getDashboardData] [641ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 641ms
2025-07-19 21:10:58.380 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [263ms] [] DB_OPERATION: $Proxy194.findById took 263ms
2025-07-19 21:13:17.305 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [284ms] [] DB_OPERATION: $Proxy194.findByEmail took 284ms
2025-07-19 21:14:06.157 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getCurrentUserPreferences] [1406ms] [] SLOW_DB_OPERATION: $Proxy222.save took 1406ms
2025-07-19 21:14:06.157 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getCurrentUserPreferences] [1542ms] [] SLOW_SERVICE_METHOD: NotificationPreferencesService.getCurrentUserPreferences took 1542ms
2025-07-19 21:14:06.164 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [1560ms] [] ENDPOINT: NotificationPreferencesController.getCurrentUserPreferences took 1560ms
2025-07-19 21:14:43.380 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCurrentUserPreferences] [437ms] [] DB_OPERATION: $Proxy222.findByUser took 437ms
2025-07-19 21:14:43.519 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [updateCurrentUserPreferences] [134ms] [] FAILED_DB_OPERATION: $Proxy222.save failed after 134ms with error: DataIntegrityViolationException
2025-07-19 21:14:43.520 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [updateCurrentUserPreferences] [137ms] [] FAILED_SERVICE_METHOD: NotificationPreferencesService.updateCurrentUserPreferences failed after 137ms with error: DataIntegrityViolationException
2025-07-19 21:18:09.502 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [201ms] [] DB_OPERATION: $Proxy194.findByEmail took 201ms
2025-07-19 21:20:17.958 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [127ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 127ms
2025-07-19 21:20:18.161 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [157ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 157ms
2025-07-19 21:20:18.402 [ForkJoinPool.commonPool-worker-4] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 121ms
2025-07-19 21:20:18.553 [MessageBroker-9] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1001ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1001ms
2025-07-19 21:20:18.555 [MessageBroker-9] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1018ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1018ms
2025-07-19 21:35:51.836 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [searchDecks] [193ms] [] DB_OPERATION: $Proxy206.advancedSearch took 193ms
2025-07-19 21:36:01.772 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:36:01.784 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [27ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 27ms with error: ServiceException
2025-07-19 21:36:01.788 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [44ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 44ms with error: ServiceException
2025-07-19 21:36:01.788 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [44ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 44ms with error: ServiceException
2025-07-19 21:36:09.970 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [searchDecks] [2ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 2ms with error: LazyInitializationException
2025-07-19 21:36:09.974 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [searchDecks] [13ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 13ms with error: ServiceException
2025-07-19 21:36:09.976 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [searchDecks] [26ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 26ms with error: ServiceException
2025-07-19 21:36:09.977 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [] [27ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 27ms with error: ServiceException
2025-07-19 21:36:11.043 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:36:11.047 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [11ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 11ms with error: ServiceException
2025-07-19 21:36:11.048 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [20ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 20ms with error: ServiceException
2025-07-19 21:36:11.049 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [21ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 21ms with error: ServiceException
2025-07-19 21:36:13.099 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [0ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 0ms with error: LazyInitializationException
2025-07-19 21:36:13.102 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [8ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 8ms with error: ServiceException
2025-07-19 21:36:13.105 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [18ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 18ms with error: ServiceException
2025-07-19 21:36:13.105 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [18ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 18ms with error: ServiceException
2025-07-19 21:36:17.173 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [0ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 0ms with error: LazyInitializationException
2025-07-19 21:36:17.178 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [11ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 11ms with error: ServiceException
2025-07-19 21:36:17.180 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [26ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 26ms with error: ServiceException
2025-07-19 21:36:17.180 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [26ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 26ms with error: ServiceException
2025-07-19 21:36:22.297 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:36:22.302 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [13ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 13ms with error: ServiceException
2025-07-19 21:36:22.303 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [23ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 23ms with error: ServiceException
2025-07-19 21:36:22.304 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [24ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 24ms with error: ServiceException
2025-07-19 21:36:23.762 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [searchDecks] [129ms] [] DB_OPERATION: $Proxy194.findById took 129ms
2025-07-19 21:36:23.818 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:36:23.847 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [262ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 262ms with error: ServiceException
2025-07-19 21:36:23.854 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [385ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 385ms with error: ServiceException
2025-07-19 21:36:23.856 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [388ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 388ms with error: ServiceException
2025-07-19 21:37:24.017 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-19 21:37:24.346 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [207ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 207ms
2025-07-19 21:37:24.348 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [209ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 209ms
2025-07-19 21:37:24.350 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [284ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 284ms
2025-07-19 21:37:24.350 [MessageBroker-9] INFO  PERFORMANCE - 
                [sendVerificationReminders] [284ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 284ms
2025-07-19 21:37:24.363 [main] INFO  PERFORMANCE - 
                [] [144ms] [] DB_OPERATION: $Proxy211.count took 144ms
2025-07-19 21:37:24.523 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [108ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 108ms
2025-07-19 21:37:24.523 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [108ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 108ms
2025-07-19 21:37:24.696 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [587ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 587ms
2025-07-19 21:37:24.696 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [585ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 585ms
2025-07-19 21:37:24.697 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [683ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 683ms
2025-07-19 21:37:24.698 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [684ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 684ms
2025-07-19 21:37:46.975 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [recordTokenValidation] [1320ms] [] SLOW_SERVICE_METHOD: JwtMetricsService.recordTokenValidation took 1320ms
2025-07-19 21:37:47.019 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [1367ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1367ms
2025-07-19 21:37:47.868 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getStudyActivityData] [794ms] [] SERVICE_METHOD: DashboardService.getStudyActivityData took 794ms
2025-07-19 21:37:48.323 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getUserStudySessions] [1167ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1167ms
2025-07-19 21:37:48.323 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getUserStudySessions] [1168ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1168ms
2025-07-19 21:37:48.324 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [1167ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1167ms
2025-07-19 21:37:48.350 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getUserDecks] [1211ms] [] SLOW_DB_OPERATION: $Proxy206.findByCreatorWithFilters took 1211ms
2025-07-19 21:37:48.390 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getEnhancedPopularDecks] [1229ms] [] SLOW_DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 1229ms
2025-07-19 21:37:48.399 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getEnhancedPopularDecks] [1242ms] [] SLOW_SERVICE_METHOD: DeckService.getEnhancedPopularDecks took 1242ms
2025-07-19 21:37:48.403 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [1266ms] [] ENDPOINT: DeckController.getEnhancedPopularDecks took 1266ms
2025-07-19 21:37:48.418 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getDashboardData] [45ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 45ms with error: ConversionFailedException
2025-07-19 21:37:48.445 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getUserStudySessions] [119ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 119ms
2025-07-19 21:37:48.461 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getUserStudySessions] [1306ms] [] SLOW_SERVICE_METHOD: StudySessionService.getUserStudySessions took 1306ms
2025-07-19 21:37:48.461 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [1306ms] [] ENDPOINT: StudySessionController.getUserStudySessions took 1306ms
2025-07-19 21:37:48.588 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getUserDecks] [1457ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 1457ms
2025-07-19 21:37:48.601 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [1492ms] [] ENDPOINT: DeckController.getUserDecks took 1492ms
2025-07-19 21:37:48.711 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDashboardData] [146ms] [] DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 146ms
2025-07-19 21:37:53.421 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getUserFavoriteDecks] [1295ms] [] SLOW_DB_OPERATION: $Proxy206.findFavoritesByUserId took 1295ms
2025-07-19 21:37:53.422 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getUserFavoriteDecks] [1301ms] [] SLOW_SERVICE_METHOD: DeckService.getUserFavoriteDecks took 1301ms
2025-07-19 21:37:53.423 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [1303ms] [] ENDPOINT: DeckController.getUserFavoriteDecks took 1303ms
2025-07-19 21:38:05.362 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [869ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 869ms
2025-07-19 21:38:05.976 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getPersonalizedRecommendations] [514ms] [] SLOW_DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 514ms
2025-07-19 21:38:12.666 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getPersonalizedRecommendations] [6686ms] [] FAILED_DB_OPERATION: DeckRepositoryCustomImpl.findUserFavoriteTagsUsingDeckTags failed after 6686ms with error: DataAccessException
2025-07-19 21:38:12.669 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [getPersonalizedRecommendations] [ms] [] SLOW_CUSTOM_QUERY: findUserFavoriteTagsUsingDeckTags took 6689ms
2025-07-19 21:38:12.672 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getPersonalizedRecommendations] [6695ms] [] FAILED_DB_OPERATION: $Proxy206.findUserFavoriteTags failed after 6695ms with error: DataAccessException
2025-07-19 21:38:12.672 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getPersonalizedRecommendations] [7276ms] [] FAILED_SERVICE_METHOD: DeckService.getPersonalizedRecommendations failed after 7276ms with error: DataAccessException
2025-07-19 21:38:12.676 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [7298ms] [] FAILED_ENDPOINT: DeckController.getPersonalizedRecommendations failed after 7298ms with error: DataAccessException
2025-07-19 21:38:17.204 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserDecks] [821ms] [] SERVICE_METHOD: DeckService.getUserDecks took 821ms
2025-07-19 21:38:21.888 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getCurrentUser] [111ms] [] DB_OPERATION: $Proxy194.findById took 111ms
2025-07-19 21:38:22.101 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [toggleFavoriteDeck] [198ms] [] DB_OPERATION: $Proxy206.findById took 198ms
2025-07-19 21:38:22.406 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [toggleFavoriteDeck] [48ms] [] FAILED_DB_OPERATION: $Proxy194.save failed after 48ms with error: RuntimeException
2025-07-19 21:38:22.407 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [toggleFavoriteDeck] [509ms] [] FAILED_SERVICE_METHOD: UserService.toggleFavoriteDeck failed after 509ms with error: RuntimeException
2025-07-19 21:38:22.408 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [511ms] [] FAILED_ENDPOINT: DeckController.toggleFavoriteDeck failed after 511ms with error: RuntimeException
2025-07-19 21:39:56.696 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [200ms] [] DB_OPERATION: $Proxy194.findByEmail took 200ms
2025-07-19 21:39:57.202 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [494ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 494ms
2025-07-19 21:39:57.222 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [514ms] [] SERVICE_METHOD: DeckService.getEnhancedPopularDecks took 514ms
2025-07-19 21:42:01.351 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:42:01.353 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [16ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 16ms with error: ServiceException
2025-07-19 21:42:01.354 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [25ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 25ms with error: ServiceException
2025-07-19 21:42:01.354 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [25ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 25ms with error: ServiceException
2025-07-19 21:44:29.790 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:44:29.793 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [searchDecks] [29ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 29ms with error: ServiceException
2025-07-19 21:44:29.797 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [searchDecks] [113ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 113ms with error: ServiceException
2025-07-19 21:44:29.798 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [114ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 114ms with error: ServiceException
2025-07-19 21:44:31.430 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [1ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 1ms with error: LazyInitializationException
2025-07-19 21:44:31.439 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [31ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 31ms with error: ServiceException
2025-07-19 21:44:31.442 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [82ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 82ms with error: ServiceException
2025-07-19 21:44:31.442 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [82ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 82ms with error: ServiceException
2025-07-19 21:44:33.602 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [0ms] [] FAILED_SERVICE_METHOD: UserService.mapToUserSummary failed after 0ms with error: LazyInitializationException
2025-07-19 21:44:33.610 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [25ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 25ms with error: ServiceException
2025-07-19 21:44:33.615 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [69ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 69ms with error: ServiceException
2025-07-19 21:44:33.615 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [69ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 69ms with error: ServiceException
2025-07-19 21:47:24.512 [MessageBroker-6] INFO  PERFORMANCE - 
                [periodicHealthCheck] [543ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 543ms
2025-07-19 21:54:32.212 [MessageBroker-14] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-19 21:54:32.619 [MessageBroker-12] INFO  PERFORMANCE - 
                [sendVerificationReminders] [317ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 317ms
2025-07-19 21:54:32.619 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [317ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 317ms
2025-07-19 21:54:32.620 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [264ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 264ms
2025-07-19 21:54:32.620 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [264ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 264ms
2025-07-19 21:54:32.628 [main] INFO  PERFORMANCE - 
                [] [176ms] [] DB_OPERATION: $Proxy211.count took 176ms
2025-07-19 21:54:32.867 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [161ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 161ms
2025-07-19 21:54:32.867 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [161ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 161ms
2025-07-19 21:54:33.005 [MessageBroker-11] INFO  PERFORMANCE - 
                [cacheWarming] [686ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 686ms
2025-07-19 21:54:33.005 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [686ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 686ms
2025-07-19 21:54:33.007 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [797ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 797ms
2025-07-19 21:54:33.007 [MessageBroker-11] INFO  PERFORMANCE - 
                [cacheWarming] [797ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 797ms
2025-07-19 21:55:08.059 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [searchDecks] [279ms] [] DB_OPERATION: $Proxy206.advancedSearch took 279ms
2025-07-19 21:59:32.398 [ForkJoinPool.commonPool-worker-3] INFO  PERFORMANCE - 
                [] [137ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 137ms
2025-07-19 22:00:14.702 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getTrendingDecks] [158ms] [] DB_OPERATION: $Proxy206.findTrendingDecks took 158ms
2025-07-19 22:02:37.504 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [307ms] [] DB_OPERATION: $Proxy194.findByEmail took 307ms
2025-07-19 22:04:58.764 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [searchDecks] [121ms] [] DB_OPERATION: $Proxy206.advancedSearch took 121ms
2025-07-19 22:14:32.466 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [160ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 160ms
2025-07-19 22:14:32.776 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [615ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 615ms
2025-07-19 22:16:09.320 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [searchDecks] [110ms] [] DB_OPERATION: $Proxy206.advancedSearch took 110ms
2025-07-19 22:23:45.705 [MessageBroker-5] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [4ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 4ms with error: NullPointerException
2025-07-19 22:23:46.066 [MessageBroker-12] INFO  PERFORMANCE - 
                [sendVerificationReminders] [330ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 330ms
2025-07-19 22:23:46.066 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [330ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 330ms
2025-07-19 22:23:46.068 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [276ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 276ms
2025-07-19 22:23:46.068 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [276ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 276ms
2025-07-19 22:23:46.077 [main] INFO  PERFORMANCE - 
                [] [132ms] [] DB_OPERATION: $Proxy211.count took 132ms
2025-07-19 22:23:46.310 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [130ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 130ms
2025-07-19 22:23:46.310 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [130ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 130ms
2025-07-19 22:23:46.495 [MessageBroker-13] INFO  PERFORMANCE - 
                [cacheWarming] [726ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 726ms
2025-07-19 22:23:46.495 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [724ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 724ms
2025-07-19 22:23:46.497 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [803ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 803ms
2025-07-19 22:23:46.497 [MessageBroker-13] INFO  PERFORMANCE - 
                [cacheWarming] [803ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 803ms
2025-07-19 22:24:06.782 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getStudyActivityData] [112ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 112ms
2025-07-19 22:24:06.794 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getDashboardData] [61ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 61ms with error: ConversionFailedException
2025-07-19 22:24:06.839 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserStudySessions] [103ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 103ms
2025-07-19 22:24:52.227 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [224ms] [] DB_OPERATION: $Proxy194.findByEmail took 224ms
2025-07-19 22:25:22.116 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCurrentUser] [103ms] [] DB_OPERATION: $Proxy194.findById took 103ms
2025-07-19 22:28:46.244 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [558ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 558ms
2025-07-19 22:30:42.065 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [176ms] [] DB_OPERATION: $Proxy194.findByEmail took 176ms
2025-07-19 22:39:19.614 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getDashboardData] [29ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 29ms with error: ConversionFailedException
2025-07-19 22:41:13.392 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [] [183ms] [] DB_OPERATION: $Proxy194.findByEmail took 183ms
2025-07-19 22:45:28.986 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [728ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 728ms
2025-07-19 22:45:28.986 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [728ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 728ms
2025-07-19 22:45:28.986 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [728ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 728ms
2025-07-19 22:45:28.986 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [] [728ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 728ms
2025-07-19 22:45:28.986 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [728ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 728ms
2025-07-19 22:45:29.223 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getCurrentUser] [104ms] [] DB_OPERATION: $Proxy194.findById took 104ms
2025-07-19 22:45:29.223 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCurrentUser] [104ms] [] DB_OPERATION: $Proxy194.findById took 104ms
2025-07-19 22:45:29.223 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCurrentUser] [104ms] [] DB_OPERATION: $Proxy194.findById took 104ms
2025-07-19 22:45:29.224 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getCurrentUser] [104ms] [] DB_OPERATION: $Proxy194.findById took 104ms
2025-07-19 22:45:29.223 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getCurrentUser] [104ms] [] DB_OPERATION: $Proxy194.findById took 104ms
2025-07-19 22:45:29.227 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getCurrentUser] [108ms] [] DB_OPERATION: $Proxy194.findById took 108ms
2025-07-19 22:45:29.411 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getDashboardData] [134ms] [] DB_OPERATION: $Proxy206.findByCreatorAndDeletedFalse took 134ms
2025-07-19 22:45:29.458 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDailyGoalProgress] [193ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 193ms
2025-07-19 22:45:29.463 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getUserStudySessions] [192ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 192ms
2025-07-19 22:45:29.470 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getStudyActivityData] [188ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 188ms
2025-07-19 22:45:29.518 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getUserDecks] [224ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 224ms
2025-07-19 22:45:35.358 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [getUserDecks] [5836ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckId took 5836ms
2025-07-19 22:45:35.382 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getDashboardData] [5971ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 5971ms with error: ConversionFailedException
2025-07-19 22:45:35.389 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [getUserDecks] [6099ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 6099ms
2025-07-19 22:45:35.391 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [6114ms] [] SLOW_ENDPOINT: DeckController.getUserDecks took 6114ms
2025-07-19 22:45:35.599 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getDashboardData] [6347ms] [] SLOW_SERVICE_METHOD: DashboardService.getDashboardData took 6347ms
2025-07-19 22:45:35.599 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [6349ms] [] SLOW_ENDPOINT: DashboardController.getDashboardData took 6349ms
2025-07-19 23:01:52.281 [MessageBroker-12] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-19 23:01:53.280 [MessageBroker-7] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1001ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1001ms
2025-07-19 23:01:55.286 [MessageBroker-11] WARN  PERFORMANCE - 
                [sendVerificationReminders] [2654ms] [] SLOW_DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 2654ms
2025-07-19 23:01:55.336 [main] WARN  PERFORMANCE - 
                [] [649ms] [] SLOW_DB_OPERATION: $Proxy211.count took 649ms
2025-07-19 23:01:55.482 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [172ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 172ms
2025-07-19 23:01:55.540 [MessageBroker-11] WARN  PERFORMANCE - 
                [sendVerificationReminders] [2958ms] [] SLOW_SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 2958ms
2025-07-19 23:01:55.637 [ForkJoinPool.commonPool-worker-1] WARN  PERFORMANCE - 
                [] [2510ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 2510ms
2025-07-19 23:01:55.638 [main] INFO  PERFORMANCE - 
                [logSubscriptionStatistics] [298ms] [] DB_OPERATION: $Proxy194.countBySubscriptionStatus took 298ms
2025-07-19 23:01:55.649 [MessageBroker-15] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [2960ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 2960ms
2025-07-19 23:01:55.649 [MessageBroker-15] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [2983ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 2983ms
2025-07-19 23:01:55.745 [main] INFO  PERFORMANCE - 
                [logSubscriptionStatistics] [107ms] [] DB_OPERATION: $Proxy194.countBySubscriptionStatus took 107ms
2025-07-19 23:01:55.905 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 267ms
2025-07-19 23:01:55.906 [main] INFO  PERFORMANCE - 
                [logSubscriptionStatistics] [133ms] [] DB_OPERATION: $Proxy194.countBySubscriptionStatus took 133ms
2025-07-19 23:01:56.073 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [251ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 251ms
2025-07-19 23:01:56.109 [MessageBroker-15] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [3831ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 3831ms
2025-07-19 23:01:56.128 [main] INFO  PERFORMANCE - 
                [logSubscriptionStatistics] [124ms] [] DB_OPERATION: $Proxy194.countBySubscriptionStatus took 124ms
2025-07-19 23:01:56.362 [main] INFO  PERFORMANCE - 
                [logSubscriptionStatistics] [130ms] [] DB_OPERATION: $Proxy194.countUsersNeedingMigration took 130ms
2025-07-19 23:01:56.363 [main] WARN  PERFORMANCE - 
                [logSubscriptionStatistics] [1024ms] [] SLOW_SERVICE_METHOD: SubscriptionMigrationService.logSubscriptionStatistics took 1024ms
2025-07-19 23:01:56.638 [ForkJoinPool.commonPool-worker-1] WARN  PERFORMANCE - 
                [] [591ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 591ms
2025-07-19 23:01:56.988 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [345ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 345ms
2025-07-19 23:01:57.390 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [4549ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4549ms
2025-07-19 23:01:57.393 [MessageBroker-8] WARN  PERFORMANCE - 
                [cacheWarming] [5114ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 5114ms
2025-07-19 23:08:21.144 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getStudyActivityData] [125ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 125ms
2025-07-19 23:08:21.167 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserDecks] [103ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 103ms
2025-07-19 23:08:21.174 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getUserStudySessions] [119ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 119ms
2025-07-19 23:08:21.187 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [128ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 128ms
2025-07-19 23:08:21.274 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getDashboardData] [159ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 159ms with error: InvalidDataAccessResourceUsageException
2025-07-19 23:08:21.570 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getDashboardData] [520ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 520ms
2025-07-19 23:08:30.208 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [19ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 19ms with error: InvalidDataAccessResourceUsageException
2025-07-19 23:08:54.297 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [409ms] [] DB_OPERATION: $Proxy194.findByEmail took 409ms
2025-07-19 23:08:54.448 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [578ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 578ms
2025-07-19 23:08:54.448 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [561ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 561ms
2025-07-19 23:08:54.664 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getUserDecks] [167ms] [] DB_OPERATION: $Proxy207.countByDeckId took 167ms
2025-07-19 23:08:54.906 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [269ms] [] DB_OPERATION: $Proxy209.findByUserWithFilters took 269ms
2025-07-19 23:08:56.433 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [1356ms] [] SLOW_DB_OPERATION: $Proxy209.getAggregatedStatisticsForDateRange took 1356ms
2025-07-19 23:08:56.908 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [471ms] [] DB_OPERATION: $Proxy209.getSessionCountByDayOfWeek took 471ms
2025-07-19 23:08:57.119 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [199ms] [] DB_OPERATION: $Proxy209.getAccuracyByDayOfWeek took 199ms
2025-07-19 23:08:57.319 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [177ms] [] DB_OPERATION: $Proxy209.getSessionCountByHourOfDay took 177ms
2025-07-19 23:08:57.515 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [175ms] [] DB_OPERATION: $Proxy194.findById took 175ms
2025-07-19 23:08:58.085 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [565ms] [] SLOW_DB_OPERATION: $Proxy209.findStudyPatternsByHour took 565ms
2025-07-19 23:08:58.142 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [625ms] [] SERVICE_METHOD: RecommendationService.calculateOptimalStudyTimesAdvanced took 625ms
2025-07-19 23:08:58.275 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [123ms] [] DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized took 123ms
2025-07-19 23:09:01.581 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [3302ms] [] SLOW_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized took 3302ms
2025-07-19 23:09:05.931 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [4219ms] [] SLOW_DB_OPERATION: $Proxy209.findStudyPatternsByDayOfWeek took 4219ms
2025-07-19 23:09:05.956 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [4358ms] [] SLOW_SERVICE_METHOD: RecommendationService.generatePersonalizedRecommendations took 4358ms
2025-07-19 23:09:06.279 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getEnhancedStudySessions] [318ms] [] DB_OPERATION: $Proxy209.getAccuracyByHourOfDay took 318ms
2025-07-19 23:09:06.289 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getEnhancedStudySessions] [11753ms] [] SLOW_SERVICE_METHOD: StudySessionService.getEnhancedStudySessions took 11753ms
2025-07-19 23:09:06.290 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [11755ms] [] SLOW_ENDPOINT: StudySessionController.getEnhancedStudySessions took 11755ms
2025-07-19 23:10:19.086 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [toggleFavoriteDeck] [207ms] [] FAILED_DB_OPERATION: $Proxy206.countFavoritesByDeckId failed after 207ms with error: DataIntegrityViolationException
2025-07-19 23:10:19.088 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [toggleFavoriteDeck] [315ms] [] FAILED_SERVICE_METHOD: UserService.toggleFavoriteDeck failed after 315ms with error: DataIntegrityViolationException
2025-07-19 23:10:19.100 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [333ms] [] FAILED_ENDPOINT: DeckController.toggleFavoriteDeck failed after 333ms with error: DataIntegrityViolationException
2025-07-19 23:11:52.754 [ForkJoinPool.commonPool-worker-5] INFO  PERFORMANCE - 
                [] [404ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 404ms
2025-07-19 23:11:57.847 [ForkJoinPool.commonPool-worker-5] WARN  PERFORMANCE - 
                [] [5077ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 5077ms
2025-07-19 23:11:57.855 [ForkJoinPool.commonPool-worker-6] WARN  PERFORMANCE - 
                [] [563ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 563ms
2025-07-19 23:11:57.965 [ForkJoinPool.commonPool-worker-6] INFO  PERFORMANCE - 
                [] [110ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 110ms
2025-07-19 23:12:06.989 [ForkJoinPool.commonPool-worker-6] WARN  PERFORMANCE - 
                [] [9023ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalStudyHours took 9023ms
2025-07-19 23:12:07.115 [ForkJoinPool.commonPool-worker-5] WARN  PERFORMANCE - 
                [] [4816ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 4816ms
2025-07-19 23:12:07.567 [ForkJoinPool.commonPool-worker-6] WARN  PERFORMANCE - 
                [] [578ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 578ms
2025-07-19 23:12:07.567 [ForkJoinPool.commonPool-worker-5] INFO  PERFORMANCE - 
                [] [452ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 452ms
2025-07-19 23:12:07.617 [MessageBroker-11] WARN  PERFORMANCE - 
                [periodicHealthCheck] [15369ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 15369ms
2025-07-19 23:12:07.828 [MessageBroker-11] WARN  PERFORMANCE - 
                [periodicHealthCheck] [15595ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 15595ms
2025-07-19 23:12:09.050 [ForkJoinPool.commonPool-worker-5] WARN  PERFORMANCE - 
                [] [1458ms] [] SLOW_DB_OPERATION: $Proxy208.countDistinctTags took 1458ms
2025-07-19 23:12:12.138 [ForkJoinPool.commonPool-worker-5] WARN  PERFORMANCE - 
                [] [3088ms] [] SLOW_DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 3088ms
2025-07-19 23:12:21.075 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [219ms] [] DB_OPERATION: $Proxy194.findByEmail took 219ms
2025-07-19 23:12:31.104 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [455ms] [] DB_OPERATION: $Proxy194.findByEmail took 455ms
2025-07-19 23:16:53.148 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [445ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 445ms
2025-07-19 23:16:53.292 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [139ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 139ms
2025-07-19 23:16:53.529 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [163ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 163ms
2025-07-19 23:16:53.692 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 162ms
2025-07-19 23:16:54.152 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 267ms
2025-07-19 23:16:54.254 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 101ms
2025-07-19 23:16:54.391 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [135ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 135ms
2025-07-19 23:16:54.555 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 121ms
2025-07-19 23:16:54.683 [ForkJoinPool.commonPool-worker-7] INFO  PERFORMANCE - 
                [] [127ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 127ms
2025-07-19 23:16:54.859 [MessageBroker-2] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2301ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2301ms
2025-07-19 23:16:54.877 [MessageBroker-2] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2677ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 2677ms
2025-07-19 23:26:04.621 [MessageBroker-12] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [2ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 2ms with error: NullPointerException
2025-07-19 23:26:04.938 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [242ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 242ms
2025-07-19 23:26:04.938 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [218ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 218ms
2025-07-19 23:26:04.938 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [218ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 218ms
2025-07-19 23:26:04.938 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [242ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 242ms
2025-07-19 23:26:04.947 [main] INFO  PERFORMANCE - 
                [] [170ms] [] DB_OPERATION: $Proxy211.count took 170ms
2025-07-19 23:26:05.050 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [111ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 111ms
2025-07-19 23:26:05.332 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [631ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 631ms
2025-07-19 23:26:05.332 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [631ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 631ms
2025-07-19 23:26:05.333 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [715ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 715ms
2025-07-19 23:26:05.333 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [715ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 715ms
2025-07-19 23:31:04.858 [ForkJoinPool.commonPool-worker-3] INFO  PERFORMANCE - 
                [] [131ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 131ms
2025-07-19 23:31:05.121 [MessageBroker-11] INFO  PERFORMANCE - 
                [periodicHealthCheck] [513ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 513ms
2025-07-19 23:32:06.834 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [102ms] [] DB_OPERATION: $Proxy194.findByEmail took 102ms
2025-07-19 23:32:07.142 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCurrentUser] [191ms] [] DB_OPERATION: $Proxy194.findById took 191ms
2025-07-19 23:32:08.874 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getUserDecks] [150ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 150ms
2025-07-19 23:32:29.192 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getStudyActivityData] [153ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 153ms
2025-07-19 23:32:29.208 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [148ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 148ms
2025-07-19 23:32:29.220 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getUserStudySessions] [106ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 106ms
2025-07-19 23:32:29.251 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getDashboardData] [188ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 188ms with error: InvalidDataAccessResourceUsageException
2025-07-19 23:32:40.751 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [toggleFavoriteDeck] [60ms] [] FAILED_SERVICE_METHOD: UserService.toggleFavoriteDeck failed after 60ms with error: ClassCastException
2025-07-19 23:32:40.757 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [68ms] [] FAILED_ENDPOINT: DeckController.toggleFavoriteDeck failed after 68ms with error: ClassCastException
2025-07-19 23:33:19.630 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [searchDecks] [112ms] [] DB_OPERATION: $Proxy206.advancedSearch took 112ms
2025-07-19 23:33:52.526 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [221ms] [] DB_OPERATION: $Proxy194.findByEmail took 221ms
2025-07-19 23:33:52.812 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getNewlyCreatedDecks] [148ms] [] DB_OPERATION: $Proxy206.findByIsPublicTrueAndDeletedFalseOrderByCreatedAtDesc took 148ms
2025-07-19 23:34:15.910 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [1123ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1123ms
2025-07-19 23:34:15.911 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [1128ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1128ms
2025-07-19 23:34:16.084 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCurrentUser] [111ms] [] DB_OPERATION: $Proxy194.findById took 111ms
2025-07-19 23:34:16.084 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCurrentUser] [111ms] [] DB_OPERATION: $Proxy194.findById took 111ms
2025-07-19 23:34:17.081 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [177ms] [] DB_OPERATION: $Proxy194.findByEmail took 177ms
2025-07-19 23:34:17.361 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getUserDecks] [1253ms] [] SLOW_DB_OPERATION: $Proxy206.findByCreatorWithFilters took 1253ms
2025-07-19 23:34:17.504 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getUserDecks] [139ms] [] DB_OPERATION: $Proxy207.countByDeckId took 139ms
2025-07-19 23:34:17.547 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [getUserDecks] [1444ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 1444ms
2025-07-19 23:34:17.549 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [] [1449ms] [] ENDPOINT: DeckController.getUserDecks took 1449ms
2025-07-19 23:35:01.206 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [createDeck] [287ms] [] DB_OPERATION: $Proxy206.save took 287ms
2025-07-19 23:35:05.619 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getTrendingDecks] [1ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 1ms with error: LazyInitializationException
2025-07-19 23:35:05.620 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getTrendingDecks] [42ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks failed after 42ms with error: LazyInitializationException
2025-07-19 23:35:05.621 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [43ms] [] FAILED_ENDPOINT: DiscoveryController.getTrendingDecks failed after 43ms with error: LazyInitializationException
2025-07-19 23:36:40.130 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getNewlyCreatedDecks] [1ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 1ms with error: LazyInitializationException
2025-07-19 23:36:40.132 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getNewlyCreatedDecks] [31ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getNewlyCreatedDecks failed after 31ms with error: LazyInitializationException
2025-07-19 23:36:40.133 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [32ms] [] FAILED_ENDPOINT: DiscoveryController.getNewDecks failed after 32ms with error: LazyInitializationException
2025-07-19 23:36:59.161 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCurrentUser] [186ms] [] DB_OPERATION: $Proxy194.findById took 186ms
2025-07-19 23:37:00.170 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getTrendingDecks] [2ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 2ms with error: LazyInitializationException
2025-07-19 23:37:00.172 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getTrendingDecks] [59ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks failed after 59ms with error: LazyInitializationException
2025-07-19 23:37:00.173 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [61ms] [] FAILED_ENDPOINT: DiscoveryController.getTrendingDecks failed after 61ms with error: LazyInitializationException
2025-07-19 23:37:01.727 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getNewlyCreatedDecks] [1ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 1ms with error: LazyInitializationException
2025-07-19 23:37:01.727 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getNewlyCreatedDecks] [23ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getNewlyCreatedDecks failed after 23ms with error: LazyInitializationException
2025-07-19 23:37:01.728 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [24ms] [] FAILED_ENDPOINT: DiscoveryController.getNewDecks failed after 24ms with error: LazyInitializationException
2025-07-19 23:47:58.887 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [517ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 517ms
2025-07-19 23:47:59.186 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCurrentUser] [128ms] [] DB_OPERATION: $Proxy194.findById took 128ms
2025-07-19 23:48:01.134 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [1943ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1943ms
2025-07-19 23:48:01.135 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [1944ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1944ms
2025-07-19 23:48:01.138 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [1947ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.shouldRedirectToSubscription took 1947ms
2025-07-19 23:48:06.380 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCurrentUserPreferences] [175ms] [] DB_OPERATION: $Proxy244.findByUser took 175ms
2025-07-19 23:48:07.081 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getTrendingDecks] [504ms] [] SLOW_DB_OPERATION: $Proxy206.findTrendingDecks took 504ms
2025-07-19 23:48:07.098 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getTrendingDecks] [16ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 16ms with error: LazyInitializationException
2025-07-19 23:48:07.104 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getTrendingDecks] [571ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks failed after 571ms with error: LazyInitializationException
2025-07-19 23:48:07.105 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [586ms] [] FAILED_ENDPOINT: DiscoveryController.getTrendingDecks failed after 586ms with error: LazyInitializationException
2025-07-19 23:48:17.100 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [9983ms] [] SLOW_DB_OPERATION: $Proxy244.findByUser took 9983ms
2025-07-19 23:48:17.118 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [10024ms] [] SLOW_SERVICE_METHOD: UserPreferencesService.updateCurrentUserPreferences took 10024ms
2025-07-19 23:48:17.122 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [] [10030ms] [] SLOW_ENDPOINT: UserPreferencesController.updateCurrentUserPreferences took 10030ms
2025-07-19 23:48:19.346 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [12252ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 12252ms
2025-07-19 23:48:19.346 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [12252ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 12252ms
2025-07-19 23:48:19.399 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [12305ms] [] SLOW_SERVICE_METHOD: UserPreferencesService.updateCurrentUserPreferences took 12305ms
2025-07-19 23:48:19.400 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [12308ms] [] SLOW_ENDPOINT: UserPreferencesController.updateCurrentUserPreferences took 12308ms
2025-07-19 23:49:05.118 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [9932ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 9932ms
2025-07-19 23:49:09.436 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [searchDecks] [174ms] [] DB_OPERATION: $Proxy206.advancedSearch took 174ms
2025-07-19 23:49:19.042 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [searchDecks] [840ms] [] SLOW_DB_OPERATION: $Proxy206.advancedSearch took 840ms
2025-07-19 23:49:19.107 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [searchDecks] [907ms] [] SERVICE_METHOD: SearchService.searchDecks took 907ms
2025-07-19 23:49:31.743 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [667ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 667ms
2025-07-19 23:49:32.435 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [672ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 672ms
2025-07-19 23:49:32.435 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [672ms] [] SERVICE_METHOD: UserService.getCurrentUser took 672ms
2025-07-19 23:49:32.436 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [shouldRedirectToSubscription] [673ms] [] SERVICE_METHOD: SubscriptionStatusService.shouldRedirectToSubscription took 673ms
2025-07-19 23:49:32.448 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [112ms] [] DB_OPERATION: $Proxy194.findByEmail took 112ms
2025-07-19 23:49:41.139 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [7728ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 7728ms
2025-07-19 23:49:41.509 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [searchPublicDecks] [251ms] [] DB_OPERATION: $Proxy206.searchPublicDecks took 251ms
2025-07-19 23:55:54.198 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [searchDecks] [192ms] [] DB_OPERATION: $Proxy206.advancedSearch took 192ms
2025-07-19 23:56:08.985 [ForkJoinPool.commonPool-worker-12] WARN  PERFORMANCE - 
                [] [4142ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 4142ms
2025-07-19 23:56:09.009 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4413ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 4413ms
2025-07-19 23:56:09.010 [MessageBroker-5] WARN  PERFORMANCE - 
                [periodicHealthCheck] [4416ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 4416ms
2025-07-19 23:56:16.102 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getTrendingDecks] [1ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 1ms with error: LazyInitializationException
2025-07-19 23:56:16.103 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getTrendingDecks] [44ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks failed after 44ms with error: LazyInitializationException
2025-07-19 23:56:16.104 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [45ms] [] FAILED_ENDPOINT: DiscoveryController.getTrendingDecks failed after 45ms with error: LazyInitializationException
2025-07-19 23:56:16.569 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getTrendingDecks] [0ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 0ms with error: LazyInitializationException
2025-07-19 23:56:16.570 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getTrendingDecks] [11ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks failed after 11ms with error: LazyInitializationException
2025-07-19 23:56:16.570 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [11ms] [] FAILED_ENDPOINT: DiscoveryController.getTrendingDecks failed after 11ms with error: LazyInitializationException
2025-07-19 23:56:41.800 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getTrendingDecks] [0ms] [] FAILED_SERVICE_METHOD: SubscriptionStatusService.hasActiveSubscription failed after 0ms with error: LazyInitializationException
2025-07-19 23:56:41.801 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getTrendingDecks] [11ms] [] FAILED_SERVICE_METHOD: DeckDiscoveryService.getTrendingDecks failed after 11ms with error: LazyInitializationException
2025-07-19 23:56:41.802 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [11ms] [] FAILED_ENDPOINT: DiscoveryController.getTrendingDecks failed after 11ms with error: LazyInitializationException
