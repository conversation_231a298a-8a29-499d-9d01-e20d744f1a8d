package com.studycards.service;

import com.studycards.dto.FavoriteResponse;
import com.studycards.model.Deck;
import com.studycards.model.User;
import com.studycards.repository.DeckRepository;
import com.studycards.repository.UserRepository;
import com.studycards.security.UserDetailsImpl;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import jakarta.persistence.Query;
import java.util.HashSet;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceFavoriteTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private DeckRepository deckRepository;

    @Mock
    private EntityManager entityManager;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private UserDetailsImpl userDetails;

    @Mock
    private Query nativeQuery;

    @InjectMocks
    private UserService userService;

    private User testUser;
    private Deck testDeck;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .password("password")
                .favoriteDecks(new HashSet<>())
                .build();

        testDeck = Deck.builder()
                .id(1L)
                .title("Test Deck")
                .description("Test Description")
                .favoriteBy(new HashSet<>())
                .build();

        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUser()).thenReturn(testUser);
    }

    @Test
    void testToggleFavoriteDeck_AddToFavorites() {
        // Arrange
        Long deckId = 1L;
        when(deckRepository.findById(deckId)).thenReturn(Optional.of(testDeck));
        when(entityManager.createNativeQuery(anyString())).thenReturn(nativeQuery);
        when(nativeQuery.setParameter(anyString(), any())).thenReturn(nativeQuery);
        when(nativeQuery.executeUpdate()).thenReturn(1);
        when(deckRepository.countFavoritesByDeckId(deckId)).thenReturn(1);

        // Act
        FavoriteResponse response = userService.toggleFavoriteDeck(deckId);

        // Assert
        assertNotNull(response);
        assertEquals(deckId, response.getDeckId());
        assertTrue(response.isFavorite());
        assertEquals(1, response.getFavoriteCount());

        // Verify native query was called to insert
        verify(entityManager).createNativeQuery(contains("INSERT INTO user_favorite_decks"));
        verify(nativeQuery, times(2)).setParameter(anyString(), any());
        verify(nativeQuery).executeUpdate();
        
        // Verify the in-memory collection was updated
        assertTrue(testUser.getFavoriteDecks().contains(testDeck));
    }

    @Test
    void testToggleFavoriteDeck_RemoveFromFavorites() {
        // Arrange
        Long deckId = 1L;
        testUser.getFavoriteDecks().add(testDeck); // User already has this deck as favorite
        
        when(deckRepository.findById(deckId)).thenReturn(Optional.of(testDeck));
        when(entityManager.createNativeQuery(anyString())).thenReturn(nativeQuery);
        when(nativeQuery.setParameter(anyString(), any())).thenReturn(nativeQuery);
        when(nativeQuery.executeUpdate()).thenReturn(1);
        when(deckRepository.countFavoritesByDeckId(deckId)).thenReturn(0);

        // Act
        FavoriteResponse response = userService.toggleFavoriteDeck(deckId);

        // Assert
        assertNotNull(response);
        assertEquals(deckId, response.getDeckId());
        assertFalse(response.isFavorite());
        assertEquals(0, response.getFavoriteCount());

        // Verify native query was called to delete
        verify(entityManager).createNativeQuery(contains("DELETE FROM user_favorite_decks"));
        verify(nativeQuery, times(2)).setParameter(anyString(), any());
        verify(nativeQuery).executeUpdate();
        
        // Verify the in-memory collection was updated
        assertFalse(testUser.getFavoriteDecks().contains(testDeck));
    }
}
