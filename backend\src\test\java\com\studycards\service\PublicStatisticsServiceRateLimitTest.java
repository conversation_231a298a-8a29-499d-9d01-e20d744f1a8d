package com.studycards.service;

import com.studycards.dto.PublicStatisticsResponse;
import com.studycards.exception.RateLimitExceededException;
import com.studycards.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Test class to verify rate limiting behavior for internal vs external requests
 */
@ExtendWith(MockitoExtension.class)
class PublicStatisticsServiceRateLimitTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private DeckRepository deckRepository;

    @Mock
    private CardRepository cardRepository;

    @Mock
    private DeckTagRepository deckTagRepository;

    @Mock
    private StudySessionRepository studySessionRepository;

    @Mock
    private StatisticsMonitoringService monitoringService;

    @InjectMocks
    private PublicStatisticsService publicStatisticsService;

    @BeforeEach
    void setUp() {
        // Mock basic repository responses to avoid null pointer exceptions
        when(userRepository.countByEmailVerifiedTrue()).thenReturn(100L);
        when(userRepository.countByCreatedAtAfter(org.mockito.ArgumentMatchers.any())).thenReturn(10L);
        when(deckRepository.countByIsPublicTrue()).thenReturn(50L);
        when(cardRepository.count()).thenReturn(1000L);
        when(studySessionRepository.countByCompletedTrue()).thenReturn(200L);
        when(deckTagRepository.countDistinctTags()).thenReturn(25L);
        
        // Mock monitoring service
        when(monitoringService.recordOperationStart(anyString())).thenReturn(System.currentTimeMillis());
    }

    @Test
    void testInternalCacheWarmingRequestBypassesRateLimit() {
        // Create a mock request that simulates cache warming
        MockHttpServletRequest cacheWarmingRequest = new MockHttpServletRequest();
        cacheWarmingRequest.setRemoteAddr("127.0.0.1");
        cacheWarmingRequest.addHeader("User-Agent", "StatisticsCacheWarmer/1.0");

        // This should not throw a rate limit exception even if called multiple times rapidly
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 5; i++) {
                PublicStatisticsResponse response = publicStatisticsService.getPublicStatistics(cacheWarmingRequest);
                assertNotNull(response);
            }
        });
    }

    @Test
    void testInternalDailySummaryRequestBypassesRateLimit() {
        // Create a mock request that simulates daily summary
        MockHttpServletRequest dailySummaryRequest = new MockHttpServletRequest();
        dailySummaryRequest.setRemoteAddr("127.0.0.1");
        dailySummaryRequest.addHeader("User-Agent", "StatisticsDailySummary/1.0");

        // This should not throw a rate limit exception even if called multiple times rapidly
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 5; i++) {
                PublicStatisticsResponse response = publicStatisticsService.getPublicStatistics(dailySummaryRequest);
                assertNotNull(response);
            }
        });
    }

    @Test
    void testExternalRequestsAreStillRateLimited() {
        // Create a mock request that simulates a regular user request
        MockHttpServletRequest userRequest = new MockHttpServletRequest();
        userRequest.setRemoteAddr("*************");
        userRequest.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

        // First request should succeed
        assertDoesNotThrow(() -> {
            PublicStatisticsResponse response = publicStatisticsService.getPublicStatistics(userRequest);
            assertNotNull(response);
        });

        // Rapid subsequent requests should trigger rate limiting
        assertThrows(RateLimitExceededException.class, () -> {
            for (int i = 0; i < 70; i++) { // Exceed the 60 requests per minute limit
                publicStatisticsService.getPublicStatistics(userRequest);
            }
        });
    }

    @Test
    void testRequestWithoutUserAgentIsNotConsideredInternal() {
        // Create a mock request without User-Agent header
        MockHttpServletRequest requestWithoutUserAgent = new MockHttpServletRequest();
        requestWithoutUserAgent.setRemoteAddr("127.0.0.1");

        // This should still be subject to rate limiting
        assertThrows(RateLimitExceededException.class, () -> {
            for (int i = 0; i < 70; i++) { // Exceed the 60 requests per minute limit
                publicStatisticsService.getPublicStatistics(requestWithoutUserAgent);
            }
        });
    }
}
