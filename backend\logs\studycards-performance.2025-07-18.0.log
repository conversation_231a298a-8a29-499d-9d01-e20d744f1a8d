2025-07-18 15:40:58.375 [MessageBroker-14] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [2ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 2ms with error: NullPointerException
2025-07-18 15:40:58.841 [MessageBroker-6] INFO  PERFORMANCE - 
                [sendVerificationReminders] [376ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 376ms
2025-07-18 15:40:58.841 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [376ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 376ms
2025-07-18 15:40:58.844 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [322ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 322ms
2025-07-18 15:40:58.844 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [322ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 322ms
2025-07-18 15:40:58.852 [main] INFO  PERFORMANCE - 
                [] [205ms] [] DB_OPERATION: $Proxy211.count took 205ms
2025-07-18 15:40:59.191 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [156ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 156ms
2025-07-18 15:40:59.191 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [158ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 158ms
2025-07-18 15:40:59.342 [MessageBroker-13] INFO  PERFORMANCE - 
                [cacheWarming] [851ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 851ms
2025-07-18 15:40:59.342 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [851ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 851ms
2025-07-18 15:40:59.345 [MessageBroker-13] INFO  PERFORMANCE - 
                [cacheWarming] [973ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 973ms
2025-07-18 15:40:59.345 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [973ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 973ms
2025-07-18 16:11:25.032 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getStudyActivityData] [134ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 134ms
2025-07-18 16:11:25.034 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getUserStudySessions] [113ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 113ms
2025-07-18 16:11:25.073 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [119ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 119ms
2025-07-18 16:11:25.089 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [94ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 94ms with error: ConversionFailedException
2025-07-18 16:11:25.097 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [110ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 110ms
2025-07-18 16:11:25.333 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDashboardData] [103ms] [] DB_OPERATION: $Proxy209.getDeckStudyStatistics took 103ms
2025-07-18 16:11:25.462 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDashboardData] [107ms] [] DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 107ms
2025-07-18 16:11:25.520 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getDashboardData] [577ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 577ms
2025-07-18 16:49:01.134 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCardsByDeckId] [146ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 146ms
2025-07-18 16:49:11.536 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [createStudySession] [240ms] [] DB_OPERATION: $Proxy209.save took 240ms
2025-07-18 17:18:03.599 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [102ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 102ms
2025-07-18 17:22:32.014 [MessageBroker-14] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-18 17:22:32.393 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [326ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 326ms
2025-07-18 17:22:32.394 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [268ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 268ms
2025-07-18 17:22:32.394 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [268ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 268ms
2025-07-18 17:22:32.405 [main] INFO  PERFORMANCE - 
                [] [180ms] [] DB_OPERATION: $Proxy211.count took 180ms
2025-07-18 17:22:32.411 [MessageBroker-10] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [344ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 344ms
2025-07-18 17:22:32.628 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [116ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 116ms
2025-07-18 17:22:32.745 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [644ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 644ms
2025-07-18 17:22:32.745 [MessageBroker-16] INFO  PERFORMANCE - 
                [cacheWarming] [644ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 644ms
2025-07-18 17:22:32.746 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [734ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 734ms
2025-07-18 17:22:32.747 [MessageBroker-16] INFO  PERFORMANCE - 
                [cacheWarming] [735ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 735ms
2025-07-18 17:22:58.164 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [createStudySession] [156ms] [] DB_OPERATION: $Proxy209.save took 156ms
2025-07-18 17:46:48.493 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCurrentUser] [118ms] [] DB_OPERATION: $Proxy194.findById took 118ms
2025-07-18 17:46:48.951 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [143ms] [] DB_OPERATION: $Proxy207.findById took 143ms
2025-07-18 17:46:49.822 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [263ms] [] DB_OPERATION: $Proxy214.findRecentByUserId took 263ms
2025-07-18 17:46:50.290 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [416ms] [] DB_OPERATION: $Proxy214.getAverageStudyTimeByCardId took 416ms
2025-07-18 17:46:50.976 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [507ms] [] SLOW_DB_OPERATION: $Proxy214.countByCardIdAndUserId took 507ms
2025-07-18 17:46:51.098 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [101ms] [] DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 101ms
2025-07-18 17:46:51.613 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [509ms] [] SLOW_DB_OPERATION: $Proxy214.getPerformanceStatsByCardId took 509ms
2025-07-18 17:46:53.270 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [recordTokenValidation] [1695ms] [] SLOW_SERVICE_METHOD: JwtMetricsService.recordTokenValidation took 1695ms
2025-07-18 17:46:53.270 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [4203ms] [] SLOW_SERVICE_METHOD: SpacedRepetitionService.calculateEnhancedNextReview took 4203ms
2025-07-18 17:46:53.428 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [133ms] [] DB_OPERATION: $Proxy214.findByStudySessionAndCardAndUser took 133ms
2025-07-18 17:46:53.448 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [1992ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1992ms
2025-07-18 17:46:54.716 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1269ms] [] SLOW_DB_OPERATION: $Proxy214.save took 1269ms
2025-07-18 17:46:54.726 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCurrentUser] [1246ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1246ms
2025-07-18 17:46:54.726 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getCurrentUser] [1246ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1246ms
2025-07-18 17:46:54.842 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [6102ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 6102ms
2025-07-18 17:46:55.368 [StudyCards-Async-2] INFO  PERFORMANCE - 
                [recordMetric] [521ms] [] SERVICE_METHOD: StudyCardsMonitoringService.recordMetric took 521ms
2025-07-18 17:46:55.575 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [] [6852ms] [] SLOW_ENDPOINT: StudySessionController.recordEnhancedCardPerformance took 6852ms
2025-07-18 17:46:55.577 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [191ms] [] DB_OPERATION: $Proxy214.findRecentByUserId took 191ms
2025-07-18 17:46:55.834 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [103ms] [] DB_OPERATION: $Proxy214.countByCardIdAndUserId took 103ms
2025-07-18 17:46:55.879 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1044ms] [] SLOW_SERVICE_METHOD: SpacedRepetitionService.calculateEnhancedNextReview took 1044ms
2025-07-18 17:46:55.981 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getCardsByDeckId] [236ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 236ms
2025-07-18 17:46:56.063 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [1278ms] [] SLOW_SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 1278ms
2025-07-18 17:46:56.279 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [1485ms] [] FAILED_ENDPOINT: StudySessionController.recordEnhancedCardPerformance failed after 1485ms with error: TransactionSystemException
2025-07-18 17:46:56.527 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [getDeckByIdWithDetails] [576ms] [] SLOW_DB_OPERATION: $Proxy206.findByIdWithDetails took 576ms
2025-07-18 17:46:56.527 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [576ms] [] SERVICE_METHOD: DeckService.getDeckByIdWithDetails took 576ms
2025-07-18 17:50:24.436 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [32ms] [] FAILED_ENDPOINT: AnalyticsController.getAllAnalytics failed after 32ms with error: SpelEvaluationException
2025-07-18 21:17:24.052 [MessageBroker-11] WARN  PERFORMANCE - 
                [hourlyPerformanceReport] [2791ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 2791ms
2025-07-18 21:17:24.573 [MessageBroker-11] INFO  PERFORMANCE - 
                [hourlyPerformanceReport] [521ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 521ms
2025-07-18 21:17:24.577 [MessageBroker-3] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2875ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2875ms
2025-07-18 21:17:24.583 [MessageBroker-3] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3332ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 3332ms
2025-07-18 21:17:25.026 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [742ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 742ms
2025-07-18 21:17:25.026 [MessageBroker-10] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [744ms] [] SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 744ms
2025-07-18 21:17:25.228 [ForkJoinPool.commonPool-worker-15] WARN  PERFORMANCE - 
                [] [1176ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1176ms
2025-07-18 21:17:30.607 [MessageBroker-3] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5379ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 5379ms
2025-07-18 21:17:30.613 [MessageBroker-8] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [6034ms] [] SLOW_SERVICE_METHOD: CacheUtilityService.silentClear took 6034ms
2025-07-18 21:17:30.613 [MessageBroker-8] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [8611ms] [] SLOW_SERVICE_METHOD: SearchCacheService.invalidateSearchCaches took 8611ms
2025-07-18 21:17:31.128 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [5893ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 5893ms
2025-07-18 21:17:31.128 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [5893ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredSubscriptions took 5893ms
2025-07-18 21:17:31.160 [ForkJoinPool.commonPool-worker-15] WARN  PERFORMANCE - 
                [] [5932ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 5932ms
2025-07-18 21:17:31.164 [ForkJoinPool.commonPool-worker-17] WARN  PERFORMANCE - 
                [] [549ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 549ms
2025-07-18 21:17:31.186 [ForkJoinPool.commonPool-worker-18] WARN  PERFORMANCE - 
                [] [565ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 565ms
2025-07-18 21:17:31.186 [MessageBroker-10] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [9913ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 9913ms
2025-07-18 21:17:31.570 [ForkJoinPool.commonPool-worker-17] INFO  PERFORMANCE - 
                [] [308ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 308ms
2025-07-18 21:17:31.579 [MessageBroker-10] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [320ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 320ms
2025-07-18 21:17:31.625 [ForkJoinPool.commonPool-worker-18] INFO  PERFORMANCE - 
                [] [344ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 344ms
2025-07-18 21:17:31.635 [ForkJoinPool.commonPool-worker-15] INFO  PERFORMANCE - 
                [] [367ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 367ms
2025-07-18 21:17:31.859 [ForkJoinPool.commonPool-worker-17] INFO  PERFORMANCE - 
                [] [276ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 276ms
2025-07-18 21:17:31.863 [ForkJoinPool.commonPool-worker-18] INFO  PERFORMANCE - 
                [] [143ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 143ms
2025-07-18 21:17:32.004 [ForkJoinPool.commonPool-worker-18] INFO  PERFORMANCE - 
                [] [141ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 141ms
2025-07-18 21:17:32.008 [ForkJoinPool.commonPool-worker-17] INFO  PERFORMANCE - 
                [] [148ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 148ms
2025-07-18 21:17:32.080 [MessageBroker-3] WARN  PERFORMANCE - 
                [periodicHealthCheck] [7026ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 7026ms
2025-07-18 21:17:32.084 [MessageBroker-3] WARN  PERFORMANCE - 
                [periodicHealthCheck] [7501ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 7501ms
2025-07-18 21:17:32.105 [MessageBroker-13] WARN  PERFORMANCE - 
                [cacheWarming] [10832ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 10832ms
2025-07-18 21:17:32.112 [MessageBroker-13] WARN  PERFORMANCE - 
                [cacheWarming] [10853ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 10853ms
2025-07-18 21:21:23.845 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [8ms] [] FAILED_ENDPOINT: AnalyticsController.getAllAnalytics failed after 8ms with error: SpelEvaluationException
2025-07-18 21:24:50.919 [MessageBroker-12] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [2ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 2ms with error: NullPointerException
2025-07-18 21:24:51.340 [MessageBroker-11] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [311ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 311ms
2025-07-18 21:24:51.340 [MessageBroker-14] INFO  PERFORMANCE - 
                [sendVerificationReminders] [311ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 311ms
2025-07-18 21:24:51.342 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [283ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 283ms
2025-07-18 21:24:51.342 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [283ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 283ms
2025-07-18 21:24:51.347 [main] INFO  PERFORMANCE - 
                [] [163ms] [] DB_OPERATION: $Proxy211.count took 163ms
2025-07-18 21:24:51.554 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [130ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 130ms
2025-07-18 21:24:51.554 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [130ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 130ms
2025-07-18 21:24:51.692 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [644ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 644ms
2025-07-18 21:24:51.692 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [659ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 659ms
2025-07-18 21:24:51.694 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [777ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 777ms
2025-07-18 21:24:51.694 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [777ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 777ms
2025-07-18 21:35:51.695 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [38ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 38ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.720 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.733 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [6ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.745 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.757 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [6ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.769 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.780 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.791 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.799 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.810 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.897 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.908 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [8ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 8ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:35:51.935 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:36:01.544 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getEnhancedStudySessionById] [15ms] [] FAILED_SERVICE_METHOD: StudySessionService.getEnhancedStudySessionById failed after 15ms with error: LazyInitializationException
2025-07-18 21:36:02.588 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessionById] [5ms] [] FAILED_SERVICE_METHOD: StudySessionService.getEnhancedStudySessionById failed after 5ms with error: LazyInitializationException
2025-07-18 21:36:04.617 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getEnhancedStudySessionById] [3ms] [] FAILED_SERVICE_METHOD: StudySessionService.getEnhancedStudySessionById failed after 3ms with error: LazyInitializationException
2025-07-18 21:38:21.475 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [7ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 7ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.495 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [7ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 7ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.508 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [6ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.521 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [6ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.534 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.549 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [6ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.565 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.577 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.589 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.599 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.649 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.655 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:21.673 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.344 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.354 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.362 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.371 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.381 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.392 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.400 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.409 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.418 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.426 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.455 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.460 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:26.469 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [2ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 2ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.853 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.865 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.874 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.882 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.891 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.900 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.909 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.917 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.925 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.970 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.977 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:35.993 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.239 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.246 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [2ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 2ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.255 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.262 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.272 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.282 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.291 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.301 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.311 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.320 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.354 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.360 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:41.370 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.728 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.741 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [8ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 8ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.751 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.758 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.767 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.776 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.785 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.793 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.801 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.810 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.830 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [3ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 3ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.837 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:38:42.850 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getEnhancedStudySessions] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:30.845 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getExamHistory] [24ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 24ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:30.846 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getExamHistory] [32ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 32ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:30.851 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [38ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 38ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:31.909 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getExamHistory] [8ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 8ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:31.909 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getExamHistory] [13ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 13ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:31.911 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [15ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 15ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:32.949 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getExamHistory] [7ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 7ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:32.950 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getExamHistory] [9ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 9ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:32.952 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [12ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 12ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:36.525 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [6ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 6ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.534 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.539 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.544 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.549 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.555 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.560 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.565 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.570 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.574 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.579 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.583 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.588 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.593 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.598 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.603 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.608 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.613 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.618 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.624 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 4ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.628 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.633 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.639 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.645 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.655 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.662 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.669 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.676 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.683 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.688 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.694 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 21:39:36.798 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:36.805 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:36.909 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy209.calculateCurrentStreakOptimized failed after 5ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:36.916 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy209.calculateLongestStreakOptimized failed after 4ms with error: InvalidDataAccessResourceUsageException
2025-07-18 21:39:36.947 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [628ms] [] SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 628ms
2025-07-18 21:39:36.949 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [] [645ms] [] FAILED_ENDPOINT: StudySessionController.getStudyStatistics failed after 645ms with error: UnexpectedRollbackException
2025-07-18 21:39:50.197 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [96ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 96ms with error: ClassCastException
2025-07-18 21:39:50.198 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [99ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 99ms with error: ClassCastException
2025-07-18 21:39:51.304 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [searchDecks] [34ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 34ms with error: ClassCastException
2025-07-18 21:39:51.304 [http-nio-8082-exec-2] ERROR PERFORMANCE - 
                [] [35ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 35ms with error: ClassCastException
2025-07-18 21:39:53.425 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [29ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 29ms with error: ClassCastException
2025-07-18 21:39:53.425 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [29ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 29ms with error: ClassCastException
2025-07-18 21:44:33.221 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-18 21:44:33.637 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [270ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 270ms
2025-07-18 21:44:33.637 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [321ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 321ms
2025-07-18 21:44:33.637 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [270ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 270ms
2025-07-18 21:44:33.637 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [320ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 320ms
2025-07-18 21:44:33.646 [main] INFO  PERFORMANCE - 
                [] [133ms] [] DB_OPERATION: $Proxy211.count took 133ms
2025-07-18 21:44:33.849 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [130ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 130ms
2025-07-18 21:44:33.849 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [130ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 130ms
2025-07-18 21:44:33.961 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [630ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 630ms
2025-07-18 21:44:33.961 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [630ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 630ms
2025-07-18 21:44:33.963 [MessageBroker-12] INFO  PERFORMANCE - 
                [periodicHealthCheck] [743ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 743ms
2025-07-18 21:44:33.963 [MessageBroker-14] INFO  PERFORMANCE - 
                [cacheWarming] [743ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 743ms
2025-07-18 21:46:16.165 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [78ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 78ms with error: ClassCastException
2025-07-18 21:46:16.165 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [79ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 79ms with error: ClassCastException
2025-07-18 21:46:17.333 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [42ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 42ms with error: ClassCastException
2025-07-18 21:46:17.333 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [42ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 42ms with error: ClassCastException
2025-07-18 21:46:19.513 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [37ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 37ms with error: ClassCastException
2025-07-18 21:46:19.514 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [37ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 37ms with error: ClassCastException
2025-07-18 21:46:26.251 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [searchDecks] [33ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 33ms with error: ClassCastException
2025-07-18 21:46:26.252 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [] [34ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 34ms with error: ClassCastException
2025-07-18 21:46:27.343 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [12ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 12ms with error: ClassCastException
2025-07-18 21:46:27.344 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [13ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 13ms with error: ClassCastException
2025-07-18 21:46:29.519 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [9ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 9ms with error: ClassCastException
2025-07-18 21:46:29.519 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [9ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 9ms with error: ClassCastException
2025-07-18 21:46:33.631 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [searchDecks] [9ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 9ms with error: ClassCastException
2025-07-18 21:46:33.631 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [9ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 9ms with error: ClassCastException
2025-07-18 21:49:33.561 [ForkJoinPool.commonPool-worker-3] INFO  PERFORMANCE - 
                [] [292ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 292ms
2025-07-18 22:00:40.136 [MessageBroker-5] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [2ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 2ms with error: NullPointerException
2025-07-18 22:00:40.478 [MessageBroker-4] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [256ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 256ms
2025-07-18 22:00:40.478 [MessageBroker-8] INFO  PERFORMANCE - 
                [sendVerificationReminders] [256ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 256ms
2025-07-18 22:00:40.478 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [211ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 211ms
2025-07-18 22:00:40.478 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [211ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 211ms
2025-07-18 22:00:40.484 [main] INFO  PERFORMANCE - 
                [] [159ms] [] DB_OPERATION: $Proxy211.count took 159ms
2025-07-18 22:00:40.663 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [122ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 122ms
2025-07-18 22:00:40.663 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [122ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 122ms
2025-07-18 22:00:41.227 [ForkJoinPool.commonPool-worker-2] WARN  PERFORMANCE - 
                [] [561ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 561ms
2025-07-18 22:00:41.234 [ForkJoinPool.commonPool-worker-1] WARN  PERFORMANCE - 
                [] [568ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 568ms
2025-07-18 22:00:41.308 [MessageBroker-7] WARN  PERFORMANCE - 
                [cacheWarming] [1073ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1073ms
2025-07-18 22:00:41.308 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1073ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1073ms
2025-07-18 22:00:41.309 [MessageBroker-6] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1177ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1177ms
2025-07-18 22:00:41.310 [MessageBroker-7] WARN  PERFORMANCE - 
                [cacheWarming] [1177ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1177ms
2025-07-18 22:02:15.899 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [669ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 669ms
2025-07-18 22:02:16.550 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getCurrentUser] [146ms] [] DB_OPERATION: $Proxy194.findById took 146ms
2025-07-18 22:02:22.861 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [6311ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 6311ms
2025-07-18 22:02:22.861 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [6311ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 6311ms
2025-07-18 22:02:22.861 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [shouldRedirectToSubscription] [6311ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.shouldRedirectToSubscription took 6311ms
2025-07-18 22:02:22.862 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [] [526ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 526ms
2025-07-18 22:02:22.866 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [548ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 548ms
2025-07-18 22:02:22.871 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [556ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 556ms
2025-07-18 22:02:22.875 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [] [557ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 557ms
2025-07-18 22:02:23.109 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getDailyGoalProgress] [189ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 189ms
2025-07-18 22:02:23.110 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserDecks] [137ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 137ms
2025-07-18 22:02:23.142 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [168ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 168ms
2025-07-18 22:02:23.145 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getStudyActivityData] [208ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 208ms
2025-07-18 22:02:24.348 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getUserDecks] [1184ms] [] SLOW_SERVICE_METHOD: UserService.isDeckFavorited took 1184ms
2025-07-18 22:02:24.364 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [] [339ms] [] DB_OPERATION: $Proxy194.findByEmail took 339ms
2025-07-18 22:02:24.606 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [109ms] [] DB_OPERATION: $Proxy194.findByEmail took 109ms
2025-07-18 22:02:24.632 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [137ms] [] DB_OPERATION: $Proxy194.findByEmail took 137ms
2025-07-18 22:02:24.858 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [] [118ms] [] DB_OPERATION: $Proxy194.findByEmail took 118ms
2025-07-18 22:02:24.863 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [113ms] [] DB_OPERATION: $Proxy194.findByEmail took 113ms
2025-07-18 22:02:24.875 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getUserStudySessions] [130ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 130ms
2025-07-18 22:02:24.949 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [getUserDecks] [1986ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 1986ms
2025-07-18 22:02:24.955 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [37ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 37ms with error: ConversionFailedException
2025-07-18 22:02:24.969 [http-nio-8082-exec-9] WARN  PERFORMANCE - 
                [] [2018ms] [] SLOW_ENDPOINT: DeckController.getUserDecks took 2018ms
2025-07-18 22:02:25.101 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy194.findByEmail took 101ms
2025-07-18 22:02:25.234 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getUserDecks] [551ms] [] SERVICE_METHOD: DeckService.getUserDecks took 551ms
2025-07-18 22:02:27.507 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [217ms] [] DB_OPERATION: $Proxy194.findByEmail took 217ms
2025-07-18 22:02:27.653 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDashboardData] [2420ms] [] SLOW_DB_OPERATION: $Proxy209.findRecentlyStudiedDecksForUser took 2420ms
2025-07-18 22:02:28.592 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDashboardData] [936ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckId took 936ms
2025-07-18 22:02:28.703 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [getDashboardData] [3820ms] [] SLOW_SERVICE_METHOD: DashboardService.getDashboardData took 3820ms
2025-07-18 22:02:28.705 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [3822ms] [] SLOW_ENDPOINT: DashboardController.getDashboardData took 3822ms
2025-07-18 22:02:43.114 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.124 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.130 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.136 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.142 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.149 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.157 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.163 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.170 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.175 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.180 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.189 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.193 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.198 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.205 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 4ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.211 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.218 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.223 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.228 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.234 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [6ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 6ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.240 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.246 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [5ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 5ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.252 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.257 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [4ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 4ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.259 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.265 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.271 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.277 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.283 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.289 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [0ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 0ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.295 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.531 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [550ms] [] SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 550ms
2025-07-18 22:02:43.767 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.772 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.776 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.782 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.788 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.793 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.800 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.805 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.810 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.815 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.821 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.826 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.831 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.836 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.840 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.844 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.848 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.853 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.856 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.860 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.866 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [3ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 3ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.872 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.878 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.882 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.886 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.890 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.894 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.899 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.903 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.906 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [1ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 1ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:02:43.914 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getComprehensiveStatistics] [2ms] [] FAILED_DB_OPERATION: $Proxy214.countMasteredCardsByUserAndDate failed after 2ms with error: InvalidDataAccessApiUsageException
2025-07-18 22:05:45.914 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getExamHistory] [39ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 39ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:45.916 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getExamHistory] [43ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 43ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:45.924 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [52ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 52ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:46.998 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getExamHistory] [8ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 8ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:46.998 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getExamHistory] [9ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 9ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:46.999 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [10ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 10ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:48.055 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getExamHistory] [8ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 8ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:48.055 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [getExamHistory] [12ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 12ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:05:48.057 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [14ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 14ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:39:01.479 [MessageBroker-14] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [3ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 3ms with error: NullPointerException
2025-07-18 22:39:01.873 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [301ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 301ms
2025-07-18 22:39:01.873 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [301ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 301ms
2025-07-18 22:39:01.873 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [215ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 215ms
2025-07-18 22:39:01.878 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [220ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 220ms
2025-07-18 22:39:01.883 [main] INFO  PERFORMANCE - 
                [] [149ms] [] DB_OPERATION: $Proxy211.count took 149ms
2025-07-18 22:39:02.148 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [190ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 190ms
2025-07-18 22:39:02.148 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [190ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 190ms
2025-07-18 22:39:02.263 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [632ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 632ms
2025-07-18 22:39:02.263 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [632ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 632ms
2025-07-18 22:39:02.265 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [796ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 796ms
2025-07-18 22:39:02.265 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [796ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 796ms
2025-07-18 22:44:29.984 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getExamHistory] [93ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 93ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:29.985 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getExamHistory] [100ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 100ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:29.994 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [111ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 111ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:31.060 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getExamHistory] [9ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 9ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:31.060 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [getExamHistory] [10ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 10ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:31.061 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [13ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 13ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:32.100 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getExamHistory] [6ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:32.100 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [getExamHistory] [9ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 9ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:32.101 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [11ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 11ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:38.994 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getExamHistory] [14ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 14ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:38.996 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [getExamHistory] [18ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 18ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:38.996 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [18ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 18ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:43.669 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getExamHistory] [16ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 16ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:43.671 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getExamHistory] [22ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 22ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:43.672 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [23ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 23ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:44.763 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getExamHistory] [17ms] [] FAILED_DB_OPERATION: $Proxy209.findByUserAndSessionTypeInWithFetch failed after 17ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:44.763 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [getExamHistory] [20ms] [] FAILED_SERVICE_METHOD: StudySessionService.getExamHistory failed after 20ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:44:44.764 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [21ms] [] FAILED_ENDPOINT: StudySessionController.getExamHistory failed after 21ms with error: InvalidDataAccessResourceUsageException
2025-07-18 22:49:09.893 [MessageBroker-10] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-18 22:49:10.233 [MessageBroker-14] INFO  PERFORMANCE - 
                [sendVerificationReminders] [262ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 262ms
2025-07-18 22:49:10.233 [MessageBroker-12] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [262ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 262ms
2025-07-18 22:49:10.234 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [220ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 220ms
2025-07-18 22:49:10.234 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [220ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 220ms
2025-07-18 22:49:10.240 [main] INFO  PERFORMANCE - 
                [] [132ms] [] DB_OPERATION: $Proxy211.count took 132ms
2025-07-18 22:49:10.420 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [116ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 116ms
2025-07-18 22:49:10.420 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [116ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 116ms
2025-07-18 22:49:10.528 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [546ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 546ms
2025-07-18 22:49:10.528 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [548ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 548ms
2025-07-18 22:49:10.529 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [638ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 638ms
2025-07-18 22:49:10.529 [MessageBroker-15] INFO  PERFORMANCE - 
                [cacheWarming] [638ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 638ms
2025-07-18 22:49:22.453 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getStudyActivityData] [138ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 138ms
2025-07-18 22:49:22.507 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getUserStudySessions] [178ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 178ms
2025-07-18 22:49:22.566 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getUserDecks] [163ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 163ms
2025-07-18 22:49:22.658 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [getDashboardData] [64ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 64ms with error: ConversionFailedException
2025-07-18 22:49:22.666 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [158ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 158ms
2025-07-18 22:49:22.913 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getUserDecks] [545ms] [] SERVICE_METHOD: DeckService.getUserDecks took 545ms
2025-07-18 22:49:23.072 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDashboardData] [523ms] [] SERVICE_METHOD: DashboardService.getDashboardData took 523ms
2025-07-18 22:51:44.200 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [603ms] [] SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 603ms
2025-07-18 22:53:54.375 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [62ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 62ms with error: ClassCastException
2025-07-18 22:53:54.376 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [63ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 63ms with error: ClassCastException
2025-07-18 22:53:55.447 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [searchDecks] [7ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 7ms with error: ClassCastException
2025-07-18 22:53:55.447 [http-nio-8082-exec-8] ERROR PERFORMANCE - 
                [] [8ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 8ms with error: ClassCastException
2025-07-18 22:53:57.543 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [searchDecks] [6ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 6ms with error: ClassCastException
2025-07-18 22:53:57.544 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [7ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 7ms with error: ClassCastException
2025-07-18 22:54:01.603 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [6ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 6ms with error: ClassCastException
2025-07-18 22:54:01.604 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [7ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 7ms with error: ClassCastException
2025-07-18 22:54:57.394 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [104ms] [] DB_OPERATION: $Proxy194.findByEmail took 104ms
2025-07-18 22:54:58.014 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [searchDecks] [150ms] [] DB_OPERATION: $Proxy206.advancedSearch took 150ms
2025-07-18 22:54:58.015 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [searchDecks] [170ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 170ms with error: ClassCastException
2025-07-18 22:54:58.016 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [171ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 171ms with error: ClassCastException
2025-07-18 22:54:59.843 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [searchDecks] [55ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 55ms with error: ClassCastException
2025-07-18 22:54:59.844 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [] [56ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 56ms with error: ClassCastException
2025-07-18 22:55:01.429 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [searchDecks] [403ms] [] DB_OPERATION: $Proxy206.advancedSearch took 403ms
2025-07-18 22:55:01.429 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [searchDecks] [413ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 413ms with error: ClassCastException
2025-07-18 22:55:01.429 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [413ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 413ms with error: ClassCastException
2025-07-18 22:55:04.522 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [] [1057ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 1057ms
2025-07-18 22:55:04.928 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getCurrentUser] [402ms] [] DB_OPERATION: $Proxy194.findById took 402ms
2025-07-18 22:55:06.177 [http-nio-8082-exec-5] WARN  PERFORMANCE - 
                [searchDecks] [1239ms] [] SLOW_DB_OPERATION: $Proxy206.advancedSearch took 1239ms
2025-07-18 22:55:06.180 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [1246ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 1246ms with error: ClassCastException
2025-07-18 22:55:06.180 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [1247ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 1247ms with error: ClassCastException
2025-07-18 22:55:07.604 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [203ms] [] DB_OPERATION: $Proxy194.findByEmail took 203ms
2025-07-18 22:55:09.249 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [114ms] [] DB_OPERATION: $Proxy194.findByEmail took 114ms
2025-07-18 22:55:09.251 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [] [110ms] [] DB_OPERATION: $Proxy194.findByEmail took 110ms
2025-07-18 22:55:09.257 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [106ms] [] DB_OPERATION: $Proxy194.findByEmail took 106ms
2025-07-18 22:55:09.533 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [searchDecks] [263ms] [] DB_OPERATION: $Proxy206.advancedSearch took 263ms
2025-07-18 22:55:09.533 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getUserDecks] [245ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 245ms
2025-07-18 22:55:09.534 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [267ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 267ms with error: ClassCastException
2025-07-18 22:55:09.534 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [267ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 267ms with error: ClassCastException
2025-07-18 22:55:11.319 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [1915ms] [] SLOW_DB_OPERATION: $Proxy194.findById took 1915ms
2025-07-18 22:55:11.319 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [1915ms] [] SLOW_SERVICE_METHOD: UserService.getCurrentUser took 1915ms
2025-07-18 22:55:11.320 [http-nio-8082-exec-9] INFO  PERFORMANCE - 
                [getCurrentSubscription] [393ms] [] DB_OPERATION: $Proxy194.findById took 393ms
2025-07-18 22:55:11.332 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [1929ms] [] SLOW_DB_OPERATION: $Proxy244.findByUser took 1929ms
2025-07-18 22:55:11.332 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [1931ms] [] SLOW_SERVICE_METHOD: UserPreferencesService.updateCurrentUserPreferences took 1931ms
2025-07-18 22:55:11.332 [http-nio-8082-exec-8] WARN  PERFORMANCE - 
                [updateCurrentUserPreferences] [1928ms] [] SLOW_SERVICE_METHOD: UserPreferencesService.updateCurrentUserPreferences took 1928ms
2025-07-18 22:55:11.333 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [] [1930ms] [] ENDPOINT: UserPreferencesController.updateCurrentUserPreferences took 1930ms
2025-07-18 22:55:11.333 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [1933ms] [] ENDPOINT: UserPreferencesController.updateCurrentUserPreferences took 1933ms
2025-07-18 22:55:11.340 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getUserDecks] [1807ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckId took 1807ms
2025-07-18 22:55:12.190 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [getUserDecks] [2906ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 2906ms
2025-07-18 22:55:12.191 [http-nio-8082-exec-2] WARN  PERFORMANCE - 
                [] [2909ms] [] SLOW_ENDPOINT: DeckController.getUserDecks took 2909ms
2025-07-18 22:55:17.763 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [searchDecks] [33ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 33ms with error: ClassCastException
2025-07-18 22:55:17.764 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [] [34ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 34ms with error: ClassCastException
2025-07-18 22:55:22.722 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [searchDecks] [10ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 10ms with error: ClassCastException
2025-07-18 22:55:22.722 [http-nio-8082-exec-3] ERROR PERFORMANCE - 
                [] [10ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 10ms with error: ClassCastException
2025-07-18 22:55:26.845 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [searchDecks] [6ms] [] FAILED_SERVICE_METHOD: SearchService.searchDecks failed after 6ms with error: ClassCastException
2025-07-18 22:55:26.845 [http-nio-8082-exec-1] ERROR PERFORMANCE - 
                [] [6ms] [] FAILED_ENDPOINT: SearchController.searchDecks failed after 6ms with error: ClassCastException
2025-07-18 23:22:13.079 [MessageBroker-10] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-18 23:22:13.438 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [228ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 228ms
2025-07-18 23:22:13.438 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [228ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 228ms
2025-07-18 23:22:13.444 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [197ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 197ms
2025-07-18 23:22:13.444 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [197ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 197ms
2025-07-18 23:22:13.448 [main] INFO  PERFORMANCE - 
                [] [139ms] [] DB_OPERATION: $Proxy211.count took 139ms
2025-07-18 23:22:13.722 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [646ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 646ms
2025-07-18 23:22:13.722 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [646ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 646ms
2025-07-18 23:22:42.209 [http-nio-8082-exec-4] ERROR PERFORMANCE - 
                [refreshToken] [46ms] [] FAILED_SERVICE_METHOD: AuthService.refreshToken failed after 46ms with error: TokenRefreshException
2025-07-18 23:22:57.086 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getStudyActivityData] [103ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 103ms
2025-07-18 23:22:57.130 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [getUserDecks] [110ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 110ms
2025-07-18 23:22:57.148 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [117ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 117ms
2025-07-18 23:22:57.215 [http-nio-8082-exec-7] ERROR PERFORMANCE - 
                [getDashboardData] [57ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 57ms with error: ConversionFailedException
2025-07-18 23:26:11.601 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [recordEnhancedCardPerformance] [577ms] [] SLOW_DB_OPERATION: $Proxy214.getAverageStudyTimeByCardId took 577ms
2025-07-18 23:26:11.624 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [620ms] [] SERVICE_METHOD: SpacedRepetitionService.calculateEnhancedNextReview took 620ms
2025-07-18 23:26:11.835 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [199ms] [] DB_OPERATION: $Proxy214.findRecentByCardIdAndUserLimit took 199ms
2025-07-18 23:26:11.843 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [recordEnhancedCardPerformance] [851ms] [] SERVICE_METHOD: StudySessionService.recordEnhancedCardPerformance took 851ms
2025-07-18 23:26:11.884 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getDeckByIdWithDetails] [242ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 242ms
2025-07-18 23:26:15.194 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [461ms] [] DB_OPERATION: $Proxy194.findByEmail took 461ms
2025-07-18 23:26:16.003 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [createStudySession] [768ms] [] SLOW_DB_OPERATION: $Proxy206.findById took 768ms
2025-07-18 23:26:16.197 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [createStudySession] [194ms] [] DB_OPERATION: $Proxy209.save took 194ms
2025-07-18 23:26:16.197 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [createStudySession] [966ms] [] SERVICE_METHOD: StudySessionServiceExtension.createStudySession took 966ms
2025-07-18 23:26:16.198 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [createStudySession] [967ms] [] SERVICE_METHOD: StudySessionService.createStudySession took 967ms
2025-07-18 23:26:16.691 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getCurrentUser] [281ms] [] DB_OPERATION: $Proxy194.findById took 281ms
2025-07-18 23:26:16.971 [http-nio-8082-exec-4] INFO  PERFORMANCE - 
                [getSessionRatings] [117ms] [] DB_OPERATION: $Proxy214.findByStudySessionAndUser took 117ms
2025-07-18 23:27:13.832 [ForkJoinPool.commonPool-worker-3] WARN  PERFORMANCE - 
                [] [735ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 735ms
2025-07-18 23:27:13.916 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [835ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 835ms
2025-07-18 23:27:13.916 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [850ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 850ms
2025-07-18 23:27:26.832 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [122ms] [] DB_OPERATION: $Proxy194.findByEmail took 122ms
2025-07-18 23:27:31.745 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getCardsByDeckId] [109ms] [] DB_OPERATION: $Proxy206.findByIdWithDetails took 109ms
2025-07-18 23:28:05.412 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [] [243ms] [] DB_OPERATION: $Proxy194.findByEmail took 243ms
2025-07-18 23:28:05.413 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [244ms] [] DB_OPERATION: $Proxy194.findByEmail took 244ms
2025-07-18 23:28:05.413 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [236ms] [] DB_OPERATION: $Proxy194.findByEmail took 236ms
2025-07-18 23:30:08.972 [http-nio-8082-exec-5] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [578ms] [] SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 578ms
2025-07-18 23:30:10.574 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [695ms] [] SLOW_DB_OPERATION: $Proxy207.findByDeck took 695ms
2025-07-18 23:30:10.732 [http-nio-8082-exec-7] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [1485ms] [] SLOW_SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 1485ms
2025-07-18 23:30:10.734 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [] [1495ms] [] ENDPOINT: StudySessionController.getStudyStatistics took 1495ms
2025-07-18 23:39:52.855 [MessageBroker-15] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [1ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 1ms with error: NullPointerException
2025-07-18 23:39:53.199 [MessageBroker-16] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [287ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 287ms
2025-07-18 23:39:53.199 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [287ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 287ms
2025-07-18 23:39:53.200 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [253ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 253ms
2025-07-18 23:39:53.200 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [253ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 253ms
2025-07-18 23:39:53.205 [main] INFO  PERFORMANCE - 
                [] [121ms] [] DB_OPERATION: $Proxy211.count took 121ms
2025-07-18 23:39:53.377 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [103ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 103ms
2025-07-18 23:39:53.377 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [103ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 103ms
2025-07-18 23:39:53.523 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [604ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 604ms
2025-07-18 23:39:53.525 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [607ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 607ms
2025-07-18 23:39:53.526 [MessageBroker-13] INFO  PERFORMANCE - 
                [periodicHealthCheck] [672ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 672ms
2025-07-18 23:39:53.526 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [673ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 673ms
2025-07-18 23:41:15.006 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [180ms] [] DB_OPERATION: $Proxy206.findIdsByCreator took 180ms
2025-07-18 23:41:15.028 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserDecks] [269ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 269ms
2025-07-18 23:41:15.295 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [232ms] [] DB_OPERATION: $Proxy207.findByDeckIdIn took 232ms
2025-07-18 23:41:15.365 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getUserDecks] [332ms] [] DB_OPERATION: $Proxy207.countByDeckId took 332ms
2025-07-18 23:41:15.401 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [103ms] [] DB_OPERATION: $Proxy214.getAggregatedPerformanceStatsByCardIds took 103ms
2025-07-18 23:41:15.904 [http-nio-8082-exec-10] WARN  PERFORMANCE - 
                [getUserDecks] [1157ms] [] SLOW_SERVICE_METHOD: DeckService.getUserDecks took 1157ms
2025-07-18 23:41:15.945 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [] [1222ms] [] ENDPOINT: DeckController.getUserDecks took 1222ms
2025-07-18 23:41:18.381 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [2178ms] [] SLOW_DB_OPERATION: $Proxy209.findByUserAndDateRange took 2178ms
2025-07-18 23:41:18.778 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [139ms] [] DB_OPERATION: $Proxy209.calculateLongestStreakOptimized took 139ms
2025-07-18 23:41:19.107 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [304ms] [] DB_OPERATION: $Proxy206.findByCreator took 304ms
2025-07-18 23:41:23.151 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [4044ms] [] SLOW_DB_OPERATION: $Proxy217.findByUserId took 4044ms
2025-07-18 23:41:23.151 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [4044ms] [] SLOW_SERVICE_METHOD: CollaborationService.getCollaborativeDecksByUser took 4044ms
2025-07-18 23:41:23.341 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [8647ms] [] SLOW_SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 8647ms
2025-07-18 23:41:23.346 [http-nio-8082-exec-1] WARN  PERFORMANCE - 
                [] [8655ms] [] SLOW_ENDPOINT: StudySessionController.getStudyStatistics took 8655ms
2025-07-18 23:41:40.483 [http-nio-8082-exec-3] WARN  PERFORMANCE - 
                [getComprehensiveStatistics] [1938ms] [] SLOW_SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 1938ms
2025-07-18 23:41:40.494 [http-nio-8082-exec-3] INFO  PERFORMANCE - 
                [] [1952ms] [] ENDPOINT: StudySessionController.getStudyStatistics took 1952ms
