# Study Session Navigation Fix

## Problem Description

Users were experiencing unexpected card transitions during study sessions where they would suddenly move to a different card without intentionally navigating. This was described as "suddenly when i'm standing on a card i move to another card."

## Root Cause Analysis

The issue was caused by multiple potential race conditions and event handling problems:

### 1. **Timeout Race Conditions**
- Multiple `setTimeout` calls were being created for navigation
- When users clicked buttons rapidly or performed actions quickly, multiple timeouts could be queued
- Previous timeouts weren't being properly cleared, causing delayed navigation to execute unexpectedly

### 2. **Event Propagation Issues**
- Click events could bubble up and trigger unintended navigation
- Keyboard events weren't properly prevented from causing accidental navigation

### 3. **Lack of Timeout Management**
- No centralized timeout management system
- Timeouts weren't being cleaned up on component unmount
- Race conditions between different navigation triggers

## Solution Applied

### 1. **Centralized Timeout Management**
Added a safe navigation system with proper timeout cleanup:

```javascript
const navigationTimeoutRef = useRef(null); // Navigation timeout reference for cleanup

// Helper function to safely handle navigation timeouts and prevent race conditions
const safeNavigateWithTimeout = useCallback((navigationFn, delay = TOTAL_WAIT_TIME) => {
  // Clear any existing navigation timeout to prevent race conditions
  if (navigationTimeoutRef.current) {
    clearTimeout(navigationTimeoutRef.current);
    navigationTimeoutRef.current = null;
  }

  // Set new timeout
  navigationTimeoutRef.current = setTimeout(() => {
    try {
      navigationFn();
    } catch (error) {
      console.error('StudySession: Navigation error:', error);
    } finally {
      navigationTimeoutRef.current = null;
    }
  }, delay);
}, []);
```

### 2. **Replaced All Unsafe setTimeout Calls**
Replaced all direct `setTimeout` calls with the safe navigation function:

- **Previous Card Navigation**: `handlePrevious()` function
- **Next Card Navigation**: `handleNext()` function  
- **Rating Navigation**: After rating a card
- **Skip Button Navigation**: Skip to next card
- **Card Dots Navigation**: Direct card selection
- **Keyboard Navigation**: Enter/Space key navigation

### 3. **Added Proper Cleanup**
Enhanced component cleanup to prevent memory leaks and race conditions:

```javascript
// Clear any pending navigation timeouts
if (navigationTimeoutRef.current) {
  clearTimeout(navigationTimeoutRef.current);
  navigationTimeoutRef.current = null;
}
```

### 4. **Keyboard Event Prevention**
Added protection against accidental keyboard navigation:

```javascript
// Prevent accidental navigation from keyboard events
useEffect(() => {
  const handleKeyDown = (e) => {
    // Prevent accidental navigation with common keys
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      // Only allow navigation if not typing in an input/textarea
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return; // Allow normal input behavior
      }
      
      // Prevent default browser navigation
      e.preventDefault();
      console.log('StudySession: Arrow key pressed, but preventing accidental navigation');
    }
    
    // Prevent accidental page navigation with backspace
    if (e.key === 'Backspace' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
      e.preventDefault();
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, []);
```

## Files Modified

- ✅ `frontend/src/pages/StudySession.jsx` - Complete navigation system overhaul

## Changes Made

### 1. **Added Safe Navigation System**
- `navigationTimeoutRef` for timeout management
- `safeNavigateWithTimeout()` helper function
- Proper timeout cleanup on component unmount

### 2. **Fixed All Navigation Functions**
- `handlePrevious()` - Safe timeout management
- `handleNext()` - Safe timeout management
- Rating navigation - Prevents race conditions
- Skip button navigation - Centralized timeout handling
- Card dots navigation - Safe direct navigation
- Keyboard navigation - Protected event handling

### 3. **Enhanced Event Prevention**
- Arrow key navigation prevention
- Backspace navigation prevention
- Input/textarea exclusions for normal typing

## Verification

The fix ensures that:
1. ✅ **No more race conditions** - Only one navigation timeout can be active at a time
2. ✅ **Proper cleanup** - All timeouts are cleared on component unmount
3. ✅ **Event prevention** - Accidental keyboard navigation is prevented
4. ✅ **Error handling** - Navigation errors are caught and logged
5. ✅ **Consistent behavior** - All navigation uses the same safe system

## Result

Users will now experience:
- ✅ **Stable card navigation** - No unexpected card transitions
- ✅ **Predictable behavior** - Navigation only happens when explicitly triggered
- ✅ **Better user experience** - No interruptions during study sessions
- ✅ **Improved reliability** - Race conditions eliminated

The fix addresses the root cause of unexpected navigation while maintaining all existing functionality and improving the overall stability of the study session experience.
