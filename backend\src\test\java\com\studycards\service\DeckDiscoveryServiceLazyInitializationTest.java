package com.studycards.service;

import com.studycards.dto.DeckResponse;
import com.studycards.enums.SubscriptionStatus;
import com.studycards.model.Deck;
import com.studycards.model.User;
import com.studycards.repository.CardRepository;
import com.studycards.repository.DeckRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * Test class to verify that LazyInitializationException is fixed in DeckDiscoveryService
 * when accessing lazy-loaded User properties through subscription status checks
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
class DeckDiscoveryServiceLazyInitializationTest {

    @Mock
    private DeckRepository deckRepository;

    @Mock
    private UserService userService;

    @Mock
    private CardRepository cardRepository;

    @Mock
    private DeckService deckService;

    @Mock
    private SubscriptionStatusService subscriptionStatusService;

    @InjectMocks
    private DeckDiscoveryService deckDiscoveryService;

    private User testUser;
    private Deck testDeck;
    private DeckResponse testDeckResponse;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .subscriptionStatus(SubscriptionStatus.ACTIVE)
                .build();

        testDeck = Deck.builder()
                .id(1L)
                .title("Test Deck")
                .description("Test Description")
                .creator(testUser)
                .isPublic(true)
                .deleted(false)
                .createdAt(LocalDateTime.now())
                .build();

        testDeckResponse = DeckResponse.builder()
                .id(1L)
                .title("Test Deck")
                .description("Test Description")
                .creatorUsername("testuser")
                .isPublic(true)
                .build();
    }

    @Test
    void getTrendingDecks_ShouldNotThrowLazyInitializationException() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        Page<Deck> mockDecks = new PageImpl<>(List.of(testDeck), pageable, 1);
        
        when(userService.getCurrentUserOrNull()).thenReturn(testUser);
        when(deckRepository.findTrendingDecks(any(LocalDateTime.class), any(Pageable.class)))
                .thenReturn(mockDecks);
        when(subscriptionStatusService.hasActiveSubscription(any(User.class))).thenReturn(true);
        when(deckService.mapToDeckResponse(any(Deck.class), any(User.class))).thenReturn(testDeckResponse);

        // When & Then - should not throw LazyInitializationException
        assertDoesNotThrow(() -> {
            Page<DeckResponse> result = deckDiscoveryService.getTrendingDecks(pageable, createdAfter);
            assertNotNull(result);
            assertEquals(1, result.getContent().size());
            assertEquals("Test Deck", result.getContent().get(0).getTitle());
        });

        // Verify that subscription status was checked (this would trigger LazyInitializationException if not fixed)
        verify(subscriptionStatusService).hasActiveSubscription(testUser);
    }

    @Test
    void getNewlyCreatedDecks_ShouldNotThrowLazyInitializationException() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        Page<Deck> mockDecks = new PageImpl<>(List.of(testDeck), pageable, 1);
        
        when(userService.getCurrentUserOrNull()).thenReturn(testUser);
        when(deckRepository.findByIsPublicTrueAndDeletedFalseAndCreatedAtAfter(any(LocalDateTime.class), any(Pageable.class)))
                .thenReturn(mockDecks);
        when(subscriptionStatusService.hasActiveSubscription(any(User.class))).thenReturn(true);
        when(deckService.mapToDeckResponse(any(Deck.class), any(User.class))).thenReturn(testDeckResponse);

        // When & Then - should not throw LazyInitializationException
        assertDoesNotThrow(() -> {
            Page<DeckResponse> result = deckDiscoveryService.getNewlyCreatedDecks(pageable, createdAfter);
            assertNotNull(result);
            assertEquals(1, result.getContent().size());
            assertEquals("Test Deck", result.getContent().get(0).getTitle());
        });

        // Verify that subscription status was checked (this would trigger LazyInitializationException if not fixed)
        verify(subscriptionStatusService).hasActiveSubscription(testUser);
    }

    @Test
    void getNewlyCreatedDecks_WithoutCreatedAfter_ShouldNotThrowLazyInitializationException() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        
        Page<Deck> mockDecks = new PageImpl<>(List.of(testDeck), pageable, 1);
        
        when(userService.getCurrentUserOrNull()).thenReturn(testUser);
        when(deckRepository.findByIsPublicTrueAndDeletedFalseOrderByCreatedAtDesc(any(Pageable.class)))
                .thenReturn(mockDecks);
        when(subscriptionStatusService.hasActiveSubscription(any(User.class))).thenReturn(true);
        when(deckService.mapToDeckResponse(any(Deck.class), any(User.class))).thenReturn(testDeckResponse);

        // When & Then - should not throw LazyInitializationException
        assertDoesNotThrow(() -> {
            Page<DeckResponse> result = deckDiscoveryService.getNewlyCreatedDecks(pageable, null);
            assertNotNull(result);
            assertEquals(1, result.getContent().size());
            assertEquals("Test Deck", result.getContent().get(0).getTitle());
        });

        // Verify that subscription status was checked (this would trigger LazyInitializationException if not fixed)
        verify(subscriptionStatusService).hasActiveSubscription(testUser);
    }

    @Test
    void getDecksByTag_ShouldNotThrowLazyInitializationException() {
        // Given
        String tagName = "physics";
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        Page<Deck> mockDecks = new PageImpl<>(List.of(testDeck), pageable, 1);
        
        when(userService.getCurrentUserOrNull()).thenReturn(testUser);
        when(deckRepository.findByTagNameIgnoreCaseAndCreatedAtAfter(anyString(), any(LocalDateTime.class), any(Pageable.class)))
                .thenReturn(mockDecks);
        when(subscriptionStatusService.hasActiveSubscription(any(User.class))).thenReturn(true);
        when(deckService.mapToDeckResponse(any(Deck.class), any(User.class))).thenReturn(testDeckResponse);

        // When & Then - should not throw LazyInitializationException
        assertDoesNotThrow(() -> {
            Page<DeckResponse> result = deckDiscoveryService.getDecksByTag(tagName, pageable, createdAfter);
            assertNotNull(result);
            assertEquals(1, result.getContent().size());
            assertEquals("Test Deck", result.getContent().get(0).getTitle());
        });

        // Verify that subscription status was checked (this would trigger LazyInitializationException if not fixed)
        verify(subscriptionStatusService).hasActiveSubscription(testUser);
    }

    @Test
    void getTrendingDecks_WithInactiveSubscription_ShouldFilterOutDeck() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        Page<Deck> mockDecks = new PageImpl<>(List.of(testDeck), pageable, 1);
        
        when(userService.getCurrentUserOrNull()).thenReturn(testUser);
        when(deckRepository.findTrendingDecks(any(LocalDateTime.class), any(Pageable.class)))
                .thenReturn(mockDecks);
        when(subscriptionStatusService.hasActiveSubscription(any(User.class))).thenReturn(false);

        // When
        Page<DeckResponse> result = deckDiscoveryService.getTrendingDecks(pageable, createdAfter);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getContent().size()); // Deck should be filtered out
        assertEquals(1, result.getTotalElements()); // But total count should remain the same

        // Verify that subscription status was checked
        verify(subscriptionStatusService).hasActiveSubscription(testUser);
        // Verify that mapToDeckResponse was not called since deck was filtered out
        verify(deckService, never()).mapToDeckResponse(any(Deck.class), any(User.class));
    }
}
