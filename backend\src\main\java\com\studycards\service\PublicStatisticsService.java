package com.studycards.service;

import com.studycards.dto.PublicStatisticsResponse;
import com.studycards.exception.RateLimitExceededException;
import com.studycards.repository.CardRepository;
import com.studycards.repository.DeckRepository;
import com.studycards.repository.DeckTagRepository;
import com.studycards.repository.StudySessionRepository;
import com.studycards.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;

/**
 * Service for providing public statistics about the application
 * Implements comprehensive error handling, validation, and security measures
 */
@Service
@Slf4j
@Validated
public class PublicStatisticsService {

    // Rate limiting: proper IP-based tracking with thread safety
    private final Map<String, Long> ipRequestTimes = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> ipRequestCounts = new ConcurrentHashMap<>();
    private static final long MIN_REQUEST_INTERVAL_MS = 1000; // 1 second between requests
    private static final int MAX_REQUESTS_PER_MINUTE = 60;
    private static final long CLEANUP_INTERVAL_MS = 300000; // 5 minutes
    private volatile long lastCleanupTime = 0;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DeckRepository deckRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private DeckTagRepository deckTagRepository;

    @Autowired
    private StudySessionRepository studySessionRepository;

    @Autowired
    private StatisticsMonitoringService monitoringService;

    /**
     * Get public statistics for the landing page
     * This method is cached to avoid frequent database queries
     * Includes rate limiting and comprehensive error handling
     *
     * @param request HTTP request for IP-based rate limiting
     * @return Public statistics response
     * @throws RateLimitExceededException if rate limit is exceeded
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "publicStatistics",
               key = "'global_' + T(java.time.LocalDate).now().toString()",
               condition = "#result != null")
    @NotNull
    public PublicStatisticsResponse getPublicStatistics(HttpServletRequest request) {
        // Start monitoring
        long operationStart = monitoringService.recordOperationStart("getPublicStatistics");
        boolean cacheHit = false;
        PublicStatisticsResponse response = null;

        try {
            // Rate limiting by IP address (skip for internal cache warming requests)
            String clientIp = getClientIpAddress(request);
            boolean isInternal = isInternalRequest(request);

            if (isInternal) {
                log.debug("Processing internal cache warming request from IP: {}", clientIp);
            } else if (isRateLimited(clientIp)) {
                log.warn("Rate limit exceeded for IP: {}", clientIp);
                throw new RateLimitExceededException("Too many requests. Please try again later.");
            }

            // Security headers validation
            validateSecurityHeaders(request);

            log.info("Starting public statistics calculation");

            // Calculate user statistics with monitoring
            long userStatsStart = monitoringService.recordOperationStart("getUserStatistics");
            PublicStatisticsResponse.UserStatistics userStats = executeWithTimeout(
                this::getUserStatistics, "getUserStatistics", 5000);
            monitoringService.recordOperationEnd("getUserStatistics", userStatsStart,
                userStats != null, null);

            // Calculate study statistics with monitoring
            long studyStatsStart = monitoringService.recordOperationStart("getStudyStatistics");
            PublicStatisticsResponse.StudyStatistics studyStats = executeWithTimeout(
                this::getStudyStatistics, "getStudyStatistics", 5000);
            monitoringService.recordOperationEnd("getStudyStatistics", studyStatsStart,
                studyStats != null, null);

            // Calculate content statistics with monitoring
            long contentStatsStart = monitoringService.recordOperationStart("getContentStatistics");
            PublicStatisticsResponse.ContentStatistics contentStats = executeWithTimeout(
                this::getContentStatistics, "getContentStatistics", 5000);
            monitoringService.recordOperationEnd("getContentStatistics", contentStatsStart,
                contentStats != null, null);

            // Validate all components are present
            if (userStats == null || studyStats == null || contentStats == null) {
                log.error("One or more statistics components returned null - userStats: {}, studyStats: {}, contentStats: {}",
                    userStats != null, studyStats != null, contentStats != null);
                response = createEmergencyFallbackResponse();
                monitoringService.recordOperationEnd("getPublicStatistics", operationStart, false, response);
                return response;
            }

            // Validate all statistics before building response
            validateUserStatistics(userStats);
            validateStudyStatistics(studyStats);
            validateContentStatistics(contentStats);

            // Build the response
            response = PublicStatisticsResponse.builder()
                    .userStatistics(userStats)
                    .studyStatistics(studyStats)
                    .contentStatistics(contentStats)
                    .build();

            log.info("Successfully calculated public statistics - Users: {}, Cards: {}, Decks: {}",
                userStats.getTotalUsers(), studyStats.getTotalCardsStudied(), contentStats.getTotalPublicDecks());

            // Record successful operation
            monitoringService.recordOperationEnd("getPublicStatistics", operationStart, true, response);
            return response;

        } catch (Exception e) {
            log.error("Critical error in getPublicStatistics: {}", e.getMessage(), e);
            response = createEmergencyFallbackResponse();
            monitoringService.recordOperationEnd("getPublicStatistics", operationStart, false, response);
            return response;
        }
    }

    /**
     * Execute a statistics calculation with timeout protection
     */
    private <T> T executeWithTimeout(java.util.function.Supplier<T> supplier, String operationName, long timeoutMs) {
        CompletableFuture<T> future = CompletableFuture.supplyAsync(supplier);
        try {
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("Operation {} timed out after {}ms", operationName, timeoutMs);
            future.cancel(true);
            return null;
        } catch (Exception e) {
            log.error("Operation {} failed: {}", operationName, e.getMessage());
            return null;
        }
    }

    /**
     * Check if this is an internal request (e.g., cache warming, daily summary)
     */
    private boolean isInternalRequest(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null && (userAgent.contains("StatisticsCacheWarmer") ||
                                   userAgent.contains("StatisticsDailySummary"));
    }

    /**
     * Check if IP is rate limited
     */
    private boolean isRateLimited(String clientIp) {
        long currentTime = System.currentTimeMillis();

        // Cleanup old entries periodically
        if (currentTime - lastCleanupTime > CLEANUP_INTERVAL_MS) {
            cleanupOldEntries(currentTime);
            lastCleanupTime = currentTime;
        }

        // Check request interval
        Long lastRequest = ipRequestTimes.get(clientIp);
        if (lastRequest != null && currentTime - lastRequest < MIN_REQUEST_INTERVAL_MS) {
            return true;
        }

        // Check requests per minute
        AtomicInteger requestCount = ipRequestCounts.computeIfAbsent(clientIp, k -> new AtomicInteger(0));
        if (requestCount.get() >= MAX_REQUESTS_PER_MINUTE) {
            return true;
        }

        // Update tracking
        ipRequestTimes.put(clientIp, currentTime);
        requestCount.incrementAndGet();

        return false;
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * Validate security headers
     */
    private void validateSecurityHeaders(HttpServletRequest request) {
        // Check for suspicious headers that might indicate automated requests
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null || userAgent.trim().isEmpty()) {
            log.warn("Request without User-Agent header from IP: {}", getClientIpAddress(request));
        }

        // Additional security validations can be added here
    }

    /**
     * Clean up old rate limiting entries
     */
    private void cleanupOldEntries(long currentTime) {
        ipRequestTimes.entrySet().removeIf(entry ->
            currentTime - entry.getValue() > 60000); // Remove entries older than 1 minute

        ipRequestCounts.entrySet().removeIf(entry -> {
            String ip = entry.getKey();
            Long lastRequest = ipRequestTimes.get(ip);
            return lastRequest == null || currentTime - lastRequest > 60000;
        });
    }

    /**
     * Create emergency fallback response when all else fails
     */
    private PublicStatisticsResponse createEmergencyFallbackResponse() {
        log.warn("Using emergency fallback statistics response");
        return PublicStatisticsResponse.builder()
                .userStatistics(PublicStatisticsResponse.UserStatistics.builder()
                        .totalUsers(1)
                        .activeUsersLast30Days(1)
                        .averageStudySessionsPerWeek(1)
                        .percentReportingImprovedGrades(0)
                        .build())
                .studyStatistics(PublicStatisticsResponse.StudyStatistics.builder()
                        .totalCardsStudied(0L)
                        .averageRetentionImprovement(0.0)
                        .averageStudyStreakDays(0)
                        .totalStudyHours(0)
                        .build())
                .contentStatistics(PublicStatisticsResponse.ContentStatistics.builder()
                        .totalPublicDecks(0)
                        .totalCards(0)
                        .totalTags(0)
                        .averageCardsPerDeck(0)
                        .build())
                .build();
    }

    private PublicStatisticsResponse.UserStatistics getUserStatistics() {
        try {
            // Count only users with valid subscription status (excludes EXPIRED/CANCELLED)
            long totalUsersLong = userRepository.countActiveSubscriptionUsers();
            int totalUsers = totalUsersLong > Integer.MAX_VALUE ? Integer.MAX_VALUE : (int) totalUsersLong;

            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            int activeUsers = 0;
            try {
                activeUsers = userRepository.countActiveUsersSince(thirtyDaysAgo);
            } catch (Exception e) {
                log.warn("Failed to get active users count: {}", e.getMessage());
                // Fallback: estimate based on total users (conservative 20% active rate)
                activeUsers = Math.max(1, totalUsers / 5);
            }

            // Calculate real percentage based on actual user feedback data
            // This should be replaced with actual survey/feedback data when available
            int percentReportingImprovedGrades = calculateRealImprovedGradesPercentage();

            // Calculate average study sessions per week with error handling
            int avgSessionsPerWeek = 0;
            try {
                avgSessionsPerWeek = studySessionRepository.getAverageSessionsPerWeek();
            } catch (Exception e) {
                log.warn("Failed to get average sessions per week: {}", e.getMessage());
                // Fallback: reasonable default based on active users
                avgSessionsPerWeek = activeUsers > 0 ? Math.max(1, activeUsers / 1000) : 1;
            }

            return PublicStatisticsResponse.UserStatistics.builder()
                    .totalUsers(totalUsers)
                    .activeUsersLast30Days(activeUsers)
                    .averageStudySessionsPerWeek(avgSessionsPerWeek)
                    .percentReportingImprovedGrades(percentReportingImprovedGrades)
                    .build();
        } catch (Exception e) {
            log.error("Critical error in getUserStatistics: {}", e.getMessage(), e);
            // Return minimal safe defaults
            return PublicStatisticsResponse.UserStatistics.builder()
                    .totalUsers(1)
                    .activeUsersLast30Days(1)
                    .averageStudySessionsPerWeek(1)
                    .percentReportingImprovedGrades(0)
                    .build();
        }
    }

    /**
     * Calculate real improved grades percentage based on actual user performance data
     * This replaces the hard-coded fake marketing value
     */
    private int calculateRealImprovedGradesPercentage() {
        try {
            // Get users who have been active for at least 30 days and have study data
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            long totalEligibleUsers = userRepository.countUsersWithStudyDataSince(thirtyDaysAgo);

            if (totalEligibleUsers == 0) {
                return 0; // No data available yet
            }

            // Count users showing improvement (accuracy > 70% in recent sessions)
            long improvedUsers = studySessionRepository.countUsersWithImprovement(thirtyDaysAgo);

            // Calculate percentage, ensuring it's realistic (not fake marketing claims)
            double percentage = (double) improvedUsers / totalEligibleUsers * 100.0;
            return Math.min(100, Math.max(0, (int) Math.round(percentage)));

        } catch (Exception e) {
            log.warn("Failed to calculate real improved grades percentage: {}", e.getMessage());
            // Return conservative estimate instead of fake marketing value
            return 0; // Be honest about lack of data
        }
    }

    private PublicStatisticsResponse.StudyStatistics getStudyStatistics() {
        try {
            long totalCardsStudied = 0;
            int averageStreak = 0;
            int totalStudyHours = 0;
            double averageRetentionImprovement = 0.0;

            // Get total cards studied with validation
            try {
                totalCardsStudied = studySessionRepository.getTotalCardsStudied();
                // Validate the result is reasonable
                if (totalCardsStudied < 0) {
                    log.warn("Invalid negative total cards studied: {}", totalCardsStudied);
                    totalCardsStudied = 0;
                }
            } catch (Exception e) {
                log.warn("Failed to get total cards studied: {}", e.getMessage());
                totalCardsStudied = 0;
            }

            // Get average study streak with proper calculation
            try {
                averageStreak = studySessionRepository.getAverageStudyStreakDays();
                // Validate streak is reasonable (max 365 days)
                if (averageStreak < 0 || averageStreak > 365) {
                    log.warn("Invalid average streak days: {}", averageStreak);
                    averageStreak = 0;
                }
            } catch (Exception e) {
                log.warn("Failed to get average study streak days: {}", e.getMessage());
                averageStreak = 0;
            }

            // Get total study hours with validation
            try {
                totalStudyHours = studySessionRepository.getTotalStudyHours();
                // Validate hours are reasonable
                if (totalStudyHours < 0) {
                    log.warn("Invalid negative total study hours: {}", totalStudyHours);
                    totalStudyHours = 0;
                }
            } catch (Exception e) {
                log.warn("Failed to get total study hours: {}", e.getMessage());
                totalStudyHours = 0;
            }

            // Calculate REAL retention improvement based on actual user performance
            averageRetentionImprovement = calculateRealRetentionImprovement();

            return PublicStatisticsResponse.StudyStatistics.builder()
                    .totalCardsStudied(totalCardsStudied)
                    .averageRetentionImprovement(averageRetentionImprovement)
                    .averageStudyStreakDays(averageStreak)
                    .totalStudyHours(totalStudyHours)
                    .build();

        } catch (Exception e) {
            log.error("Critical error in getStudyStatistics: {}", e.getMessage(), e);
            // Return safe defaults
            return PublicStatisticsResponse.StudyStatistics.builder()
                    .totalCardsStudied(0L)
                    .averageRetentionImprovement(0.0)
                    .averageStudyStreakDays(0)
                    .totalStudyHours(0)
                    .build();
        }
    }

    /**
     * Calculate real retention improvement based on actual user performance data
     * This replaces the hard-coded fake marketing value
     */
    private double calculateRealRetentionImprovement() {
        try {
            // Calculate retention improvement based on users' accuracy over time
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            LocalDateTime sixtyDaysAgo = LocalDateTime.now().minusDays(60);

            // Get average accuracy for recent period vs older period
            Double recentAccuracy = studySessionRepository.getAverageAccuracyForPeriod(thirtyDaysAgo, LocalDateTime.now());
            Double olderAccuracy = studySessionRepository.getAverageAccuracyForPeriod(sixtyDaysAgo, thirtyDaysAgo);

            if (recentAccuracy == null || olderAccuracy == null || olderAccuracy == 0) {
                return 0.0; // Not enough data for meaningful calculation
            }

            // Calculate improvement multiplier (e.g., 1.2x means 20% improvement)
            double improvement = recentAccuracy / olderAccuracy;

            // Cap at reasonable values (max 3x improvement, min 0.5x)
            improvement = Math.min(3.0, Math.max(0.5, improvement));

            return Math.round(improvement * 100.0) / 100.0; // Round to 2 decimal places

        } catch (Exception e) {
            log.warn("Failed to calculate real retention improvement: {}", e.getMessage());
            return 0.0; // Be honest about lack of data instead of fake marketing claims
        }
    }

    private PublicStatisticsResponse.ContentStatistics getContentStatistics() {
        try {
            int totalPublicDecks = 0;
            int totalCards = 0;
            int totalTags = 0;
            int avgCardsPerDeck = 0;

            // Get total public decks with error handling
            try {
                totalPublicDecks = deckRepository.countByPublicTrueAndDeletedFalse();
                if (totalPublicDecks < 0) {
                    log.warn("Invalid negative public decks count: {}", totalPublicDecks);
                    totalPublicDecks = 0;
                }
            } catch (Exception e) {
                log.warn("Failed to get total public decks: {}", e.getMessage());
                totalPublicDecks = 0;
            }

            // Get total cards with error handling
            try {
                totalCards = cardRepository.countByDeckDeletedFalse();
                if (totalCards < 0) {
                    log.warn("Invalid negative cards count: {}", totalCards);
                    totalCards = 0;
                }
            } catch (Exception e) {
                log.warn("Failed to get total cards: {}", e.getMessage());
                totalCards = 0;
            }

            // Get total tags with error handling
            try {
                totalTags = deckTagRepository.countDistinctTags();
                if (totalTags < 0) {
                    log.warn("Invalid negative tags count: {}", totalTags);
                    totalTags = 0;
                }
            } catch (Exception e) {
                log.warn("Failed to get total tags: {}", e.getMessage());
                totalTags = 0;
            }

            // Get average cards per deck with error handling and validation
            try {
                avgCardsPerDeck = deckRepository.getAverageCardsPerDeck();
                // Validate average is reasonable (0-1000 cards per deck)
                if (avgCardsPerDeck < 0 || avgCardsPerDeck > 1000) {
                    log.warn("Invalid average cards per deck: {}", avgCardsPerDeck);
                    // Calculate fallback if we have data
                    if (totalPublicDecks > 0 && totalCards > 0) {
                        avgCardsPerDeck = totalCards / totalPublicDecks;
                    } else {
                        avgCardsPerDeck = 0;
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to get average cards per deck: {}", e.getMessage());
                // Calculate fallback if we have data
                if (totalPublicDecks > 0 && totalCards > 0) {
                    avgCardsPerDeck = totalCards / totalPublicDecks;
                } else {
                    avgCardsPerDeck = 0;
                }
            }

            return PublicStatisticsResponse.ContentStatistics.builder()
                    .totalPublicDecks(totalPublicDecks)
                    .totalCards(totalCards)
                    .totalTags(totalTags)
                    .averageCardsPerDeck(avgCardsPerDeck)
                    .build();

        } catch (Exception e) {
            log.error("Critical error in getContentStatistics: {}", e.getMessage(), e);
            // Return safe defaults
            return PublicStatisticsResponse.ContentStatistics.builder()
                    .totalPublicDecks(0)
                    .totalCards(0)
                    .totalTags(0)
                    .averageCardsPerDeck(0)
                    .build();
        }
    }

    /**
     * Evict the public statistics cache
     * This should be called whenever data that affects public statistics changes
     */
    @CacheEvict(value = "publicStatistics", allEntries = true)
    public void evictPublicStatisticsCache() {
        log.info("Evicting public statistics cache due to data changes");

        // Record cache eviction for monitoring
        try {
            monitoringService.recordCacheEvent(false); // Cache miss will occur on next request
        } catch (Exception e) {
            log.warn("Failed to record cache eviction event: {}", e.getMessage());
        }
    }

    /**
     * Validate user statistics data for integrity
     */
    private void validateUserStatistics(PublicStatisticsResponse.UserStatistics stats) {
        if (stats.getTotalUsers() < 0) {
            throw new IllegalStateException("Total users cannot be negative: " + stats.getTotalUsers());
        }
        if (stats.getActiveUsersLast30Days() > stats.getTotalUsers()) {
            throw new IllegalStateException("Active users (" + stats.getActiveUsersLast30Days() +
                ") cannot exceed total users (" + stats.getTotalUsers() + ")");
        }
        if (stats.getPercentReportingImprovedGrades() < 0 || stats.getPercentReportingImprovedGrades() > 100) {
            throw new IllegalStateException("Improvement percentage must be 0-100, got: " +
                stats.getPercentReportingImprovedGrades());
        }
        if (stats.getAverageStudySessionsPerWeek() < 0) {
            throw new IllegalStateException("Average sessions cannot be negative: " +
                stats.getAverageStudySessionsPerWeek());
        }
    }

    /**
     * Validate study statistics data for integrity
     */
    private void validateStudyStatistics(PublicStatisticsResponse.StudyStatistics stats) {
        if (stats.getTotalCardsStudied() < 0) {
            throw new IllegalStateException("Total cards studied cannot be negative: " +
                stats.getTotalCardsStudied());
        }
        if (stats.getAverageRetentionImprovement() < 0 || stats.getAverageRetentionImprovement() > 10) {
            throw new IllegalStateException("Retention improvement must be 0-10x, got: " +
                stats.getAverageRetentionImprovement());
        }
        if (stats.getAverageStudyStreakDays() < 0) {
            throw new IllegalStateException("Average streak cannot be negative: " +
                stats.getAverageStudyStreakDays());
        }
        if (stats.getTotalStudyHours() < 0) {
            throw new IllegalStateException("Total study hours cannot be negative: " +
                stats.getTotalStudyHours());
        }
    }

    /**
     * Validate content statistics data for integrity
     */
    private void validateContentStatistics(PublicStatisticsResponse.ContentStatistics stats) {
        if (stats.getTotalPublicDecks() < 0) {
            throw new IllegalStateException("Total public decks cannot be negative: " +
                stats.getTotalPublicDecks());
        }
        if (stats.getTotalCards() < 0) {
            throw new IllegalStateException("Total cards cannot be negative: " +
                stats.getTotalCards());
        }
        if (stats.getTotalTags() < 0) {
            throw new IllegalStateException("Total tags cannot be negative: " +
                stats.getTotalTags());
        }
        if (stats.getAverageCardsPerDeck() < 0) {
            throw new IllegalStateException("Average cards per deck cannot be negative: " +
                stats.getAverageCardsPerDeck());
        }
    }
}
