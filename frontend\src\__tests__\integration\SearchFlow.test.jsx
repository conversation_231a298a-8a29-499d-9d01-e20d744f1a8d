// src/__tests__/integration/SearchFlow.test.jsx
import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from 'react-query';
import { <PERSON><PERSON>er<PERSON>outer, MemoryRouter } from 'react-router-dom';
import { SearchProvider } from '../../contexts/SearchContext';
import NewUnifiedSearch from '../../pages/NewUnifiedSearch';
import { searchAPI } from '../../api/search';

// Mock the search API
jest.mock('../../api/search', () => ({
  searchAPI: {
    unifiedSearchDecks: jest.fn(),
    unifiedSearchCards: jest.fn(),
    quickSearch: jest.fn(),
    getEnhancedSearchSuggestions: jest.fn()
  }
}));

// Mock other dependencies
jest.mock('react-hot-toast', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn()
  }
}));

jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    currentUser: { id: 1, username: 'testuser' },
    isAuthenticated: true
  })
}));

const createTestWrapper = (initialEntries = ['/app/search']) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { 
        retry: false,
        cacheTime: 0,
        staleTime: 0
      },
      mutations: { retry: false }
    }
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <MemoryRouter initialEntries={initialEntries}>
        <SearchProvider>
          {children}
        </SearchProvider>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('Search Flow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  test('complete deck search flow', async () => {
    const user = userEvent.setup();
    
    const mockDeckResults = {
      content: [
        {
          id: 1,
          title: 'JavaScript Fundamentals',
          description: 'Learn the basics of JavaScript',
          creator: { username: 'teacher1' },
          cardCount: 25,
          isPublic: true,
          tags: ['javascript', 'programming']
        },
        {
          id: 2,
          title: 'Advanced JavaScript',
          description: 'Advanced JavaScript concepts',
          creator: { username: 'teacher2' },
          cardCount: 40,
          isPublic: true,
          tags: ['javascript', 'advanced']
        }
      ],
      totalElements: 2,
      totalPages: 1,
      number: 0,
      size: 12
    };

    searchAPI.unifiedSearchDecks.mockResolvedValue(mockDeckResults);

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Verify initial state
    expect(screen.getByPlaceholderText(/search decks/i)).toBeInTheDocument();
    expect(screen.getByText('Decks')).toHaveClass(/bg-white/); // Active tab

    // Enter search query
    const searchInput = screen.getByPlaceholderText(/search decks/i);
    await user.type(searchInput, 'javascript');
    
    // Submit search
    await user.keyboard('{Enter}');

    // Wait for search results
    await waitFor(() => {
      expect(searchAPI.unifiedSearchDecks).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'javascript',
          searchContext: 'unified_search'
        })
      );
    });

    // Verify results are displayed
    await waitFor(() => {
      expect(screen.getByText('JavaScript Fundamentals')).toBeInTheDocument();
      expect(screen.getByText('Advanced JavaScript')).toBeInTheDocument();
      expect(screen.getByText('2 total results')).toBeInTheDocument();
    });
  });

  test('search type switching', async () => {
    const user = userEvent.setup();
    
    const mockCardResults = {
      content: [
        {
          id: 1,
          question: 'What is a closure?',
          answer: 'A closure is a function that has access to variables in its outer scope',
          deckTitle: 'JavaScript Fundamentals',
          difficultyLevel: 3
        }
      ],
      totalElements: 1,
      totalPages: 1,
      number: 0,
      size: 20
    };

    searchAPI.unifiedSearchCards.mockResolvedValue(mockCardResults);

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Switch to cards tab
    const cardsTab = screen.getByText('Cards');
    await user.click(cardsTab);

    // Verify tab is active
    expect(cardsTab).toHaveClass(/bg-white/);
    expect(screen.getByPlaceholderText(/search cards/i)).toBeInTheDocument();

    // Enter search query
    const searchInput = screen.getByPlaceholderText(/search cards/i);
    await user.type(searchInput, 'closure');
    await user.keyboard('{Enter}');

    // Wait for card search results
    await waitFor(() => {
      expect(searchAPI.unifiedSearchCards).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'closure'
        })
      );
    });

    await waitFor(() => {
      expect(screen.getByText('What is a closure?')).toBeInTheDocument();
    });
  });

  test('advanced filters functionality', async () => {
    const user = userEvent.setup();
    
    searchAPI.unifiedSearchDecks.mockResolvedValue({
      content: [],
      totalElements: 0,
      totalPages: 0,
      number: 0,
      size: 12
    });

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Open advanced filters
    const advancedFiltersButton = screen.getByText('Advanced Filters');
    await user.click(advancedFiltersButton);

    // Wait for filters to appear
    await waitFor(() => {
      expect(screen.getByText('Visibility')).toBeInTheDocument();
    });

    // Set visibility filter
    const visibilitySelect = screen.getByDisplayValue('All Decks');
    await user.selectOptions(visibilitySelect, 'Public Only');

    // Set tags filter
    const tagsInput = screen.getByPlaceholderText('Enter tags (comma-separated)');
    await user.type(tagsInput, 'javascript, react');

    // Set favorites only
    const favoritesCheckbox = screen.getByLabelText('Favorites Only');
    await user.click(favoritesCheckbox);

    // Perform search with filters
    const searchInput = screen.getByPlaceholderText(/search decks/i);
    await user.type(searchInput, 'test');
    await user.keyboard('{Enter}');

    // Verify API called with filters
    await waitFor(() => {
      expect(searchAPI.unifiedSearchDecks).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test',
          isPublic: true,
          tagNames: ['javascript', 'react'],
          favoritesOnly: true
        })
      );
    });

    // Verify filter chips are displayed
    await waitFor(() => {
      expect(screen.getByText('Public Decks')).toBeInTheDocument();
      expect(screen.getByText('#javascript')).toBeInTheDocument();
      expect(screen.getByText('#react')).toBeInTheDocument();
      expect(screen.getByText('Favorites Only')).toBeInTheDocument();
    });
  });

  test('filter chips removal', async () => {
    const user = userEvent.setup();
    
    searchAPI.unifiedSearchDecks.mockResolvedValue({
      content: [],
      totalElements: 0,
      totalPages: 0,
      number: 0,
      size: 12
    });

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Set up filters first
    const advancedFiltersButton = screen.getByText('Advanced Filters');
    await user.click(advancedFiltersButton);

    await waitFor(() => {
      expect(screen.getByText('Visibility')).toBeInTheDocument();
    });

    const visibilitySelect = screen.getByDisplayValue('All Decks');
    await user.selectOptions(visibilitySelect, 'Public Only');

    const tagsInput = screen.getByPlaceholderText('Enter tags (comma-separated)');
    await user.type(tagsInput, 'javascript');

    // Trigger search to show filter chips
    const searchInput = screen.getByPlaceholderText(/search decks/i);
    await user.type(searchInput, 'test');
    await user.keyboard('{Enter}');

    // Wait for filter chips to appear
    await waitFor(() => {
      expect(screen.getByText('Public Decks')).toBeInTheDocument();
      expect(screen.getByText('#javascript')).toBeInTheDocument();
    });

    // Remove a filter chip
    const javascriptChip = screen.getByText('#javascript').closest('div');
    const removeButton = within(javascriptChip).getByRole('button');
    await user.click(removeButton);

    // Verify chip is removed and new search is triggered
    await waitFor(() => {
      expect(screen.queryByText('#javascript')).not.toBeInTheDocument();
    });

    // Verify API called without the removed filter
    await waitFor(() => {
      expect(searchAPI.unifiedSearchDecks).toHaveBeenLastCalledWith(
        expect.objectContaining({
          query: 'test',
          isPublic: true,
          tagNames: [] // Should be empty after removal
        })
      );
    });
  });

  test('search history functionality', async () => {
    const user = userEvent.setup();
    
    searchAPI.unifiedSearchDecks.mockResolvedValue({
      content: [],
      totalElements: 0,
      totalPages: 0,
      number: 0,
      size: 12
    });

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Perform a search
    const searchInput = screen.getByPlaceholderText(/search decks/i);
    await user.type(searchInput, 'first search');
    await user.keyboard('{Enter}');

    await waitFor(() => {
      expect(searchAPI.unifiedSearchDecks).toHaveBeenCalled();
    });

    // Clear input and perform another search
    await user.clear(searchInput);
    await user.type(searchInput, 'second search');
    await user.keyboard('{Enter}');

    // Open search history
    const historyButton = screen.getByTitle('Search History');
    await user.click(historyButton);

    // Verify history items are shown
    await waitFor(() => {
      expect(screen.getByText('Recent Searches')).toBeInTheDocument();
      expect(screen.getByText('second search')).toBeInTheDocument();
      expect(screen.getByText('first search')).toBeInTheDocument();
    });

    // Click on a history item
    const firstSearchItem = screen.getByText('first search');
    await user.click(firstSearchItem);

    // Verify search input is updated and search is performed
    await waitFor(() => {
      expect(searchInput).toHaveValue('first search');
    });
  });

  test('URL synchronization', async () => {
    const user = userEvent.setup();
    
    searchAPI.unifiedSearchDecks.mockResolvedValue({
      content: [],
      totalElements: 0,
      totalPages: 0,
      number: 0,
      size: 12
    });

    // Start with URL parameters
    const TestWrapper = createTestWrapper(['/app/search?q=initial&type=decks']);
    render(<NewUnifiedSearch />, { wrapper: TestWrapper });

    // Verify initial state from URL
    await waitFor(() => {
      expect(screen.getByDisplayValue('initial')).toBeInTheDocument();
      expect(screen.getByText('Decks')).toHaveClass(/bg-white/);
    });

    // Change search type
    const cardsTab = screen.getByText('Cards');
    await user.click(cardsTab);

    // Verify URL would be updated (in real app)
    // Note: Testing URL updates in MemoryRouter requires additional setup
    expect(cardsTab).toHaveClass(/bg-white/);
  });

  test('error handling', async () => {
    const user = userEvent.setup();
    
    // Mock API to reject
    searchAPI.unifiedSearchDecks.mockRejectedValue(new Error('Search service unavailable'));

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Perform search
    const searchInput = screen.getByPlaceholderText(/search decks/i);
    await user.type(searchInput, 'error test');
    await user.keyboard('{Enter}');

    // Wait for error to be displayed
    await waitFor(() => {
      expect(screen.getByText(/Search service unavailable/)).toBeInTheDocument();
    });
  });

  test('empty state display', async () => {
    const user = userEvent.setup();
    
    searchAPI.unifiedSearchDecks.mockResolvedValue({
      content: [],
      totalElements: 0,
      totalPages: 0,
      number: 0,
      size: 12
    });

    render(<NewUnifiedSearch />, { wrapper: createTestWrapper() });

    // Initially should show empty state
    expect(screen.getByText('Start your search')).toBeInTheDocument();

    // Perform search with no results
    const searchInput = screen.getByPlaceholderText(/search decks/i);
    await user.type(searchInput, 'nonexistent');
    await user.keyboard('{Enter}');

    // Should show no results state
    await waitFor(() => {
      expect(screen.getByText('No results found')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your search terms or filters')).toBeInTheDocument();
    });
  });
});
