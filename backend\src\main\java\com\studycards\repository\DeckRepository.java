package com.studycards.repository;

import com.studycards.dto.TagSummary;
import com.studycards.model.Deck;
import com.studycards.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface DeckRepository extends JpaRepository<Deck, Long>, DeckRepositoryCustom {
    // Default methods should filter out deleted decks
    List<Deck> findByCreatorAndDeletedFalse(User creator);
    Page<Deck> findByCreatorAndDeletedFalse(User creator, Pageable pageable);

    // Methods to find deleted decks
    List<Deck> findByCreatorAndDeletedTrue(User creator);
    Page<Deck> findByCreatorAndDeletedTrue(User creator, Pageable pageable);
    Page<Deck> findByCreatorAndDeletedTrueOrderByDeletedAtDesc(User creator, Pageable pageable);

    // Legacy methods (should be updated to use the new ones)
    @Deprecated
    List<Deck> findByCreator(User creator);
    @Deprecated
    Page<Deck> findByCreator(User creator, Pageable pageable);

    /**
     * Find all deck IDs for a user
     *
     * @param userId The user ID
     * @return List of deck IDs
     */
    @Query("SELECT d.id FROM Deck d WHERE d.creator.id = :userId AND d.deleted = false")
    List<Long> findIdsByCreator(@Param("userId") Long userId);

    /**
     * PERFORMANCE FIX: Batch query for card counts by deck IDs
     */
    @Query("SELECT d.id, COUNT(c.id) FROM Deck d LEFT JOIN Card c ON d.id = c.deck.id WHERE d.id IN :deckIds GROUP BY d.id")
    List<Object[]> findCardCountsByDeckIds(@Param("deckIds") List<Long> deckIds);

    /**
     * PERFORMANCE FIX: Batch query for tags by deck IDs
     */
    @Query("SELECT dt.deck.id, dt.tagName FROM DeckTag dt WHERE dt.deck.id IN :deckIds")
    List<Object[]> findTagsByDeckIds(@Param("deckIds") List<Long> deckIds);

    /**
     * PERFORMANCE FIX: Batch query for favorite counts by deck IDs
     */
    @Query("SELECT d.id, COUNT(f.id) FROM Deck d LEFT JOIN d.favoriteBy f WHERE d.id IN :deckIds GROUP BY d.id")
    List<Object[]> findFavoriteCountsByDeckIds(@Param("deckIds") List<Long> deckIds);

    /**
     * PERFORMANCE FIX: Batch query for user favorites by deck IDs
     */
    @Query("SELECT d.id FROM Deck d JOIN d.favoriteBy f WHERE d.id IN :deckIds AND f.id = :userId")
    List<Long> findUserFavoriteDeckIds(@Param("userId") Long userId, @Param("deckIds") List<Long> deckIds);

    /**
     * Count favorites for a single deck
     */
    @Query("SELECT COUNT(f.id) FROM Deck d LEFT JOIN d.favoriteBy f WHERE d.id = :deckId")
    int countFavoritesByDeckId(@Param("deckId") Long deckId);

    // Find decks by creator and parent folder
    Page<Deck> findByCreatorAndParentDeckIdAndDeletedFalse(User creator, Long parentDeckId, Pageable pageable);

    // Find decks by creator and parent folder is null (root decks)
    Page<Deck> findByCreatorAndParentDeckIsNullAndDeletedFalse(User creator, Pageable pageable);

    // Find decks by creator and isFolder flag
    Page<Deck> findByCreatorAndIsFolderAndDeletedFalse(User creator, boolean isFolder, Pageable pageable);

    // Find decks by creator and public status
    Page<Deck> findByCreatorAndIsPublicAndDeletedFalse(User creator, boolean isPublic, Pageable pageable);

    // Legacy methods (should be updated to use the new ones)
    @Deprecated
    Page<Deck> findByCreatorAndParentDeckId(User creator, Long parentDeckId, Pageable pageable);
    @Deprecated
    Page<Deck> findByCreatorAndParentDeckIsNull(User creator, Pageable pageable);
    @Deprecated
    Page<Deck> findByCreatorAndIsFolder(User creator, boolean isFolder, Pageable pageable);
    @Deprecated
    Page<Deck> findByCreatorAndIsPublic(User creator, boolean isPublic, Pageable pageable);

    // Complex query to filter decks by multiple criteria
    @Query("SELECT d FROM Deck d WHERE d.creator = :creator " +
           "AND d.deleted = false " +
           "AND (:parentId IS NULL OR d.parentDeck.id = :parentId) " +
           "AND (:includeFolder IS NULL OR d.isFolder = :includeFolder) " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic)")
    Page<Deck> findByCreatorWithFilters(
            User creator,
            @Param("parentId") Long parentId,
            @Param("includeFolder") Boolean includeFolder,
            @Param("isPublic") Boolean isPublic,
            Pageable pageable);

    // Complex query to find deleted decks by multiple criteria
    @Query("SELECT d FROM Deck d WHERE d.creator = :creator " +
           "AND d.deleted = true " +
           "AND (:parentId IS NULL OR d.originalParentId = :parentId) " +
           "AND (:includeFolder IS NULL OR d.isFolder = :includeFolder) " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic)")
    Page<Deck> findDeletedByCreatorWithFilters(
            User creator,
            @Param("parentId") Long parentId,
            @Param("includeFolder") Boolean includeFolder,
            @Param("isPublic") Boolean isPublic,
            Pageable pageable);

    /**
     * Find all public decks with pagination and sorting
     * Updated to include subscription status filtering for StudyCards MVP
     */
    @Query("SELECT d FROM Deck d WHERE d.isPublic = true AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    Page<Deck> findByIsPublicTrueAndDeletedFalse(Pageable pageable);

    // Legacy method (should be updated to use the new one)
    @Deprecated
    Page<Deck> findByIsPublicTrue(Pageable pageable);

    /**
     * Find popular public decks based on multiple factors:
     * - Number of favorites
     * - Number of recent study sessions
     * - Creation date (newer decks get a boost)
     *
     * OPTIMIZED VERSION: Replaced inefficient SIZE() function with native SQL
     * Includes subscription status filtering for StudyCards MVP requirements
     */
    @Query(value = "SELECT d.*, COALESCE(favorite_count, 0) as popularity_score " +
           "FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "LEFT JOIN (" +
           "    SELECT deck_id, COUNT(*) as favorite_count " +
           "    FROM user_favorite_decks " +
           "    GROUP BY deck_id" +
           ") f ON d.id = f.deck_id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') " +
           "ORDER BY popularity_score DESC, d.created_at DESC",
           countQuery = "SELECT COUNT(d.id) FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')",
           nativeQuery = true)
    Page<Deck> findPopularPublicDecks(Pageable pageable);

    /**
     * Enhanced method to find popular decks with multiple weighted factors:
     * - Number of favorites (highest weight)
     * - Number of recent study sessions within the specified time period
     * - Recency of the deck (newer decks get a boost)
     *
     * OPTIMIZED VERSION: Replaced inefficient SIZE() and subqueries with native SQL
     * Includes subscription status filtering for StudyCards MVP requirements
     *
     * @param timeFrame The time period to consider for recent study sessions
     * @param pageable Pagination information
     * @return Page of popular decks
     */
    @Query(value = "SELECT d.*, " +
           "(COALESCE(f.favorite_count, 0) * 3 + " +
           " COALESCE(s.session_count, 0) + " +
           " CASE WHEN d.created_at > :timeFrame THEN 2 ELSE 0 END) as popularity_score " +
           "FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "LEFT JOIN (" +
           "    SELECT deck_id, COUNT(*) as favorite_count " +
           "    FROM user_favorite_decks " +
           "    GROUP BY deck_id" +
           ") f ON d.id = f.deck_id " +
           "LEFT JOIN (" +
           "    SELECT deck_id, COUNT(*) as session_count " +
           "    FROM study_sessions " +
           "    WHERE start_time > :timeFrame " +
           "    GROUP BY deck_id" +
           ") s ON d.id = s.deck_id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') " +
           "ORDER BY popularity_score DESC, d.created_at DESC",
           countQuery = "SELECT COUNT(d.id) FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')",
           nativeQuery = true)
    Page<Deck> findEnhancedPopularPublicDecks(
            @Param("timeFrame") LocalDateTime timeFrame,
            Pageable pageable);

    /**
     * Search public decks by title and description
     * Updated to include subscription status filtering for StudyCards MVP
     */
    @Query("SELECT d FROM Deck d WHERE d.isPublic = true AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED') " +
           "AND (LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')))")
    Page<Deck> searchPublicDecks(String query, Pageable pageable);

    @Deprecated
    default List<Deck> findByIsPublicTrueAndDeletedFalseAndTagsName(String tagName) {
        return findByTagNameIgnoreCase(tagName, PageRequest.of(0, Integer.MAX_VALUE)).getContent();
    }

    @Deprecated
    default List<Deck> findByIsPublicTrueAndTagsName(String tagName) {
        return findByIsPublicTrueAndDeletedFalseAndTagsName(tagName);
    }

    /**
     * Find popular public decks with details (tags and creator) eagerly loaded
     * FIXED VERSION: Using native SQL to avoid JPQL subquery issues in ORDER BY
     * Includes subscription status filtering for StudyCards MVP requirements
     */
    @Query(value = "SELECT d.*, COALESCE(f.favorite_count, 0) as popularity_score " +
           "FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "LEFT JOIN (" +
           "    SELECT deck_id, COUNT(*) as favorite_count " +
           "    FROM user_favorite_decks " +
           "    GROUP BY deck_id" +
           ") f ON d.id = f.deck_id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') " +
           "ORDER BY popularity_score DESC, d.created_at DESC",
           countQuery = "SELECT COUNT(d.id) FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')",
           nativeQuery = true)
    Page<Deck> findPopularPublicDecksWithDetails(Pageable pageable);

    /**
     * Search public decks with optimized query and eager loading
     * Updated to include subscription status filtering for StudyCards MVP
     */
    @Query(value = "SELECT DISTINCT d FROM Deck d " +
            "LEFT JOIN FETCH d.deckTags " +
            "JOIN FETCH d.creator " +
            "WHERE d.isPublic = true AND d.deleted = false " +
            "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED') " +
            "AND (LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')))",
            countQuery = "SELECT COUNT(DISTINCT d) FROM Deck d " +
            "WHERE d.isPublic = true AND d.deleted = false " +
            "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED') " +
            "AND (LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')))")
    Page<Deck> searchPublicDecksOptimized(String query, Pageable pageable);

    /**
     * Advanced search query with multiple criteria
     * Searches in title, description, and tags
     * Can filter by public/private, creator, and more
     * Optimized with better index usage and query structure
     * Removed ORDER BY from query to avoid conflicts with Pageable sorting
     */
    @Query(value = "SELECT DISTINCT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags dt " +
           "LEFT JOIN FETCH d.creator " +
           "WHERE d.deleted = false " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    d.title LIKE CONCAT('%', :query, '%') OR " +
           "    d.description LIKE CONCAT('%', :query, '%') OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE dt2.tagName LIKE CONCAT('%', :query, '%'))) " +
           "AND (:tagName IS NULL OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName = :tagName))",
           countQuery = "SELECT COUNT(DISTINCT d) FROM Deck d " +
           "LEFT JOIN d.deckTags dt " +
           "WHERE d.deleted = false " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    d.title LIKE CONCAT('%', :query, '%') OR " +
           "    d.description LIKE CONCAT('%', :query, '%') OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE dt2.tagName LIKE CONCAT('%', :query, '%'))) " +
           "AND (:tagName IS NULL OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName = :tagName))")
    Page<Deck> advancedSearch(
            @Param("query") String query,
            @Param("isPublic") Boolean isPublic,
            @Param("creatorId") Long creatorId,
            @Param("tagName") String tagName,
            Pageable pageable);

    /**
     * Enhanced advanced search query with multiple criteria and multiple tags
     * Searches in title, description, and tags
     * Can filter by public/private, creator, difficulty, card count, and more
     * Optimized with better index usage and query structure
     */
    @Query(value = "SELECT DISTINCT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags dt " +
           "LEFT JOIN FETCH d.creator " +
           "WHERE d.deleted = false " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    d.title LIKE CONCAT('%', :query, '%') OR " +
           "    d.description LIKE CONCAT('%', :query, '%') OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE dt2.tagName LIKE CONCAT('%', :query, '%'))) " +
           "AND (COALESCE(:tagNamesSize, 0) = 0 OR " +
           "    (SELECT COUNT(DISTINCT dt3.tagName) FROM d.deckTags dt3 WHERE dt3.tagName IN (:tagNames)) = :tagNamesSize) " +
           "AND (:isFolder IS NULL OR d.isFolder = :isFolder) " +
           "AND (:parentFolderId IS NULL OR d.parentDeck.id = :parentFolderId) " +
           "AND (:isCollaborative IS NULL OR " +
           "    (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR " +
           "    (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) " +
           "ORDER BY d.createdAt DESC",
           countQuery = "SELECT COUNT(DISTINCT d) FROM Deck d " +
           "LEFT JOIN d.deckTags dt " +
           "WHERE d.deleted = false " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    d.title LIKE CONCAT('%', :query, '%') OR " +
           "    d.description LIKE CONCAT('%', :query, '%') OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE dt2.tagName LIKE CONCAT('%', :query, '%'))) " +
           "AND (COALESCE(:tagNamesSize, 0) = 0 OR " +
           "    (SELECT COUNT(DISTINCT dt3.tagName) FROM d.deckTags dt3 WHERE dt3.tagName IN (:tagNames)) = :tagNamesSize) " +
           "AND (:isFolder IS NULL OR d.isFolder = :isFolder) " +
           "AND (:parentFolderId IS NULL OR d.parentDeck.id = :parentFolderId) " +
           "AND (:isCollaborative IS NULL OR " +
           "    (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR " +
           "    (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)))")
    Page<Deck> enhancedAdvancedSearch(
            @Param("query") String query,
            @Param("isPublic") Boolean isPublic,
            @Param("creatorId") Long creatorId,
            @Param("tagNames") List<String> tagNames,
            @Param("tagNamesSize") Integer tagNamesSize,
            @Param("isFolder") Boolean isFolder,
            @Param("parentFolderId") Long parentFolderId,
            @Param("isCollaborative") Boolean isCollaborative,
            Pageable pageable);

    /**
     * Advanced search query for deleted decks
     */
    @Query(value = "SELECT DISTINCT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags dt " +
           "LEFT JOIN FETCH d.creator " +
           "WHERE d.deleted = true " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    EXISTS (SELECT dt2 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) " +
           "AND (:tagName IS NULL OR EXISTS (SELECT dt3 FROM d.deckTags dt3 WHERE LOWER(dt3.tagName) = LOWER(:tagName))) " +
           "ORDER BY d.deletedAt DESC",
           countQuery = "SELECT COUNT(DISTINCT d) FROM Deck d LEFT JOIN d.deckTags dt " +
           "WHERE d.deleted = true " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    EXISTS (SELECT dt2 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) " +
           "AND (:tagName IS NULL OR EXISTS (SELECT dt3 FROM d.deckTags dt3 WHERE LOWER(dt3.tagName) = LOWER(:tagName)))")
    Page<Deck> advancedSearchDeleted(
            @Param("query") String query,
            @Param("isPublic") Boolean isPublic,
            @Param("creatorId") Long creatorId,
            @Param("tagName") String tagName,
            Pageable pageable);

    /**
     * Find root decks (decks with no parent) for a user with eager loading of child decks
     * to avoid N+1 queries when building the hierarchy
     *
     * @param userId The user ID
     * @return List of root decks with child decks loaded
     */
    @Query("SELECT DISTINCT d, d.isFolder as folderFlag, d.title as titleSort FROM Deck d LEFT JOIN FETCH d.childDecks c LEFT JOIN FETCH c.childDecks " +
           "WHERE d.creator.id = :userId AND d.parentDeck IS NULL AND d.deleted = false " +
           "ORDER BY folderFlag DESC, titleSort ASC")
    List<Deck> findRootDecksByCreatorId(Long userId);

    List<Deck> findByParentDeckIdAndDeletedFalse(Long parentDeckId);

    @Deprecated
    List<Deck> findByParentDeckId(Long parentDeckId);

    /**
     * Find root decks (decks with no parent) for a user with pagination
     *
     * @param userId The user ID
     * @param pageable Pagination information
     * @return Page of root decks
     */
    @Query(value = "SELECT d, d.isFolder as folderFlag, d.title as titleSort FROM Deck d WHERE d.creator.id = :userId AND d.parentDeck IS NULL AND d.deleted = false " +
           "ORDER BY folderFlag DESC, titleSort ASC",
           countQuery = "SELECT COUNT(d) FROM Deck d WHERE d.creator.id = :userId AND d.parentDeck IS NULL AND d.deleted = false")
    Page<Deck> findRootDecksByCreatorIdPaginated(Long userId, Pageable pageable);

    /**
     * Find child decks for a given parent deck
     *
     * @param parentDeckId The parent deck ID
     * @return List of child decks
     */
    @Query("SELECT d, d.isFolder as folderFlag, d.title as titleSort FROM Deck d WHERE d.parentDeck.id = :parentDeckId AND d.deleted = false " +
           "ORDER BY folderFlag DESC, titleSort ASC")
    List<Deck> findChildDecks(Long parentDeckId);

    // Find decks by tag - these methods are deprecated, use DeckRepositoryCustom methods instead
    @Deprecated
    default Page<Deck> findByIsPublicTrueAndDeletedFalseAndTagsNameIgnoreCase(String tagName, Pageable pageable) {
        return findByTagNameIgnoreCase(tagName, pageable);
    }

    // Find decks by tag and creation date - these methods are deprecated, use DeckRepositoryCustom methods instead
    @Deprecated
    default Page<Deck> findByIsPublicTrueAndDeletedFalseAndTagsNameIgnoreCaseAndCreatedAtAfter(
            String tagName, LocalDateTime createdAfter, Pageable pageable) {
        return findByTagNameIgnoreCaseAndCreatedAtAfter(tagName, createdAfter, pageable);
    }

    @Deprecated
    default Page<Deck> findByIsPublicTrueAndTagsNameIgnoreCase(String tagName, Pageable pageable) {
        return findByTagNameIgnoreCase(tagName, pageable);
    }

    @Deprecated
    default Page<Deck> findByIsPublicTrueAndTagsNameIgnoreCaseAndCreatedAtAfter(
            String tagName, LocalDateTime createdAfter, Pageable pageable) {
        return findByTagNameIgnoreCaseAndCreatedAtAfter(tagName, createdAfter, pageable);
    }

    /**
     * Find newly created public decks
     * Updated to include subscription status filtering for StudyCards MVP
     * Removed ORDER BY from query to avoid conflicts with Pageable sorting
     */
    @Query("SELECT d FROM Deck d WHERE d.isPublic = true AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    Page<Deck> findByIsPublicTrueAndDeletedFalseOrderByCreatedAtDesc(Pageable pageable);

    /**
     * Find newly created public decks after a specific date
     * Updated to include subscription status filtering for StudyCards MVP
     * Removed ORDER BY from query to avoid conflicts with Pageable sorting
     */
    @Query("SELECT d FROM Deck d WHERE d.isPublic = true AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED') " +
           "AND d.createdAt > :createdAfter")
    Page<Deck> findByIsPublicTrueAndDeletedFalseAndCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);

    @Deprecated
    Page<Deck> findByIsPublicTrueOrderByCreatedAtDesc(Pageable pageable);

    @Deprecated
    Page<Deck> findByIsPublicTrueAndCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);

    /**
     * Find trending decks based on recent favorite counts and study sessions
     * OPTIMIZED VERSION: Replaced inefficient SIZE() and subqueries with native SQL
     * Includes subscription status filtering for StudyCards MVP requirements
     */
    @Query(value = "SELECT d.*, " +
           "(COALESCE(f.favorite_count, 0) + COALESCE(s.session_count, 0)) as trending_score " +
           "FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "LEFT JOIN (" +
           "    SELECT deck_id, COUNT(*) as favorite_count " +
           "    FROM user_favorite_decks " +
           "    GROUP BY deck_id" +
           ") f ON d.id = f.deck_id " +
           "LEFT JOIN (" +
           "    SELECT deck_id, COUNT(*) as session_count " +
           "    FROM study_sessions " +
           "    WHERE start_time > :recentDate " +
           "    GROUP BY deck_id" +
           ") s ON d.id = s.deck_id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') " +
           "ORDER BY trending_score DESC, d.created_at DESC",
           countQuery = "SELECT COUNT(d.id) FROM decks d " +
           "JOIN users u ON d.creator_id = u.id " +
           "WHERE d.is_public = 1 AND d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')",
           nativeQuery = true)
    Page<Deck> findTrendingDecks(
            @Param("recentDate") LocalDateTime recentDate,
            Pageable pageable);

    // Find popular tags - this method is deprecated, use DeckRepositoryCustom.findPopularTags instead
    @Deprecated
    default List<TagSummary> findPopularTags(Pageable pageable) {
        return findPopularTagsFromDeckTags(pageable);
    }

    /**
     * Count the number of public, non-deleted decks
     * Excludes decks from users with EXPIRED or CANCELLED subscription status
     *
     * @return The number of public decks
     */
    @Query("SELECT COUNT(d) FROM Deck d WHERE d.isPublic = true AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    int countByPublicTrueAndDeletedFalse();

    /**
     * Get the average number of cards per deck
     * Using a subquery approach that's compatible with SQL Server
     * Excludes decks from users with EXPIRED or CANCELLED subscription status
     *
     * @return The average number of cards per deck
     */
    @Query(value = "SELECT COALESCE(AVG(CAST(card_count AS FLOAT)), 0) FROM " +
           "(SELECT COUNT(c.id) as card_count FROM decks d " +
           "LEFT JOIN cards c ON d.id = c.deck_id " +
           "JOIN users u ON d.creator_id = u.id " +
           "WHERE d.deleted = 0 " +
           "AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') " +
           "GROUP BY d.id) as deck_card_counts", nativeQuery = true)
    int getAverageCardsPerDeck();

    long countByCreatorAndIsPublicTrueAndDeletedFalse(User creator);

    @Deprecated
    long countByCreatorAndIsPublicTrue(User creator);

    long countByCreator(User creator);

    /**
     * Count the number of non-deleted decks created by a user
     *
     * @param creatorId The user ID
     * @return The number of decks
     */
    @Query("SELECT COUNT(d) FROM Deck d WHERE d.creator.id = :creatorId AND d.deleted = false")
    long countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * Find public decks by multiple creators
     * Updated to include subscription status filtering for StudyCards MVP
     */
    @Query("SELECT d FROM Deck d WHERE d.isPublic = true AND d.deleted = false " +
           "AND d.creator IN :creators " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED') " +
           "ORDER BY d.createdAt DESC")
    Page<Deck> findPublicDecksByCreatorIn(Set<User> creators, Pageable pageable);

    List<Deck> findTop5ByCreatorOrderByCreatedAtDesc(User creator);

    /**
     * Find decks that a user has favorited with pagination
     * Updated to include subscription status filtering for StudyCards MVP
     */
    @Query("SELECT d FROM Deck d JOIN d.favoriteBy u WHERE u.id = :userId AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    Page<Deck> findFavoritesByUserId(Long userId, Pageable pageable);

    /**
     * Search within user's favorite decks
     *
     * @param userId The user ID
     * @param query Search query
     * @param pageable Pagination information
     * @return Page of matching favorite decks
     */
    @Query("SELECT d FROM Deck d " +
           "JOIN d.favoriteBy u " +
           "LEFT JOIN d.deckTags dt " +
           "WHERE u.id = :userId " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED') " +
           "AND (:query IS NULL OR " +
           "    LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%'))))")
    Page<Deck> searchUserFavorites(@Param("userId") Long userId,
                                  @Param("query") String query,
                                  Pageable pageable);

    /**
     * Unified advanced search with database-level subscription filtering and comprehensive criteria
     * This consolidates all search functionality into a single, optimized query
     */
    @Query(value = "SELECT DISTINCT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags dt " +
           "LEFT JOIN FETCH d.creator c " +
           "LEFT JOIN d.favoriteBy f " +
           "WHERE d.deleted = :includeDeleted " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) " +
           "AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName IN :tagNames)) " +
           "AND (:minDifficulty IS NULL OR " +
           "    (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR " +
           "    (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) <= :maxDifficulty) " +
           "AND (:minCardCount IS NULL OR " +
           "    (SELECT COUNT(c) FROM Card c WHERE c.deck = d AND c.deleted = false) >= :minCardCount) " +
           "AND (:maxCardCount IS NULL OR " +
           "    (SELECT COUNT(c) FROM Card c WHERE c.deck = d AND c.deleted = false) <= :maxCardCount) " +
           "AND (:isFolder IS NULL OR d.isFolder = :isFolder) " +
           "AND (:parentFolderId IS NULL OR d.parentDeck.id = :parentFolderId) " +
           "AND (:isCollaborative IS NULL OR " +
           "    (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR " +
           "    (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) " +
           "AND (:createdAfter IS NULL OR CAST(d.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(d.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(d.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(d.updatedAt AS date) <= :updatedBefore) " +
           "AND (:favoritesOnly = false OR f.id = :currentUserId) " +
           "AND (:includeExpiredCreators = true OR c.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')) " +
           "AND (d.creator.id = :currentUserId OR d.isPublic = true OR " +
           "     EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :currentUserId))",
           countQuery = "SELECT COUNT(DISTINCT d) FROM Deck d " +
           "LEFT JOIN d.deckTags dt " +
           "LEFT JOIN d.creator c " +
           "LEFT JOIN d.favoriteBy f " +
           "WHERE d.deleted = :includeDeleted " +
           "AND (:isPublic IS NULL OR d.isPublic = :isPublic) " +
           "AND (:creatorId IS NULL OR d.creator.id = :creatorId) " +
           "AND (:query IS NULL OR " +
           "    LOWER(d.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(d.description) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    EXISTS (SELECT 1 FROM d.deckTags dt2 WHERE LOWER(dt2.tagName) LIKE LOWER(CONCAT('%', :query, '%')))) " +
           "AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM d.deckTags dt3 WHERE dt3.tagName IN :tagNames)) " +
           "AND (:minDifficulty IS NULL OR " +
           "    (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR " +
           "    (SELECT AVG(CAST(c.difficultyLevel AS FLOAT)) FROM Card c WHERE c.deck = d AND c.deleted = false) <= :maxDifficulty) " +
           "AND (:minCardCount IS NULL OR " +
           "    (SELECT COUNT(c) FROM Card c WHERE c.deck = d AND c.deleted = false) >= :minCardCount) " +
           "AND (:maxCardCount IS NULL OR " +
           "    (SELECT COUNT(c) FROM Card c WHERE c.deck = d AND c.deleted = false) <= :maxCardCount) " +
           "AND (:isFolder IS NULL OR d.isFolder = :isFolder) " +
           "AND (:parentFolderId IS NULL OR d.parentDeck.id = :parentFolderId) " +
           "AND (:isCollaborative IS NULL OR " +
           "    (:isCollaborative = true AND EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d)) OR " +
           "    (:isCollaborative = false AND NOT EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d))) " +
           "AND (:createdAfter IS NULL OR CAST(d.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(d.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(d.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(d.updatedAt AS date) <= :updatedBefore) " +
           "AND (:favoritesOnly = false OR f.id = :currentUserId) " +
           "AND (:includeExpiredCreators = true OR c.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')) " +
           "AND (d.creator.id = :currentUserId OR d.isPublic = true OR " +
           "     EXISTS (SELECT 1 FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :currentUserId))")
    Page<Deck> unifiedAdvancedSearch(
            @Param("query") String query,
            @Param("isPublic") Boolean isPublic,
            @Param("creatorId") Long creatorId,
            @Param("tagNames") List<String> tagNames,
            @Param("tagNamesEmpty") boolean tagNamesEmpty,
            @Param("minDifficulty") Integer minDifficulty,
            @Param("maxDifficulty") Integer maxDifficulty,
            @Param("minCardCount") Integer minCardCount,
            @Param("maxCardCount") Integer maxCardCount,
            @Param("includeDeleted") boolean includeDeleted,
            @Param("isFolder") Boolean isFolder,
            @Param("parentFolderId") Long parentFolderId,
            @Param("isCollaborative") Boolean isCollaborative,
            @Param("createdAfter") java.time.LocalDate createdAfter,
            @Param("createdBefore") java.time.LocalDate createdBefore,
            @Param("updatedAfter") java.time.LocalDate updatedAfter,
            @Param("updatedBefore") java.time.LocalDate updatedBefore,
            @Param("favoritesOnly") boolean favoritesOnly,
            @Param("includeExpiredCreators") boolean includeExpiredCreators,
            @Param("currentUserId") Long currentUserId,
            Pageable pageable);

    /**
     * Find a deck by ID with eager loading of related entities to avoid N+1 queries
     *
     * @param id The deck ID
     * @return The deck with related entities loaded
     */
    @Query("SELECT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags " +
           "LEFT JOIN FETCH d.creator " +
           "WHERE d.id = :id")
    Optional<Deck> findByIdWithDetails(@Param("id") Long id);

    /**
     * Find a non-deleted deck by ID with eager loading of related entities
     *
     * @param id The deck ID
     * @return The deck with related entities loaded
     */
    @Query("SELECT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags " +
           "LEFT JOIN FETCH d.creator " +
           "WHERE d.id = :id AND d.deleted = false")
    Optional<Deck> findByIdWithDetailsNotDeleted(@Param("id") Long id);

    /**
     * Find a deleted deck by ID with eager loading of related entities
     *
     * @param id The deck ID
     * @return The deleted deck with related entities loaded
     */
    @Query("SELECT d FROM Deck d " +
           "LEFT JOIN FETCH d.deckTags " +
           "LEFT JOIN FETCH d.creator " +
           "WHERE d.id = :id AND d.deleted = true")
    Optional<Deck> findDeletedByIdWithDetails(@Param("id") Long id);

    /**
     * Find decks by title containing the given text (case-insensitive)
     *
     * @param title The title text to search for
     * @param pageable Pagination information
     * @return List of matching decks
     */
    @Query("SELECT d FROM Deck d WHERE LOWER(d.title) LIKE LOWER(CONCAT('%', :title, '%')) " +
           "AND d.deleted = false " +
           "AND (d.isPublic = true OR d.creator = ?#{principal?.user}) " +
           "ORDER BY " +
           "CASE WHEN LOWER(d.title) LIKE LOWER(CONCAT(:title, '%')) THEN 0 " +
           "     WHEN LOWER(d.title) LIKE LOWER(CONCAT('% ', :title, '%')) THEN 1 " +
           "     ELSE 2 END, " +
           "LENGTH(d.title)")
    List<Deck> findByTitleContainingIgnoreCase(@Param("title") String title, Pageable pageable);

    /**
     * Find related decks based on shared tags
     * This method is deprecated, use DeckRepositoryCustom.findRelatedDecksByTags instead
     *
     * @param deckId The deck ID to find related decks for
     * @param limit Maximum number of related decks to return
     * @return List of related decks
     */
    @Deprecated
    default List<Deck> findRelatedDecksByTags(@Param("deckId") Long deckId, Pageable pageable) {
        return findRelatedDecksByTagsUsingDeckTags(deckId, pageable);
    }



    /**
     * Find decks with due cards for a specific user
     * Includes decks the user owns and decks they collaborate on
     *
     * @param userId The user ID
     * @param today Today's date
     * @param pageable Pagination information
     * @return List of decks with due cards
     */
    @Query("SELECT DISTINCT d, COUNT(c) as dueCardCount FROM Deck d JOIN d.cards c " +
           "WHERE c.nextReviewDate <= :today " +
           "AND d.deleted = false " +
           "AND (d.creator.id = :userId " +
           "     OR EXISTS (SELECT dc FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :userId)) " +
           "GROUP BY d " +
           "ORDER BY dueCardCount DESC")
    List<Deck> findDecksWithDueCardsForUser(
            @Param("userId") Long userId,
            @Param("today") java.time.LocalDate today,
            Pageable pageable);

    /**
     * Find user's favorite tags based on their favorited decks and study history
     * This method is deprecated, use DeckRepositoryCustom.findUserFavoriteTags instead
     *
     * @param userId The user ID
     * @param pageable Pagination information
     * @return List of tag names
     */
    @Deprecated
    default List<String> findUserFavoriteTags(
            @Param("userId") Long userId,
            Pageable pageable) {
        return findUserFavoriteTagsUsingDeckTags(userId, pageable);
    }

    /**
     * Find popular decks by tags
     * This method is deprecated, use DeckRepositoryCustom.findPopularDecksByTags instead
     *
     * @param tagNames List of tag names
     * @param pageable Pagination information
     * @return List of popular decks with the specified tags
     */
    @Deprecated
    default List<Deck> findPopularDecksByTags(
            @Param("tagNames") List<String> tagNames,
            Pageable pageable) {
        return findPopularDecksByTagsUsingDeckTags(tagNames, pageable);
    }

    // Method removed to avoid infinite recursion
    // Use DeckRepositoryCustom.findNewDecksByTags directly

    /**
     * Find all decks that have been deleted for more than the specified time
     * Used by the scheduled cleanup task
     *
     * @param deletedBefore Only include decks deleted before this date/time
     * @return List of decks to be permanently deleted
     */
    List<Deck> findByDeletedTrueAndDeletedAtBefore(LocalDateTime deletedBefore);

    /**
     * Find decks by tag name using DeckTag (implementation provided by DeckRepositoryCustom)
     */
    Page<Deck> findByTagNameIgnoreCase(String tagName, Pageable pageable);

    /**
     * Find decks by tag name and creation date using DeckTag (implementation provided by DeckRepositoryCustom)
     */
    Page<Deck> findByTagNameIgnoreCaseAndCreatedAtAfter(String tagName, LocalDateTime createdAfter, Pageable pageable);

    /**
     * Find popular tags using DeckTag (implementation provided by DeckRepositoryCustom)
     */
    List<TagSummary> findPopularTagsFromDeckTags(Pageable pageable);

    /**
     * Find related decks by tags using DeckTag (implementation provided by DeckRepositoryCustom)
     */
    List<Deck> findRelatedDecksByTagsUsingDeckTags(Long deckId, Pageable pageable);

    /**
     * Find user's favorite tags using DeckTag (implementation provided by DeckRepositoryCustom)
     */
    List<String> findUserFavoriteTagsUsingDeckTags(Long userId, Pageable pageable);

    /**
     * Find popular decks by tags using DeckTag (implementation provided by DeckRepositoryCustom)
     */
    List<Deck> findPopularDecksByTagsUsingDeckTags(List<String> tagNames, Pageable pageable);

    /**
     * Count decks by creator and creation date after
     *
     * @param creator The creator
     * @param createdAt The creation date threshold
     * @return Number of decks created after the date
     */
    long countByCreatorAndCreatedAtAfter(User creator, LocalDateTime createdAt);

    /**
     * Find deck by creator and title (case insensitive)
     *
     * @param creator The creator
     * @param title The title
     * @return Optional deck
     */
    Optional<Deck> findByCreatorAndTitleIgnoreCase(User creator, String title);

    /**
     * Find incomplete deck by creator ID and title with empty cards
     * FIXED VERSION: Replaced SIZE() function with NOT EXISTS for better performance
     *
     * @param creatorId The creator ID
     * @param title The title
     * @return Optional deck
     */
    @Query("SELECT d FROM Deck d WHERE d.creator.id = :creatorId AND d.title = :title AND NOT EXISTS (SELECT c FROM Card c WHERE c.deck = d)")
    Optional<Deck> findByCreatorIdAndTitleAndCardsIsEmpty(@Param("creatorId") Long creatorId, @Param("title") String title);

    // Method removed to avoid parsing errors
    // Use DeckRepositoryCustom.findNewDecksByTags directly
}