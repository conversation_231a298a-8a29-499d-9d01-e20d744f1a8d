package com.studycards.integration;

import com.studycards.dto.DeckResponse;
import com.studycards.service.DeckDiscoveryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to verify that LazyInitializationException is fixed
 * in DeckDiscoveryService when accessing lazy-loaded User properties
 */
@SpringBootTest
@ActiveProfiles("test")
class DeckDiscoveryLazyInitializationFixTest {

    @Autowired
    private DeckDiscoveryService deckDiscoveryService;

    @Test
    @Transactional
    void testGetTrendingDecks_ShouldNotThrowLazyInitializationException() {
        // This test verifies that the fix for LazyInitializationException works
        // when accessing trending decks that have lazy-loaded creator relationships
        
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        try {
            // Try to get trending decks - this should not throw LazyInitializationException
            // The method internally calls subscriptionStatusService.hasActiveSubscription(deck.getCreator())
            // which would previously cause LazyInitializationException
            Page<DeckResponse> result = deckDiscoveryService.getTrendingDecks(pageable, createdAfter);
            
            // If we reach here, no LazyInitializationException was thrown
            assertNotNull(result);
            assertTrue(result.getContent().size() >= 0); // Can be empty, but should not be null
            
        } catch (Exception e) {
            // Verify that the exception is NOT a LazyInitializationException
            assertFalse(e.getClass().getSimpleName().contains("LazyInitializationException"),
                    "LazyInitializationException should not be thrown when accessing trending decks. Got: " + e.getClass().getSimpleName());
            
            // Also check the cause chain for LazyInitializationException
            Throwable cause = e.getCause();
            while (cause != null) {
                assertFalse(cause.getClass().getSimpleName().contains("LazyInitializationException"),
                        "LazyInitializationException should not be in the cause chain. Got: " + cause.getClass().getSimpleName());
                cause = cause.getCause();
            }
            
            // If it's a different exception (like ResourceNotFoundException), that's acceptable
            // The main goal is to ensure no LazyInitializationException occurs
        }
    }

    @Test
    @Transactional
    void testGetNewlyCreatedDecks_ShouldNotThrowLazyInitializationException() {
        // This test verifies that the fix for LazyInitializationException works
        // when accessing newly created decks that have lazy-loaded creator relationships
        
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        try {
            // Try to get newly created decks - this should not throw LazyInitializationException
            Page<DeckResponse> result = deckDiscoveryService.getNewlyCreatedDecks(pageable, createdAfter);
            
            // If we reach here, no LazyInitializationException was thrown
            assertNotNull(result);
            assertTrue(result.getContent().size() >= 0); // Can be empty, but should not be null
            
        } catch (Exception e) {
            // Verify that the exception is NOT a LazyInitializationException
            assertFalse(e.getClass().getSimpleName().contains("LazyInitializationException"),
                    "LazyInitializationException should not be thrown when accessing newly created decks. Got: " + e.getClass().getSimpleName());
            
            // Also check the cause chain for LazyInitializationException
            Throwable cause = e.getCause();
            while (cause != null) {
                assertFalse(cause.getClass().getSimpleName().contains("LazyInitializationException"),
                        "LazyInitializationException should not be in the cause chain. Got: " + cause.getClass().getSimpleName());
                cause = cause.getCause();
            }
        }
    }

    @Test
    @Transactional
    void testGetNewlyCreatedDecks_WithoutCreatedAfter_ShouldNotThrowLazyInitializationException() {
        // This test verifies the fix works for the overloaded method without createdAfter parameter
        
        Pageable pageable = PageRequest.of(0, 10);
        
        try {
            // Try to get newly created decks without date filter
            Page<DeckResponse> result = deckDiscoveryService.getNewlyCreatedDecks(pageable, null);
            
            // If we reach here, no LazyInitializationException was thrown
            assertNotNull(result);
            assertTrue(result.getContent().size() >= 0); // Can be empty, but should not be null
            
        } catch (Exception e) {
            // Verify that the exception is NOT a LazyInitializationException
            assertFalse(e.getClass().getSimpleName().contains("LazyInitializationException"),
                    "LazyInitializationException should not be thrown when accessing newly created decks. Got: " + e.getClass().getSimpleName());
            
            // Also check the cause chain for LazyInitializationException
            Throwable cause = e.getCause();
            while (cause != null) {
                assertFalse(cause.getClass().getSimpleName().contains("LazyInitializationException"),
                        "LazyInitializationException should not be in the cause chain. Got: " + cause.getClass().getSimpleName());
                cause = cause.getCause();
            }
        }
    }

    @Test
    @Transactional
    void testGetDecksByTag_ShouldNotThrowLazyInitializationException() {
        // This test verifies that the fix works for tag-based deck discovery
        
        String tagName = "test";
        Pageable pageable = PageRequest.of(0, 10);
        LocalDateTime createdAfter = LocalDateTime.now().minusDays(7);
        
        try {
            // Try to get decks by tag - this should not throw LazyInitializationException
            Page<DeckResponse> result = deckDiscoveryService.getDecksByTag(tagName, pageable, createdAfter);
            
            // If we reach here, no LazyInitializationException was thrown
            assertNotNull(result);
            assertTrue(result.getContent().size() >= 0); // Can be empty, but should not be null
            
        } catch (Exception e) {
            // Verify that the exception is NOT a LazyInitializationException
            assertFalse(e.getClass().getSimpleName().contains("LazyInitializationException"),
                    "LazyInitializationException should not be thrown when accessing decks by tag. Got: " + e.getClass().getSimpleName());
            
            // Also check the cause chain for LazyInitializationException
            Throwable cause = e.getCause();
            while (cause != null) {
                assertFalse(cause.getClass().getSimpleName().contains("LazyInitializationException"),
                        "LazyInitializationException should not be in the cause chain. Got: " + cause.getClass().getSimpleName());
                cause = cause.getCause();
            }
        }
    }
}
