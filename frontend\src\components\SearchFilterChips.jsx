// src/components/SearchFilterChips.jsx
import React from 'react';
import { XMarkIcon, TagIcon, CalendarIcon, StarIcon, UserIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

const SearchFilterChips = ({ filters, searchType, onRemoveFilter, onClearAll }) => {
  const getFilterChips = () => {
    const chips = [];
    
    if (searchType === 'decks') {
      // Public/Private filter
      if (filters.isPublic !== null) {
        chips.push({
          key: 'isPublic',
          label: filters.isPublic ? 'Public Decks' : 'Private Decks',
          icon: filters.isPublic ? UserIcon : null,
          color: 'blue'
        });
      }
      
      // Tags filter
      if (filters.tagNames && filters.tagNames.length > 0) {
        filters.tagNames.forEach((tag, index) => {
          chips.push({
            key: `tagNames-${index}`,
            label: `#${tag}`,
            icon: TagIcon,
            color: 'green',
            onRemove: () => {
              const newTags = filters.tagNames.filter((_, i) => i !== index);
              onRemoveFilter('tagNames', newTags);
            }
          });
        });
      }
      
      // Difficulty range
      if (filters.minDifficulty !== null || filters.maxDifficulty !== null) {
        const min = filters.minDifficulty || 1;
        const max = filters.maxDifficulty || 5;
        chips.push({
          key: 'difficulty',
          label: min === max ? `Difficulty: ${min}` : `Difficulty: ${min}-${max}`,
          color: 'yellow',
          onRemove: () => {
            onRemoveFilter('minDifficulty', null);
            onRemoveFilter('maxDifficulty', null);
          }
        });
      }
      
      // Card count range
      if (filters.minCardCount !== null || filters.maxCardCount !== null) {
        const min = filters.minCardCount || 0;
        const max = filters.maxCardCount || '∞';
        chips.push({
          key: 'cardCount',
          label: `Cards: ${min}${max !== '∞' ? `-${max}` : '+'}`,
          color: 'purple'
        });
      }
      
      // Favorites only
      if (filters.favoritesOnly) {
        chips.push({
          key: 'favoritesOnly',
          label: 'Favorites Only',
          icon: StarIcon,
          color: 'yellow'
        });
      }
      
      // Date filters
      if (filters.createdAfter || filters.createdBefore) {
        const after = filters.createdAfter ? new Date(filters.createdAfter).toLocaleDateString() : null;
        const before = filters.createdBefore ? new Date(filters.createdBefore).toLocaleDateString() : null;
        
        let label = 'Created: ';
        if (after && before) {
          label += `${after} - ${before}`;
        } else if (after) {
          label += `After ${after}`;
        } else if (before) {
          label += `Before ${before}`;
        }
        
        chips.push({
          key: 'createdDate',
          label,
          icon: CalendarIcon,
          color: 'gray',
          onRemove: () => {
            onRemoveFilter('createdAfter', null);
            onRemoveFilter('createdBefore', null);
          }
        });
      }
      
      // Collaborative filter
      if (filters.isCollaborative !== null) {
        chips.push({
          key: 'isCollaborative',
          label: filters.isCollaborative ? 'Collaborative' : 'Non-Collaborative',
          color: 'indigo'
        });
      }
      
    } else if (searchType === 'cards') {
      // Difficulty range for cards
      if (filters.minDifficulty !== null || filters.maxDifficulty !== null) {
        const min = filters.minDifficulty || 1;
        const max = filters.maxDifficulty || 5;
        chips.push({
          key: 'difficulty',
          label: min === max ? `Difficulty: ${min}` : `Difficulty: ${min}-${max}`,
          color: 'yellow',
          onRemove: () => {
            onRemoveFilter('minDifficulty', null);
            onRemoveFilter('maxDifficulty', null);
          }
        });
      }
      
      // Progress range
      if (filters.minProgress !== null || filters.maxProgress !== null) {
        const min = Math.round((filters.minProgress || 0) * 100);
        const max = Math.round((filters.maxProgress || 1) * 100);
        chips.push({
          key: 'progress',
          label: min === max ? `Progress: ${min}%` : `Progress: ${min}-${max}%`,
          color: 'green',
          onRemove: () => {
            onRemoveFilter('minProgress', null);
            onRemoveFilter('maxProgress', null);
          }
        });
      }
      
      // Due cards only
      if (filters.includeDueCards) {
        chips.push({
          key: 'includeDueCards',
          label: 'Due for Review',
          icon: CalendarIcon,
          color: 'red'
        });
      }
      
      // Tags for cards
      if (filters.tagNames && filters.tagNames.length > 0) {
        filters.tagNames.forEach((tag, index) => {
          chips.push({
            key: `tagNames-${index}`,
            label: `#${tag}`,
            icon: TagIcon,
            color: 'green',
            onRemove: () => {
              const newTags = filters.tagNames.filter((_, i) => i !== index);
              onRemoveFilter('tagNames', newTags);
            }
          });
        });
      }
    }
    
    return chips;
  };

  const chips = getFilterChips();
  
  if (chips.length === 0) {
    return null;
  }

  const getChipColors = (color) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800',
      green: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800',
      purple: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800',
      red: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800',
      indigo: 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-300 dark:border-indigo-800',
      gray: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800'
    };
    return colors[color] || colors.gray;
  };

  return (
    <div className="flex flex-wrap items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
        Active Filters:
      </span>
      
      <AnimatePresence>
        {chips.map((chip) => (
          <motion.div
            key={chip.key}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getChipColors(chip.color)}`}
          >
            {chip.icon && (
              <chip.icon className="h-3 w-3 mr-1.5" />
            )}
            <span>{chip.label}</span>
            <button
              onClick={chip.onRemove || (() => onRemoveFilter(chip.key, null))}
              className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
              title={`Remove ${chip.label} filter`}
            >
              <XMarkIcon className="h-3 w-3" />
            </button>
          </motion.div>
        ))}
      </AnimatePresence>
      
      {chips.length > 1 && (
        <button
          onClick={onClearAll}
          className="ml-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 underline"
        >
          Clear All
        </button>
      )}
    </div>
  );
};

export default SearchFilterChips;
