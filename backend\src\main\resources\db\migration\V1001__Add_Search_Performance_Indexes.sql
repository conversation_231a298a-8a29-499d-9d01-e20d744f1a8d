-- V1001__Add_Search_Performance_Indexes.sql
-- Additional indexes for search performance optimization

-- Composite index for deck search with subscription filtering
CREATE INDEX IF NOT EXISTS idx_deck_search_composite 
ON decks(is_public, deleted, created_at, updated_at);

-- Index for deck search with creator subscription status
CREATE INDEX IF NOT EXISTS idx_deck_creator_subscription 
ON decks(creator_id, is_public, deleted);

-- Composite index for deck tags search
CREATE INDEX IF NOT EXISTS idx_deck_tags_search 
ON deck_tags(tag_name, deck_id);

-- Index for deck favorites with user filtering
CREATE INDEX IF NOT EXISTS idx_deck_favorites_user 
ON user_favorite_decks(user_id, deck_id);

-- Composite index for card search with deck filtering
CREATE INDEX IF NOT EXISTS idx_card_search_composite 
ON cards(deck_id, deleted, difficulty_level, learning_progress, created_at);

-- Index for card tags search
CREATE INDEX IF NOT EXISTS idx_card_tags_search 
ON card_tags(tag_name, card_id);

-- Index for card review dates with difficulty
CREATE INDEX IF NOT EXISTS idx_card_review_difficulty 
ON cards(next_review_date, difficulty_level, deleted);

-- Composite index for user search history
CREATE INDEX IF NOT EXISTS idx_search_history_user_type 
ON search_history(user_id, search_type, search_timestamp);

-- Index for popular search queries
CREATE INDEX IF NOT EXISTS idx_search_history_query_timestamp 
ON search_history(query_text, search_timestamp);

-- Index for search result tracking
CREATE INDEX IF NOT EXISTS idx_search_history_results 
ON search_history(result_count, clicked_result, search_timestamp);

-- Composite index for deck collaboration filtering
CREATE INDEX IF NOT EXISTS idx_deck_collaborator_search 
ON deck_collaborators(deck_id, user_id, permission);

-- Index for user subscription status (for filtering)
CREATE INDEX IF NOT EXISTS idx_user_subscription_status 
ON users(subscription_status, subscription_end_date);

-- Full-text search indexes (if supported by database)
-- Note: These may need to be adjusted based on the specific database system

-- Deck title and description search
CREATE INDEX IF NOT EXISTS idx_deck_title_fulltext 
ON decks USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Card question and answer search  
CREATE INDEX IF NOT EXISTS idx_card_content_fulltext 
ON cards USING gin(to_tsvector('english', question || ' ' || answer || ' ' || COALESCE(notes, '') || ' ' || COALESCE(hint, '')));

-- Covering index for deck search results (includes commonly selected columns)
CREATE INDEX IF NOT EXISTS idx_deck_search_covering 
ON decks(is_public, deleted, created_at) 
INCLUDE (id, title, description, creator_id, card_count, average_difficulty);

-- Covering index for card search results
CREATE INDEX IF NOT EXISTS idx_card_search_covering 
ON cards(deck_id, deleted, difficulty_level) 
INCLUDE (id, question, answer, learning_progress, next_review_date);

-- Partial indexes for common search patterns

-- Index for public, non-deleted decks only
CREATE INDEX IF NOT EXISTS idx_deck_public_active 
ON decks(created_at, updated_at) 
WHERE is_public = true AND deleted = false;

-- Index for private decks by creator
CREATE INDEX IF NOT EXISTS idx_deck_private_by_creator 
ON decks(creator_id, created_at) 
WHERE is_public = false AND deleted = false;

-- Index for cards due for review
CREATE INDEX IF NOT EXISTS idx_card_due_review 
ON cards(deck_id, next_review_date, difficulty_level) 
WHERE deleted = false AND next_review_date <= CURRENT_DATE;

-- Index for high-difficulty cards
CREATE INDEX IF NOT EXISTS idx_card_high_difficulty 
ON cards(deck_id, created_at) 
WHERE difficulty_level >= 4 AND deleted = false;

-- Performance monitoring indexes

-- Index for tracking search performance by time periods
CREATE INDEX IF NOT EXISTS idx_search_history_performance 
ON search_history(search_timestamp, result_count, clicked_result);

-- Index for user activity analysis
CREATE INDEX IF NOT EXISTS idx_search_history_user_activity 
ON search_history(user_id, search_timestamp) 
WHERE search_timestamp >= CURRENT_DATE - INTERVAL '30 days';

-- Statistics and maintenance

-- Update table statistics for better query planning
ANALYZE decks;
ANALYZE cards;
ANALYZE deck_tags;
ANALYZE card_tags;
ANALYZE user_favorite_decks;
ANALYZE search_history;
ANALYZE deck_collaborators;

-- Add comments for documentation
COMMENT ON INDEX idx_deck_search_composite IS 'Composite index for general deck search performance';
COMMENT ON INDEX idx_deck_creator_subscription IS 'Index for filtering decks by creator subscription status';
COMMENT ON INDEX idx_deck_tags_search IS 'Index for tag-based deck search';
COMMENT ON INDEX idx_card_search_composite IS 'Composite index for general card search performance';
COMMENT ON INDEX idx_search_history_user_type IS 'Index for user search history and analytics';

-- Create materialized view for search analytics (if supported)
CREATE MATERIALIZED VIEW IF NOT EXISTS search_analytics_daily AS
SELECT 
    DATE(search_timestamp) as search_date,
    search_type,
    COUNT(*) as total_searches,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(result_count) as avg_results,
    COUNT(CASE WHEN clicked_result = true THEN 1 END)::float / COUNT(*) as click_through_rate,
    array_agg(DISTINCT query_text ORDER BY query_text) FILTER (WHERE query_text IS NOT NULL) as popular_queries
FROM search_history 
WHERE search_timestamp >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(search_timestamp), search_type
ORDER BY search_date DESC, search_type;

-- Index on the materialized view
CREATE INDEX IF NOT EXISTS idx_search_analytics_daily_date 
ON search_analytics_daily(search_date, search_type);

-- Refresh the materialized view
REFRESH MATERIALIZED VIEW search_analytics_daily;
