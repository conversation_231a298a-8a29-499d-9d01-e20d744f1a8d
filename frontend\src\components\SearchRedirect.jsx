// src/components/SearchRedirect.jsx
import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  MagnifyingGlassIcon, 
  ArrowRightIcon,
  SparklesIcon 
} from '@heroicons/react/24/outline';

/**
 * Component that redirects users from old search pages to the new unified search
 */
const SearchRedirect = ({ 
  fromPage = 'discover', 
  message = 'Redirecting to improved search experience...',
  delay = 2000 
}) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const timer = setTimeout(() => {
      // Preserve any existing search parameters
      const query = searchParams.get('q') || searchParams.get('query');
      const type = searchParams.get('type') || 'decks';
      
      const newSearchParams = new URLSearchParams();
      if (query) newSearchParams.set('q', query);
      if (type) newSearchParams.set('type', type);
      
      const newUrl = `/app/search${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`;
      navigate(newUrl, { replace: true });
    }, delay);

    return () => clearTimeout(timer);
  }, [navigate, searchParams, delay]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-md mx-4"
      >
        {/* Icon Animation */}
        <motion.div
          animate={{ 
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="w-16 h-16 mx-auto mb-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center"
        >
          <MagnifyingGlassIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
        </motion.div>

        {/* Title */}
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
          Search Experience Upgraded!
        </h2>

        {/* Message */}
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {message}
        </p>

        {/* Features List */}
        <div className="text-left space-y-2 mb-6">
          <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
            <SparklesIcon className="h-4 w-4 text-primary-500 mr-2" />
            <span>Enhanced search with better filters</span>
          </div>
          <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
            <SparklesIcon className="h-4 w-4 text-primary-500 mr-2" />
            <span>Search history and saved presets</span>
          </div>
          <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
            <SparklesIcon className="h-4 w-4 text-primary-500 mr-2" />
            <span>Unified deck and card search</span>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center space-x-2 text-primary-600 dark:text-primary-400">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="w-2 h-2 bg-current rounded-full"
          />
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
            className="w-2 h-2 bg-current rounded-full"
          />
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
            className="w-2 h-2 bg-current rounded-full"
          />
        </div>

        {/* Manual Navigation */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => navigate('/app/search')}
            className="inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
          >
            <span>Go to search now</span>
            <ArrowRightIcon className="h-4 w-4 ml-1" />
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default SearchRedirect;
