2025-07-19 00:23:02.995 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 00:23:03.003 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 00:23:03.004 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 00:23:03.004 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 00:23:03.004 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 12:10:28.153 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 12:10:28.162 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 12:10:28.162 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 12:10:28.162 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 12:10:28.162 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 17:29:01.862 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 17:29:01.869 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 17:29:01.870 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 17:29:01.870 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 17:29:01.870 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 17:41:47.919 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 17:41:47.924 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 17:41:47.924 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 17:41:47.924 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 17:41:47.924 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 18:02:15.990 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 18:02:15.995 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 18:02:15.996 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 18:02:15.996 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 18:02:15.996 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 18:27:09.060 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 18:27:09.065 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 18:27:09.065 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 18:27:09.065 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 18:27:09.065 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 19:16:01.335 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 19:16:01.339 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 19:16:01.339 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 19:16:01.339 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 19:16:01.339 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 20:01:00.558 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 20:01:00.563 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 20:01:00.563 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 20:01:00.563 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 20:01:00.563 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 21:09:58.674 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 21:09:58.678 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 21:09:58.678 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 21:09:58.678 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 21:09:58.678 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 21:37:10.139 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 21:37:10.144 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 21:37:10.145 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 21:37:10.146 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 21:37:10.146 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 21:54:16.759 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 21:54:16.767 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 21:54:16.768 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 21:54:16.768 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 21:54:16.768 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 22:23:29.463 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 22:23:29.468 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 22:23:29.469 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 22:23:29.469 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 22:23:29.469 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 22:24:03.899 [http-nio-8082-exec-2] INFO  c.s.security.CustomOAuth2UserService - 
                [] [] [] [] Google OAuth2 user attributes validated successfully for email: <EMAIL>
2025-07-19 22:24:03.903 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success handler called
2025-07-19 22:24:03.904 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Request URI: /login/oauth2/code/google
2025-07-19 22:24:03.904 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Query string: state=xZlHxo-ZIE1UeZCUk-o2Q25qvadmYypmgWPX8zGuyTQ%3D&code=4%2F0AVMBsJhohlyemwYEY3RmtLoNAHjMHf0XBbVaDuD-KM6NGmmDzKk1xg16fJfScf5EBXdBxg&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=1&prompt=none
2025-07-19 22:24:03.904 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success from Code
2025-07-19 22:24:03.974 [http-nio-8082-exec-2] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT token generated successfully for email: <EMAIL> (ID: ba7eaa09-b8f1-41c6-b226-2f5b984a2bc1)
2025-07-19 22:24:04.111 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 cookie configuration - requireHttps: false, isSecure: false, useSecureCookies: false
2025-07-19 22:24:04.112 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 access token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-19 22:24:04.112 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 refresh token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-19 23:01:26.970 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 23:01:26.974 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 23:01:26.975 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 23:01:26.975 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 23:01:26.975 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-19 23:25:50.315 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-19 23:25:50.320 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-19 23:25:50.320 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-19 23:25:50.321 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-19 23:25:50.321 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
