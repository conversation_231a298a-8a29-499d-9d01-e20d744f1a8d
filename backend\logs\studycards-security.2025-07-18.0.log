2025-07-18 15:23:02.948 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 15:23:02.953 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 15:23:02.953 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 15:23:02.953 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 15:23:02.953 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 15:40:41.830 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 15:40:41.835 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 15:40:41.835 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 15:40:41.835 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 15:40:41.835 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 16:11:21.659 [http-nio-8082-exec-3] INFO  c.s.security.CustomOAuth2UserService - 
                [] [] [] [] Google OAuth2 user attributes validated successfully for email: <EMAIL>
2025-07-18 16:11:21.665 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success handler called
2025-07-18 16:11:21.665 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Request URI: /login/oauth2/code/google
2025-07-18 16:11:21.665 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Query string: state=QEhWR_vHpzz4dKY7pbyzp1Qh01Knb2_eeDoxulQYL40%3D&code=4%2F0AVMBsJibJXpJwlr7zacktrGcqCIpfrnEbR3udFnKYOv4MBblA7V51MyPQ-u1sTHizRq7VA&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid&authuser=1&prompt=none
2025-07-18 16:11:21.665 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success from Code
2025-07-18 16:11:21.775 [http-nio-8082-exec-3] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT token generated successfully for email: <EMAIL> (ID: 8a19da72-fa74-4a18-a065-6d1e469c7cb0)
2025-07-18 16:11:21.904 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 cookie configuration - requireHttps: false, isSecure: false, useSecureCookies: false
2025-07-18 16:11:21.904 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 access token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-18 16:11:21.904 [http-nio-8082-exec-3] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 refresh token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-18 17:22:18.606 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 17:22:18.612 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 17:22:18.612 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 17:22:18.612 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 17:22:18.612 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 21:24:35.132 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 21:24:35.137 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 21:24:35.137 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 21:24:35.137 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 21:24:35.137 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 21:44:16.337 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 21:44:16.343 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 21:44:16.343 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 21:44:16.343 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 21:44:16.343 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 22:00:26.695 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 22:00:26.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 22:00:26.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 22:00:26.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 22:00:26.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 22:38:46.724 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 22:38:46.731 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 22:38:46.731 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 22:38:46.731 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 22:38:46.731 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 22:48:54.072 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 22:48:54.078 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 22:48:54.079 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 22:48:54.079 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 22:48:54.079 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 23:21:59.019 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 23:21:59.032 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 23:21:59.032 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 23:21:59.032 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 23:21:59.032 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-18 23:22:41.927 [http-nio-8082-exec-1] ERROR c.s.security.AuthEntryPointJwt - 
                [] [] [] [] Unauthorized error: Full authentication is required to access this resource
2025-07-18 23:22:54.556 [http-nio-8082-exec-6] INFO  c.s.security.CustomOAuth2UserService - 
                [] [] [] [] Google OAuth2 user attributes validated successfully for email: <EMAIL>
2025-07-18 23:22:54.558 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success handler called
2025-07-18 23:22:54.558 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Request URI: /login/oauth2/code/google
2025-07-18 23:22:54.558 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Query string: state=ekbDJZxQcUoZREd37gfN24iReMdv2Ldf_JhXI9bYVlw%3D&code=4%2F0AVMBsJgL90QmgWdTMnN5vgmzO6NsLTTWXmgMRXlZpf4fDxr4FEdQqwHyMGUqc5P2LIc5kQ&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid&authuser=1&prompt=none
2025-07-18 23:22:54.558 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success from Code
2025-07-18 23:22:54.617 [http-nio-8082-exec-6] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT token generated successfully for email: <EMAIL> (ID: 5590f378-5ecc-4636-8532-3fd8697655a5)
2025-07-18 23:22:54.721 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 cookie configuration - requireHttps: false, isSecure: false, useSecureCookies: false
2025-07-18 23:22:54.722 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 access token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-18 23:22:54.723 [http-nio-8082-exec-6] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 refresh token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-18 23:39:39.361 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-18 23:39:39.369 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-18 23:39:39.369 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-18 23:39:39.369 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-18 23:39:39.369 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
