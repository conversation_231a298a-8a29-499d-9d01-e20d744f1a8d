// src/__tests__/useSearchOperations.test.jsx
import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { BrowserRouter } from 'react-router-dom';
import { SearchProvider } from '../contexts/SearchContext';
import { useSearchOperations } from '../hooks/useSearchOperations';
import { searchAPI } from '../api/search';

// Mock the search API
jest.mock('../api/search', () => ({
  searchAPI: {
    unifiedSearchDecks: jest.fn(),
    unifiedSearchCards: jest.fn(),
    quickSearch: jest.fn(),
    getEnhancedSearchSuggestions: jest.fn()
  }
}));

// Mock toast
jest.mock('react-hot-toast', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn()
  }
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { 
        retry: false,
        cacheTime: 0
      },
      mutations: { retry: false }
    }
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <SearchProvider>
          {children}
        </SearchProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('useSearchOperations', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('initializes with correct default state', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    expect(result.current.state.query).toBe('');
    expect(result.current.state.searchType).toBe('decks');
    expect(result.current.isLoading).toBe(false);
    expect(result.current.hasResults).toBe(false);
    expect(result.current.totalResults).toBe(0);
  });

  test('executes deck search successfully', async () => {
    const mockResponse = {
      content: [
        { id: 1, title: 'Test Deck', description: 'Test Description' }
      ],
      totalElements: 1,
      totalPages: 1,
      number: 0,
      size: 10
    };

    searchAPI.unifiedSearchDecks.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    // Execute search
    result.current.executeSearch('test query', 'decks');

    await waitFor(() => {
      expect(result.current.state.query).toBe('test query');
      expect(result.current.state.searchType).toBe('decks');
    });

    await waitFor(() => {
      expect(searchAPI.unifiedSearchDecks).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test query',
          searchContext: 'unified_search'
        })
      );
    });
  });

  test('executes card search successfully', async () => {
    const mockResponse = {
      content: [
        { id: 1, question: 'Test Question', answer: 'Test Answer' }
      ],
      totalElements: 1,
      totalPages: 1,
      number: 0,
      size: 20
    };

    searchAPI.unifiedSearchCards.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    // Execute search
    result.current.executeSearch('test query', 'cards');

    await waitFor(() => {
      expect(result.current.state.query).toBe('test query');
      expect(result.current.state.searchType).toBe('cards');
    });

    await waitFor(() => {
      expect(searchAPI.unifiedSearchCards).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test query'
        })
      );
    });
  });

  test('handles search errors gracefully', async () => {
    const mockError = new Error('Search failed');
    searchAPI.unifiedSearchDecks.mockRejectedValue(mockError);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    result.current.executeSearch('test query', 'decks');

    await waitFor(() => {
      expect(result.current.state.error).toContain('Search failed');
    });
  });

  test('executes quick search', async () => {
    const mockResponse = {
      query: 'quick',
      deckResults: [{ id: 1, title: 'Quick Deck' }],
      cardResults: [{ id: 1, question: 'Quick Card' }],
      totalDeckResults: 1,
      totalCardResults: 1
    };

    searchAPI.quickSearch.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    const quickSearchResult = await result.current.executeQuickSearch('quick');

    expect(searchAPI.quickSearch).toHaveBeenCalledWith('quick', 10);
    expect(quickSearchResult.data).toEqual(mockResponse);
  });

  test('detects active filters correctly', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    // Initially no active filters
    expect(result.current.hasActiveFilters('decks')).toBe(false);

    // Set some filters
    result.current.actions.setFilters({
      isPublic: true,
      tagNames: ['test'],
      favoritesOnly: false
    }, 'decks');

    expect(result.current.hasActiveFilters('decks')).toBe(true);
  });

  test('clears filters correctly', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    // Set some filters
    result.current.actions.setFilters({
      isPublic: true,
      tagNames: ['test'],
      minDifficulty: 2
    }, 'decks');

    expect(result.current.hasActiveFilters('decks')).toBe(true);

    // Clear filters
    result.current.clearFilters('decks');

    expect(result.current.hasActiveFilters('decks')).toBe(false);
  });

  test('changes page correctly', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    result.current.changePage(2, 'decks');

    expect(result.current.state.pagination.decks.page).toBe(2);
  });

  test('changes page size correctly', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    result.current.changePageSize(25, 'decks');

    expect(result.current.state.pagination.decks.size).toBe(25);
    expect(result.current.state.pagination.decks.page).toBe(0); // Should reset to first page
  });

  test('adds search to history on successful search', async () => {
    const mockResponse = {
      content: [{ id: 1, title: 'Test Deck' }],
      totalElements: 1,
      totalPages: 1,
      number: 0,
      size: 10
    };

    searchAPI.unifiedSearchDecks.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    result.current.executeSearch('test query', 'decks');

    await waitFor(() => {
      expect(result.current.state.searchHistory).toHaveLength(1);
      expect(result.current.state.searchHistory[0].query).toBe('test query');
      expect(result.current.state.searchHistory[0].resultCount).toBe(1);
    });
  });

  test('updates pagination on search results', async () => {
    const mockResponse = {
      content: [{ id: 1, title: 'Test Deck' }],
      totalElements: 25,
      totalPages: 3,
      number: 1,
      size: 10
    };

    searchAPI.unifiedSearchDecks.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    result.current.executeSearch('test query', 'decks');

    await waitFor(() => {
      expect(result.current.state.pagination.decks.totalElements).toBe(25);
      expect(result.current.state.pagination.decks.totalPages).toBe(3);
      expect(result.current.state.pagination.decks.page).toBe(1);
    });
  });

  test('handles search with filters', async () => {
    const mockResponse = {
      content: [],
      totalElements: 0,
      totalPages: 0,
      number: 0,
      size: 10
    };

    searchAPI.unifiedSearchDecks.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    const filters = {
      isPublic: true,
      tagNames: ['javascript', 'react'],
      minDifficulty: 2,
      maxDifficulty: 4
    };

    result.current.executeSearch('test query', 'decks', filters);

    await waitFor(() => {
      expect(searchAPI.unifiedSearchDecks).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'test query',
          isPublic: true,
          tagNames: ['javascript', 'react'],
          minDifficulty: 2,
          maxDifficulty: 4
        })
      );
    });
  });

  test('calculates total results correctly', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    // Set mock results
    result.current.actions.setResults({
      content: [],
      totalElements: 15
    }, 'decks');

    result.current.actions.setResults({
      content: [],
      totalElements: 8
    }, 'cards');

    expect(result.current.totalResults).toBe(23);
  });

  test('determines hasResults correctly', () => {
    const { result } = renderHook(() => useSearchOperations(), {
      wrapper: createWrapper()
    });

    expect(result.current.hasResults).toBe(false);

    // Set some deck results
    result.current.actions.setResults({
      content: [{ id: 1, title: 'Test' }],
      totalElements: 1
    }, 'decks');

    expect(result.current.hasResults).toBe(true);
  });
});
