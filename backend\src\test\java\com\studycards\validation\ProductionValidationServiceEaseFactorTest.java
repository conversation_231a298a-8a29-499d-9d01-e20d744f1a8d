package com.studycards.validation;

import com.studycards.model.Card;
import com.studycards.model.Deck;
import com.studycards.model.User;
import com.studycards.model.enums.QuestionType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ProductionValidationServiceEaseFactorTest {

    @InjectMocks
    private ProductionValidationService validationService;

    private Card testCard;
    private Deck testDeck;
    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .build();

        testDeck = Deck.builder()
                .id(1L)
                .title("Test Deck")
                .user(testUser)
                .build();

        testCard = Card.builder()
                .id(1L)
                .question("Test Question")
                .answer("Test Answer")
                .difficultyLevel(1)
                .questionType(QuestionType.SHORT_ANSWER)
                .deck(testDeck)
                .easeFactor(BigDecimal.valueOf(2.5))
                .build();
    }

    @Test
    void testValidateCard_ValidEaseFactor_RemainsUnchanged() {
        // Arrange
        testCard.setEaseFactor(BigDecimal.valueOf(2.0));

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert
        assertEquals(BigDecimal.valueOf(2.0), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_EaseFactorBelowMinimum_ClampsToMinimum() {
        // Arrange
        testCard.setEaseFactor(BigDecimal.valueOf(1.0)); // Below minimum of 1.3

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert
        assertEquals(BigDecimal.valueOf(1.3), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_EaseFactorAboveMaximum_ClampsToMaximum() {
        // Arrange
        testCard.setEaseFactor(BigDecimal.valueOf(6.0)); // Above maximum of 5.0

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert
        assertEquals(BigDecimal.valueOf(5.0), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_NullEaseFactor_SetsDefault() {
        // Arrange
        testCard.setEaseFactor(null);

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert
        assertEquals(BigDecimal.valueOf(2.5), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_ZeroEaseFactor_SetsDefault() {
        // Arrange
        testCard.setEaseFactor(BigDecimal.ZERO);

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert
        assertEquals(BigDecimal.valueOf(2.5), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_PrecisionEaseFactor_HandledCorrectly() {
        // Arrange - Test with a precise value that might cause floating point issues
        testCard.setEaseFactor(BigDecimal.valueOf(1.30000001)); // Slightly above minimum

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert
        assertTrue(validatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(1.3)) >= 0);
        assertTrue(validatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(5.0)) <= 0);
    }
}
