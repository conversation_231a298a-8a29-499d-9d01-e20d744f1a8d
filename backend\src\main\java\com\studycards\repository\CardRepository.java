package com.studycards.repository;

import com.studycards.model.Card;
import com.studycards.model.Deck;
import com.studycards.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CardRepository extends JpaRepository<Card, Long> {
    List<Card> findByDeck(Deck deck);

    List<Card> findByDeckId(Long deckId);

    Page<Card> findByDeckId(Long deckId, Pageable pageable);

    /**
     * Find cards by deck entity, filtering out deleted decks and expired/cancelled users
     * This is the MISSING method that was referenced in the analysis
     *
     * @param deck The deck entity
     * @return List of cards from non-deleted decks with valid subscription
     */
    @Query("SELECT c FROM Card c WHERE c.deck = :deck AND c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    List<Card> findByDeckAndDeletedFalse(@Param("deck") Deck deck);

    /**
     * Find cards by deck ID, filtering out deleted decks and expired/cancelled users
     * Secure version of findByDeckId
     *
     * @param deckId The deck ID
     * @return List of cards from non-deleted decks with valid subscription
     */
    @Query("SELECT c FROM Card c WHERE c.deck.id = :deckId AND c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    List<Card> findByDeckIdAndDeletedFalse(@Param("deckId") Long deckId);

    /**
     * Find cards by deck ID with pagination, filtering out deleted decks and expired/cancelled users
     * Secure version of findByDeckId with pagination
     *
     * @param deckId The deck ID
     * @param pageable Pagination information
     * @return Page of cards from non-deleted decks with valid subscription
     */
    @Query("SELECT c FROM Card c WHERE c.deck.id = :deckId AND c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    Page<Card> findByDeckIdAndDeletedFalse(@Param("deckId") Long deckId, Pageable pageable);

    /**
     * Find cards by multiple deck IDs
     *
     * @param deckIds List of deck IDs
     * @return List of cards in the specified decks
     */
    List<Card> findByDeckIdIn(List<Long> deckIds);

    int countByDeckId(Long deckId);

    /**
     * Count the number of cards in non-deleted decks
     * Excludes cards from decks owned by users with EXPIRED or CANCELLED subscription status
     *
     * @return The number of cards
     */
    @Query("SELECT COUNT(c) FROM Card c WHERE c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    int countByDeckDeletedFalse();

    /**
     * Find cards by deck ID with deck info, filtering out deleted decks and expired users
     * Updated to include proper subscription filtering and parameter binding
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE)")
    List<Card> findByDeckIdWithDeckInfo(@Param("deckId") Long deckId);

    /**
     * Find cards for review by deck ID, filtering out deleted decks and expired users
     * Updated to include subscription filtering and JOIN FETCH to prevent N+1 queries
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY c.difficultyLevel DESC, c.updatedAt ASC")
    List<Card> findCardsForReview(@Param("deckId") Long deckId, Pageable pageable);

    /**
     * Find cards by deck ID and next review date less than or equal to specified date
     * FIXED: Added proper subscription filtering that was missing and JOIN FETCH to prevent LazyInitializationException
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND c.nextReviewDate <= :date " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE)")
    List<Card> findByDeckIdAndNextReviewDateLessThanEqual(@Param("deckId") Long deckId, @Param("date") LocalDate date);

    /**
     * Find cards with next review date between two dates
     *
     * @param startDate Start date (inclusive)
     * @param endDate End date (inclusive)
     * @return List of cards due for review between the specified dates
     */
    /**
     * Find cards with next review date between two dates, filtering expired users
     * Updated to include subscription filtering and fix data type consistency
     * FIXED: Changed parameters from LocalDateTime to LocalDate to match nextReviewDate field type
     */
    @Query("SELECT c FROM Card c WHERE c.nextReviewDate BETWEEN :startDate AND :endDate " +
           "AND c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (c.deck.creator.subscriptionEndDate IS NULL OR c.deck.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY c.nextReviewDate ASC")
    List<Card> findByNextReviewDateBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find due cards by deck ID ordered by next review date, filtering expired users
     * Updated to include proper subscription filtering with trial expiration check and JOIN FETCH to prevent LazyInitializationException
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "AND c.nextReviewDate <= :date " +
           "ORDER BY c.nextReviewDate ASC")
    List<Card> findDueCardsByDeckIdOrderByNextReviewDate(@Param("deckId") Long deckId, @Param("date") LocalDate date);

    /**
     * Find due cards by deck ID ordered by next review date with pagination
     * PERFORMANCE FIX: Added pagination to prevent excessive memory usage
     */
    @Query("SELECT c FROM Card c WHERE c.deck.id = :deckId " +
           "AND c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (c.deck.creator.subscriptionEndDate IS NULL OR c.deck.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "AND c.nextReviewDate <= :date " +
           "ORDER BY c.nextReviewDate ASC")
    Page<Card> findDueCardsByDeckIdOrderByNextReviewDatePaged(@Param("deckId") Long deckId, @Param("date") LocalDate date, Pageable pageable);

    /**
     * Find due cards by deck ID ordered by difficulty and date, filtering expired users
     * Updated to include proper subscription filtering with trial expiration check and JOIN FETCH to prevent LazyInitializationException
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "AND c.nextReviewDate <= :date " +
           "ORDER BY c.difficultyLevel DESC, c.nextReviewDate ASC")
    List<Card> findDueCardsByDeckIdOrderByDifficultyAndDate(@Param("deckId") Long deckId, @Param("date") LocalDate date);

    /**
     * Find due cards by deck ID ordered by difficulty and date with pagination
     * PERFORMANCE FIX: Added pagination to prevent excessive memory usage
     */
    @Query("SELECT c FROM Card c WHERE c.deck.id = :deckId " +
           "AND c.deck.deleted = false " +
           "AND c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (c.deck.creator.subscriptionEndDate IS NULL OR c.deck.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "AND c.nextReviewDate <= :date " +
           "ORDER BY c.difficultyLevel DESC, c.nextReviewDate ASC")
    Page<Card> findDueCardsByDeckIdOrderByDifficultyAndDatePaged(@Param("deckId") Long deckId, @Param("date") LocalDate date, Pageable pageable);

    /**
     * Find cards by deck user (creator)
     * Updated to include proper subscription filtering - users can access their own content
     * but we still need to validate subscription for business logic compliance
     *
     * @param user The user who created the deck
     * @return List of cards in decks created by the user
     */
    @Query("SELECT c FROM Card c WHERE c.deck.creator = :user " +
           "AND c.deck.deleted = false " +
           "AND (c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "     OR c.deck.creator.subscriptionEndDate >= CURRENT_DATE)")
    List<Card> findByDeckUser(@Param("user") User user);

    /**
     * Find cards by deck user (creator) with pagination
     * PERFORMANCE FIX: Added pagination to prevent excessive memory usage
     */
    @Query("SELECT c FROM Card c WHERE c.deck.creator = :user " +
           "AND c.deck.deleted = false " +
           "AND (c.deck.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "     OR c.deck.creator.subscriptionEndDate >= CURRENT_DATE)")
    Page<Card> findByDeckUserPaged(@Param("user") User user, Pageable pageable);

    @Query("SELECT c FROM Card c JOIN FETCH c.deck d JOIN FETCH d.deckTags dt WHERE d.id = :deckId " +
            "AND c.nextReviewDate <= :date " +
            "AND dt.tagName IN :tagNames " +
            "AND d.deleted = false " +
            "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
            "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
            "ORDER BY c.nextReviewDate ASC")
    List<Card> findDueCardsByDeckIdAndTagsOrderByNextReviewDate(
            @Param("deckId") Long deckId,
            @Param("date") LocalDate date,
            @Param("tagNames") List<String> tagNames);

    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
            "WHERE c.nextReviewDate <= :date " +
            "AND d.deleted = false " +
            "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
            "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
            "AND (d.creator.id = :userId OR EXISTS (SELECT dc FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = :userId)) " +
            "ORDER BY c.nextReviewDate ASC")
    List<Card> findAllDueCardsForUser(@Param("date") LocalDate date, @Param("userId") Long userId);

    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
            "WHERE c.nextReviewDate <= :date " +
            "AND d.creator.id = :userId " +
            "AND d.deleted = false " +
            "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
            "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
            "ORDER BY c.nextReviewDate ASC")
    List<Card> findDueCardsForUserOwnedDecks(@Param("date") LocalDate date, @Param("userId") Long userId);

    @Query("SELECT COUNT(c) FROM Card c WHERE c.deck.id = :deckId AND c.nextReviewDate <= :date")
    int countDueCardsByDeckId(@Param("deckId") Long deckId, @Param("date") LocalDate date);

    @Query("SELECT MIN(c.nextReviewDate) FROM Card c WHERE c.deck.id = :deckId AND c.nextReviewDate > :date")
    LocalDate findNextDueDateForDeck(@Param("deckId") Long deckId, @Param("date") LocalDate date);

    /**
     * Find cards due for review by deck ID - MISSING METHOD REFERENCED IN ANALYSIS
     * Includes proper subscription filtering and deleted deck exclusion with JOIN FETCH to prevent LazyInitializationException
     *
     * @param deckId The deck ID
     * @param pageable Pagination information
     * @return List of cards due for review
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND d.deleted = false " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "AND c.nextReviewDate <= CURRENT_DATE " +
           "ORDER BY c.nextReviewDate ASC")
    List<Card> findDueForReview(@Param("deckId") Long deckId, Pageable pageable);

    /**
     * Find cards by question containing the given text (case-insensitive)
     *
     * @param question The question text to search for
     * @param pageable Pagination information
     * @return List of matching cards
     */
    @Query("SELECT c FROM Card c JOIN c.deck d WHERE LOWER(c.question) LIKE LOWER(CONCAT('%', :question, '%')) " +
           "AND d.deleted = false " +
           "AND (d.isPublic = true OR d.creator.id = ?#{principal?.user?.id} OR " +
           "EXISTS (SELECT dc FROM DeckCollaborator dc WHERE dc.deck = d AND dc.user.id = ?#{principal?.user?.id})) " +
           "ORDER BY " +
           "CASE WHEN LOWER(c.question) LIKE LOWER(CONCAT(:question, '%')) THEN 0 " +
           "     WHEN LOWER(c.question) LIKE LOWER(CONCAT('% ', :question, '%')) THEN 1 " +
           "     ELSE 2 END, " +
           "LENGTH(c.question)")
    List<Card> findByQuestionContainingIgnoreCase(@Param("question") String question, Pageable pageable);

    /**
     * Advanced search for cards with multiple criteria
     *
     * @param query Search text to find in question, answer, notes, or hint
     * @param deckIds List of deck IDs to search in (null for all accessible decks)
     * @param minDifficulty Minimum difficulty level (1-5)
     * @param maxDifficulty Maximum difficulty level (1-5)
     * @param minProgress Minimum learning progress (0.0-1.0)
     * @param maxProgress Maximum learning progress (0.0-1.0)
     * @param createdAfter Created after date
     * @param createdBefore Created before date
     * @param updatedAfter Updated after date
     * @param updatedBefore Updated before date
     * @param reviewDateAfter Next review date after
     * @param reviewDateBefore Next review date before
     * @param currentUser The current user
     * @param includeCollaborative Include cards from collaborative decks
     * @param includePublicDecks Include cards from public decks
     * @param includePrivateDecks Include cards from private decks
     * @param includeDueCards Include only cards that are due for review
     * @param pageable Pagination and sorting information
     * @return Page of cards matching the criteria
     */
    @Query(value = "SELECT DISTINCT c FROM Card c " +
           "JOIN FETCH c.deck d " +
           "LEFT JOIN DeckCollaborator dc ON dc.deck = d AND dc.user.id = :userId " +
           "WHERE d.deleted = false " +
           "AND (:query IS NULL OR " +
           "    LOWER(c.question) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.answer) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.notes) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.hint) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "AND (:deckIds IS NULL OR d.id IN :deckIds) " +
           "AND (:minDifficulty IS NULL OR c.difficultyLevel >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR c.difficultyLevel <= :maxDifficulty) " +
           "AND (:minProgress IS NULL OR c.learningProgress >= :minProgress) " +
           "AND (:maxProgress IS NULL OR c.learningProgress <= :maxProgress) " +
           "AND (:createdAfter IS NULL OR CAST(c.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(c.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(c.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(c.updatedAt AS date) <= :updatedBefore) " +
           "AND (:reviewDateAfter IS NULL OR c.nextReviewDate >= :reviewDateAfter) " +
           "AND (:reviewDateBefore IS NULL OR c.nextReviewDate <= :reviewDateBefore) " +
           "AND (:includeDueCards = false OR c.nextReviewDate <= CURRENT_DATE) " +
           "AND (d.creator.id = :userId " +
           "    OR (:includeCollaborative = true AND dc.id IS NOT NULL) " +
           "    OR (:includePublicDecks = true AND d.isPublic = true) " +
           "    OR (:includePrivateDecks = true AND d.isPublic = false AND d.creator.id = :userId))",
           countQuery = "SELECT COUNT(DISTINCT c) FROM Card c " +
           "JOIN c.deck d " +
           "LEFT JOIN DeckCollaborator dc ON dc.deck = d AND dc.user.id = :userId " +
           "WHERE d.deleted = false " +
           "AND (:query IS NULL OR " +
           "    LOWER(c.question) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.answer) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.notes) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.hint) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "AND (:deckIds IS NULL OR d.id IN :deckIds) " +
           "AND (:minDifficulty IS NULL OR c.difficultyLevel >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR c.difficultyLevel <= :maxDifficulty) " +
           "AND (:minProgress IS NULL OR c.learningProgress >= :minProgress) " +
           "AND (:maxProgress IS NULL OR c.learningProgress <= :maxProgress) " +
           "AND (:createdAfter IS NULL OR CAST(c.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(c.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(c.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(c.updatedAt AS date) <= :updatedBefore) " +
           "AND (:reviewDateAfter IS NULL OR c.nextReviewDate >= :reviewDateAfter) " +
           "AND (:reviewDateBefore IS NULL OR c.nextReviewDate <= :reviewDateBefore) " +
           "AND (:includeDueCards = false OR c.nextReviewDate <= CURRENT_DATE) " +
           "AND (d.creator.id = :userId " +
           "    OR (:includeCollaborative = true AND dc.id IS NOT NULL) " +
           "    OR (:includePublicDecks = true AND d.isPublic = true) " +
           "    OR (:includePrivateDecks = true AND d.isPublic = false AND d.creator.id = :userId))")
    Page<Card> advancedSearch(
            @Param("query") String query,
            @Param("deckIds") List<Long> deckIds,
            @Param("minDifficulty") Integer minDifficulty,
            @Param("maxDifficulty") Integer maxDifficulty,
            @Param("minProgress") Float minProgress,
            @Param("maxProgress") Float maxProgress,
            @Param("createdAfter") LocalDate createdAfter,
            @Param("createdBefore") LocalDate createdBefore,
            @Param("updatedAfter") LocalDate updatedAfter,
            @Param("updatedBefore") LocalDate updatedBefore,
            @Param("reviewDateAfter") LocalDate reviewDateAfter,
            @Param("reviewDateBefore") LocalDate reviewDateBefore,
            @Param("userId") Long userId,
            @Param("includeCollaborative") Boolean includeCollaborative,
            @Param("includePublicDecks") Boolean includePublicDecks,
            @Param("includePrivateDecks") Boolean includePrivateDecks,
            @Param("includeDueCards") Boolean includeDueCards,
            Pageable pageable);

    /**
     * Advanced search for cards with tag filtering
     *
     * @param query Search text to find in question, answer, notes, or hint
     * @param deckIds List of deck IDs to search in (null for all accessible decks)
     * @param tagNames List of tag names to filter by
     * @param minDifficulty Minimum difficulty level (1-5)
     * @param maxDifficulty Maximum difficulty level (1-5)
     * @param minProgress Minimum learning progress (0.0-1.0)
     * @param maxProgress Maximum learning progress (0.0-1.0)
     * @param createdAfter Created after date
     * @param createdBefore Created before date
     * @param updatedAfter Updated after date
     * @param updatedBefore Updated before date
     * @param reviewDateAfter Next review date after
     * @param reviewDateBefore Next review date before
     * @param currentUser The current user
     * @param includeCollaborative Include cards from collaborative decks
     * @param includePublicDecks Include cards from public decks
     * @param includePrivateDecks Include cards from private decks
     * @param includeDueCards Include only cards that are due for review
     * @param pageable Pagination and sorting information
     * @return Page of cards matching the criteria
     */
    @Query(value = "SELECT DISTINCT c FROM Card c " +
           "JOIN FETCH c.deck d " +
           "JOIN d.deckTags dt " +
           "LEFT JOIN DeckCollaborator dc ON dc.deck = d AND dc.user.id = :userId " +
           "WHERE d.deleted = false " +
           "AND (:query IS NULL OR " +
           "    LOWER(c.question) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.answer) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.notes) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.hint) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "AND (:deckIds IS NULL OR d.id IN :deckIds) " +
           "AND (:tagNames IS NULL OR " +
           "    dt.tagName IN :tagNames) " +
           "AND (:minDifficulty IS NULL OR c.difficultyLevel >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR c.difficultyLevel <= :maxDifficulty) " +
           "AND (:minProgress IS NULL OR c.learningProgress >= :minProgress) " +
           "AND (:maxProgress IS NULL OR c.learningProgress <= :maxProgress) " +
           "AND (:createdAfter IS NULL OR CAST(c.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(c.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(c.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(c.updatedAt AS date) <= :updatedBefore) " +
           "AND (:reviewDateAfter IS NULL OR c.nextReviewDate >= :reviewDateAfter) " +
           "AND (:reviewDateBefore IS NULL OR c.nextReviewDate <= :reviewDateBefore) " +
           "AND (:includeDueCards = false OR c.nextReviewDate <= CURRENT_DATE) " +
           "AND (d.creator.id = :userId " +
           "    OR (:includeCollaborative = true AND dc.id IS NOT NULL) " +
           "    OR (:includePublicDecks = true AND d.isPublic = true) " +
           "    OR (:includePrivateDecks = true AND d.isPublic = false AND d.creator.id = :userId))",
           countQuery = "SELECT COUNT(DISTINCT c) FROM Card c " +
           "JOIN c.deck d " +
           "JOIN d.deckTags dt " +
           "LEFT JOIN DeckCollaborator dc ON dc.deck = d AND dc.user.id = :userId " +
           "WHERE d.deleted = false " +
           "AND (:query IS NULL OR " +
           "    LOWER(c.question) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.answer) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.notes) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.hint) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "AND (:deckIds IS NULL OR d.id IN :deckIds) " +
           "AND (:tagNames IS NULL OR " +
           "    dt.tagName IN :tagNames) " +
           "AND (:minDifficulty IS NULL OR c.difficultyLevel >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR c.difficultyLevel <= :maxDifficulty) " +
           "AND (:minProgress IS NULL OR c.learningProgress >= :minProgress) " +
           "AND (:maxProgress IS NULL OR c.learningProgress <= :maxProgress) " +
           "AND (:createdAfter IS NULL OR CAST(c.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(c.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(c.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(c.updatedAt AS date) <= :updatedBefore) " +
           "AND (:reviewDateAfter IS NULL OR c.nextReviewDate >= :reviewDateAfter) " +
           "AND (:reviewDateBefore IS NULL OR c.nextReviewDate <= :reviewDateBefore) " +
           "AND (:includeDueCards = false OR c.nextReviewDate <= CURRENT_DATE) " +
           "AND (d.creator.id = :userId " +
           "    OR (:includeCollaborative = true AND dc.id IS NOT NULL) " +
           "    OR (:includePublicDecks = true AND d.isPublic = true) " +
           "    OR (:includePrivateDecks = true AND d.isPublic = false AND d.creator.id = :userId))")
    Page<Card> advancedSearchWithTags(
            @Param("query") String query,
            @Param("deckIds") List<Long> deckIds,
            @Param("tagNames") List<String> tagNames,
            @Param("minDifficulty") Integer minDifficulty,
            @Param("maxDifficulty") Integer maxDifficulty,
            @Param("minProgress") Float minProgress,
            @Param("maxProgress") Float maxProgress,
            @Param("createdAfter") LocalDate createdAfter,
            @Param("createdBefore") LocalDate createdBefore,
            @Param("updatedAfter") LocalDate updatedAfter,
            @Param("updatedBefore") LocalDate updatedBefore,
            @Param("reviewDateAfter") LocalDate reviewDateAfter,
            @Param("reviewDateBefore") LocalDate reviewDateBefore,
            @Param("userId") Long userId,
            @Param("includeCollaborative") Boolean includeCollaborative,
            @Param("includePublicDecks") Boolean includePublicDecks,
            @Param("includePrivateDecks") Boolean includePrivateDecks,
            @Param("includeDueCards") Boolean includeDueCards,
            Pageable pageable);

    /**
     * Count cards deleted by user today
     *
     * @param userId The user ID
     * @return Number of cards deleted today
     */
    @Query("SELECT COUNT(c) FROM Card c WHERE c.deletedBy.id = :userId " +
           "AND c.deletedAt >= :startOfDay")
    long countDeletedByUserToday(@Param("userId") Long userId, @Param("startOfDay") LocalDateTime startOfDay);

    /**
     * Count cards deleted by user today (convenience method)
     *
     * @param userId The user ID
     * @return Number of cards deleted today
     */
    default long countDeletedByUserToday(Long userId) {
        LocalDateTime startOfDay = LocalDateTime.now().truncatedTo(java.time.temporal.ChronoUnit.DAYS);
        return countDeletedByUserToday(userId, startOfDay);
    }

    /**
     * Count cards by deck and deletion status
     *
     * @param deck The deck
     * @return Number of non-deleted cards in the deck
     */
    long countByDeckAndDeletedFalse(Deck deck);

    /**
     * Find cards due for review for a specific user and deck
     * Used by MobileStudyService for offline review functionality
     *
     * @param userId The user ID
     * @param deckId The deck ID
     * @param currentDateTime Current date/time to compare against next review date
     * @param pageable Pagination parameters
     * @return List of cards due for review
     */
    @Query("SELECT c FROM Card c JOIN FETCH c.deck d " +
           "WHERE c.deck.id = :deckId " +
           "AND d.creator.id = :userId " +
           "AND c.deleted = false " +
           "AND d.deleted = false " +
           "AND (c.nextReviewDate IS NULL OR c.nextReviewDate <= CAST(:currentDateTime AS date)) " +
           "AND d.creator.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED', 'FREE') " +
           "AND (d.creator.subscriptionEndDate IS NULL OR d.creator.subscriptionEndDate >= CURRENT_DATE) " +
           "ORDER BY c.nextReviewDate ASC, c.difficultyLevel DESC")
    List<Card> findCardsDueForReview(@Param("userId") Long userId,
                                   @Param("deckId") Long deckId,
                                   @Param("currentDateTime") LocalDateTime currentDateTime,
                                   Pageable pageable);

    /**
     * Unified advanced card search with comprehensive filtering and database-level optimizations
     */
    @Query(value = "SELECT DISTINCT c FROM Card c " +
           "LEFT JOIN FETCH c.deck d " +
           "LEFT JOIN FETCH d.creator dc " +
           "LEFT JOIN c.cardTags ct " +
           "LEFT JOIN DeckCollaborator dcol ON dcol.deck = d " +
           "WHERE (:query IS NULL OR " +
           "    LOWER(c.question) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.answer) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.notes) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "    LOWER(c.hint) LIKE LOWER(CONCAT('%', :query, '%'))) " +
           "AND (:deckIdsEmpty = true OR d.id IN :deckIds) " +
           "AND (:tagNamesEmpty = true OR EXISTS (SELECT 1 FROM c.cardTags ct2 WHERE ct2.tagName IN :tagNames)) " +
           "AND (:minDifficulty IS NULL OR c.difficultyLevel >= :minDifficulty) " +
           "AND (:maxDifficulty IS NULL OR c.difficultyLevel <= :maxDifficulty) " +
           "AND (:minProgress IS NULL OR c.learningProgress >= :minProgress) " +
           "AND (:maxProgress IS NULL OR c.learningProgress <= :maxProgress) " +
           "AND (:createdAfter IS NULL OR CAST(c.createdAt AS date) >= :createdAfter) " +
           "AND (:createdBefore IS NULL OR CAST(c.createdAt AS date) <= :createdBefore) " +
           "AND (:updatedAfter IS NULL OR CAST(c.updatedAt AS date) >= :updatedAfter) " +
           "AND (:updatedBefore IS NULL OR CAST(c.updatedAt AS date) <= :updatedBefore) " +
           "AND (:reviewDateAfter IS NULL OR c.nextReviewDate >= :reviewDateAfter) " +
           "AND (:reviewDateBefore IS NULL OR c.nextReviewDate <= :reviewDateBefore) " +
           "AND (:includeDueCards = false OR c.nextReviewDate <= CURRENT_DATE) " +
           "AND (d.creator.id = :userId " +
           "    OR (:includeCollaborative = true AND dcol.id IS NOT NULL) " +
           "    OR (:includePublicDecks = true AND d.isPublic = true) " +
           "    OR (:includePrivateDecks = true AND d.isPublic = false AND d.creator.id = :userId)) " +
           "AND dc.subscriptionStatus NOT IN ('EXPIRED', 'CANCELLED')")
    Page<Card> unifiedAdvancedSearch(
            @Param("query") String query,
            @Param("deckIds") List<Long> deckIds,
            @Param("deckIdsEmpty") boolean deckIdsEmpty,
            @Param("tagNames") List<String> tagNames,
            @Param("tagNamesEmpty") boolean tagNamesEmpty,
            @Param("minDifficulty") Integer minDifficulty,
            @Param("maxDifficulty") Integer maxDifficulty,
            @Param("minProgress") Float minProgress,
            @Param("maxProgress") Float maxProgress,
            @Param("createdAfter") java.time.LocalDate createdAfter,
            @Param("createdBefore") java.time.LocalDate createdBefore,
            @Param("updatedAfter") java.time.LocalDate updatedAfter,
            @Param("updatedBefore") java.time.LocalDate updatedBefore,
            @Param("reviewDateAfter") java.time.LocalDate reviewDateAfter,
            @Param("reviewDateBefore") java.time.LocalDate reviewDateBefore,
            @Param("includeCollaborative") boolean includeCollaborative,
            @Param("includePublicDecks") boolean includePublicDecks,
            @Param("includePrivateDecks") boolean includePrivateDecks,
            @Param("includeDueCards") boolean includeDueCards,
            @Param("userId") Long userId,
            Pageable pageable);

}