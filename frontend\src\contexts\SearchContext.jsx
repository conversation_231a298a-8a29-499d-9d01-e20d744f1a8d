// src/contexts/SearchContext.jsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// Search action types
const SEARCH_ACTIONS = {
  SET_QUERY: 'SET_QUERY',
  SET_FILTERS: 'SET_FILTERS',
  SET_SEARCH_TYPE: 'SET_SEARCH_TYPE',
  SET_RESULTS: 'SET_RESULTS',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  ADD_TO_HISTORY: 'ADD_TO_HISTORY',
  CLEAR_HISTORY: 'CLEAR_HISTORY',
  SET_SUGGESTIONS: 'SET_SUGGESTIONS',
  RESET_SEARCH: 'RESET_SEARCH',
  SET_PAGINATION: 'SET_PAGINATION',
  SAVE_FILTER_PRESET: 'SAVE_FILTER_PRESET',
  LOAD_FILTER_PRESET: 'LOAD_FILTER_PRESET',
  DELETE_FILTER_PRESET: 'DELETE_FILTER_PRESET'
};

// Initial search state
const initialState = {
  // Current search
  query: '',
  searchType: 'decks', // 'decks', 'cards', 'all'
  filters: {
    decks: {
      isPublic: null,
      creatorId: null,
      tagNames: [],
      minDifficulty: null,
      maxDifficulty: null,
      minCardCount: null,
      maxCardCount: null,
      includeFolders: null,
      parentFolderId: null,
      isCollaborative: null,
      createdAfter: null,
      createdBefore: null,
      updatedAfter: null,
      updatedBefore: null,
      favoritesOnly: false,
      includeExpiredCreators: false,
      enableRelevanceRanking: true,
      rankingStrategy: 'relevance'
    },
    cards: {
      deckIds: [],
      tagNames: [],
      minDifficulty: null,
      maxDifficulty: null,
      minProgress: null,
      maxProgress: null,
      createdAfter: null,
      createdBefore: null,
      updatedAfter: null,
      updatedBefore: null,
      reviewDateAfter: null,
      reviewDateBefore: null,
      includeCollaborative: true,
      includePublicDecks: true,
      includePrivateDecks: true,
      includeDueCards: false
    }
  },
  
  // Results
  results: {
    decks: null,
    cards: null
  },
  
  // UI state
  isLoading: false,
  error: null,
  
  // Pagination
  pagination: {
    decks: { page: 0, size: 12, totalElements: 0, totalPages: 0 },
    cards: { page: 0, size: 20, totalElements: 0, totalPages: 0 }
  },
  
  // Search history
  searchHistory: [],
  
  // Suggestions
  suggestions: {
    recent: [],
    popular: [],
    tags: [],
    content: []
  },
  
  // Filter presets
  filterPresets: [],
  
  // Search context
  searchContext: 'unified_search' // 'discover', 'unified_search', 'quick_search'
};

// Search reducer
function searchReducer(state, action) {
  switch (action.type) {
    case SEARCH_ACTIONS.SET_QUERY:
      return {
        ...state,
        query: action.payload,
        error: null
      };
      
    case SEARCH_ACTIONS.SET_SEARCH_TYPE:
      return {
        ...state,
        searchType: action.payload,
        results: {
          ...state.results,
          [action.payload]: null
        },
        pagination: {
          ...state.pagination,
          [action.payload]: { ...state.pagination[action.payload], page: 0 }
        }
      };
      
    case SEARCH_ACTIONS.SET_FILTERS:
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.searchType]: {
            ...state.filters[action.searchType],
            ...action.payload
          }
        },
        pagination: {
          ...state.pagination,
          [action.searchType]: { ...state.pagination[action.searchType], page: 0 }
        }
      };
      
    case SEARCH_ACTIONS.SET_RESULTS:
      return {
        ...state,
        results: {
          ...state.results,
          [action.searchType]: action.payload
        },
        isLoading: false,
        error: null
      };
      
    case SEARCH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
      
    case SEARCH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };
      
    case SEARCH_ACTIONS.ADD_TO_HISTORY:
      const newHistoryItem = {
        query: action.payload.query,
        searchType: action.payload.searchType,
        filters: action.payload.filters,
        timestamp: new Date().toISOString(),
        resultCount: action.payload.resultCount
      };
      
      // Remove duplicate and add to beginning
      const filteredHistory = state.searchHistory.filter(
        item => !(item.query === newHistoryItem.query && item.searchType === newHistoryItem.searchType)
      );
      
      return {
        ...state,
        searchHistory: [newHistoryItem, ...filteredHistory].slice(0, 50) // Keep last 50 searches
      };
      
    case SEARCH_ACTIONS.CLEAR_HISTORY:
      return {
        ...state,
        searchHistory: []
      };
      
    case SEARCH_ACTIONS.SET_SUGGESTIONS:
      return {
        ...state,
        suggestions: action.payload
      };
      
    case SEARCH_ACTIONS.RESET_SEARCH:
      return {
        ...initialState,
        searchHistory: state.searchHistory,
        filterPresets: state.filterPresets
      };
      
    case SEARCH_ACTIONS.SET_PAGINATION:
      return {
        ...state,
        pagination: {
          ...state.pagination,
          [action.searchType]: {
            ...state.pagination[action.searchType],
            ...action.payload
          }
        }
      };
      
    case SEARCH_ACTIONS.SAVE_FILTER_PRESET:
      const preset = {
        id: Date.now().toString(),
        name: action.payload.name,
        searchType: action.payload.searchType,
        filters: action.payload.filters,
        createdAt: new Date().toISOString()
      };
      
      return {
        ...state,
        filterPresets: [...state.filterPresets, preset]
      };
      
    case SEARCH_ACTIONS.LOAD_FILTER_PRESET:
      const loadedPreset = state.filterPresets.find(p => p.id === action.payload);
      if (!loadedPreset) return state;
      
      return {
        ...state,
        searchType: loadedPreset.searchType,
        filters: {
          ...state.filters,
          [loadedPreset.searchType]: loadedPreset.filters
        }
      };
      
    case SEARCH_ACTIONS.DELETE_FILTER_PRESET:
      return {
        ...state,
        filterPresets: state.filterPresets.filter(p => p.id !== action.payload)
      };
      
    default:
      return state;
  }
}

// Create context
const SearchContext = createContext();

// Search provider component
export const SearchProvider = ({ children }) => {
  const [state, dispatch] = useReducer(searchReducer, initialState);
  const location = useLocation();
  const navigate = useNavigate();

  // Load state from localStorage on mount
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem('searchHistory');
      const savedPresets = localStorage.getItem('filterPresets');
      
      if (savedHistory) {
        const history = JSON.parse(savedHistory);
        dispatch({ type: SEARCH_ACTIONS.ADD_TO_HISTORY, payload: history });
      }
      
      if (savedPresets) {
        const presets = JSON.parse(savedPresets);
        presets.forEach(preset => {
          dispatch({ type: SEARCH_ACTIONS.SAVE_FILTER_PRESET, payload: preset });
        });
      }
    } catch (error) {
      console.error('Failed to load search state from localStorage:', error);
    }
  }, []);

  // Save state to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem('searchHistory', JSON.stringify(state.searchHistory));
      localStorage.setItem('filterPresets', JSON.stringify(state.filterPresets));
    } catch (error) {
      console.error('Failed to save search state to localStorage:', error);
    }
  }, [state.searchHistory, state.filterPresets]);

  // Sync URL with search state
  useEffect(() => {
    if (location.pathname === '/app/search') {
      const searchParams = new URLSearchParams(location.search);
      const urlQuery = searchParams.get('q');
      const urlType = searchParams.get('type');
      
      if (urlQuery && urlQuery !== state.query) {
        dispatch({ type: SEARCH_ACTIONS.SET_QUERY, payload: urlQuery });
      }
      
      if (urlType && urlType !== state.searchType) {
        dispatch({ type: SEARCH_ACTIONS.SET_SEARCH_TYPE, payload: urlType });
      }
    }
  }, [location, state.query, state.searchType]);

  // Action creators
  const actions = {
    setQuery: (query) => dispatch({ type: SEARCH_ACTIONS.SET_QUERY, payload: query }),
    setSearchType: (type) => dispatch({ type: SEARCH_ACTIONS.SET_SEARCH_TYPE, payload: type }),
    setFilters: (filters, searchType) => dispatch({ 
      type: SEARCH_ACTIONS.SET_FILTERS, 
      payload: filters, 
      searchType: searchType || state.searchType 
    }),
    setResults: (results, searchType) => dispatch({ 
      type: SEARCH_ACTIONS.SET_RESULTS, 
      payload: results, 
      searchType: searchType || state.searchType 
    }),
    setLoading: (loading) => dispatch({ type: SEARCH_ACTIONS.SET_LOADING, payload: loading }),
    setError: (error) => dispatch({ type: SEARCH_ACTIONS.SET_ERROR, payload: error }),
    addToHistory: (searchData) => dispatch({ type: SEARCH_ACTIONS.ADD_TO_HISTORY, payload: searchData }),
    clearHistory: () => dispatch({ type: SEARCH_ACTIONS.CLEAR_HISTORY }),
    setSuggestions: (suggestions) => dispatch({ type: SEARCH_ACTIONS.SET_SUGGESTIONS, payload: suggestions }),
    resetSearch: () => dispatch({ type: SEARCH_ACTIONS.RESET_SEARCH }),
    setPagination: (pagination, searchType) => dispatch({ 
      type: SEARCH_ACTIONS.SET_PAGINATION, 
      payload: pagination, 
      searchType: searchType || state.searchType 
    }),
    saveFilterPreset: (preset) => dispatch({ type: SEARCH_ACTIONS.SAVE_FILTER_PRESET, payload: preset }),
    loadFilterPreset: (presetId) => dispatch({ type: SEARCH_ACTIONS.LOAD_FILTER_PRESET, payload: presetId }),
    deleteFilterPreset: (presetId) => dispatch({ type: SEARCH_ACTIONS.DELETE_FILTER_PRESET, payload: presetId }),
    
    // Utility actions
    updateUrl: (query, searchType) => {
      const params = new URLSearchParams();
      if (query) params.set('q', query);
      if (searchType) params.set('type', searchType);
      
      const newUrl = `/app/search${params.toString() ? '?' + params.toString() : ''}`;
      navigate(newUrl, { replace: true });
    }
  };

  return (
    <SearchContext.Provider value={{ state, actions }}>
      {children}
    </SearchContext.Provider>
  );
};

// Custom hook to use search context
export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

export default SearchContext;
