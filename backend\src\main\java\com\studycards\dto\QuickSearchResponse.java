package com.studycards.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for quick search that includes both deck and card results
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuickSearchResponse {
    
    private String query;
    
    private List<DeckResponse> deckResults;
    private List<CardResponse> cardResults;
    
    private long totalDeckResults;
    private long totalCardResults;
    
    private long searchDurationMs;
    private String searchId;
    
    // Metadata for analytics
    private String searchContext;
    private java.time.LocalDateTime searchTimestamp;
}
