2025-07-20 00:32:02.938 [http-nio-8082-exec-6] WARN  PERFORMANCE - 
                [] [2935ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 2935ms
2025-07-20 00:32:03.018 [ForkJoinPool.commonPool-worker-19] INFO  PERFORMANCE - 
                [] [162ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 162ms
2025-07-20 00:32:03.095 [MessageBroker-8] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [328ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 328ms
2025-07-20 00:32:03.300 [ForkJoinPool.commonPool-worker-19] INFO  PERFORMANCE - 
                [] [281ms] [] DB_OPERATION: $Proxy194.countActiveU<PERSON>s<PERSON><PERSON>ce took 281ms
2025-07-20 00:32:03.304 [http-nio-8082-exec-6] INFO  PERFORMANCE - 
                [getCurrentUser] [292ms] [] DB_OPERATION: $Proxy194.findById took 292ms
2025-07-20 00:32:03.344 [MessageBroker-8] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [647ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 647ms
2025-07-20 00:32:03.552 [ForkJoinPool.commonPool-worker-19] INFO  PERFORMANCE - 
                [] [250ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 250ms
2025-07-20 00:32:03.933 [ForkJoinPool.commonPool-worker-19] INFO  PERFORMANCE - 
                [] [278ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 278ms
2025-07-20 00:32:04.314 [ForkJoinPool.commonPool-worker-19] INFO  PERFORMANCE - 
                [] [381ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 381ms
2025-07-20 00:32:07.704 [ForkJoinPool.commonPool-worker-19] WARN  PERFORMANCE - 
                [] [3390ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalStudyHours took 3390ms
2025-07-20 00:32:09.082 [ForkJoinPool.commonPool-worker-19] WARN  PERFORMANCE - 
                [] [1271ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 1271ms
2025-07-20 00:32:09.146 [MessageBroker-6] WARN  PERFORMANCE - 
                [cacheWarming] [6428ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 6428ms
2025-07-20 00:32:09.158 [MessageBroker-6] WARN  PERFORMANCE - 
                [cacheWarming] [6464ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 6464ms
2025-07-20 00:32:23.873 [http-nio-8082-exec-4] WARN  PERFORMANCE - 
                [] [8539ms] [] SLOW_DB_OPERATION: $Proxy194.findByEmail took 8539ms
2025-07-20 03:56:25.319 [MessageBroker-15] INFO  PERFORMANCE - 
                [cleanupOldDeletedDecks] [260ms] [] DB_OPERATION: $Proxy206.findByDeletedTrueAndDeletedAtBefore took 260ms
2025-07-20 21:50:16.023 [MessageBroker-12] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [183ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 183ms
2025-07-20 21:50:16.026 [ForkJoinPool.commonPool-worker-25] INFO  PERFORMANCE - 
                [] [198ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 198ms
2025-07-20 21:50:16.152 [ForkJoinPool.commonPool-worker-25] INFO  PERFORMANCE - 
                [] [126ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 126ms
2025-07-20 21:50:16.218 [MessageBroker-1] INFO  PERFORMANCE - 
                [generateDailyNotifications] [325ms] [] DB_OPERATION: $Proxy194.findAll took 325ms
2025-07-20 21:50:16.272 [ForkJoinPool.commonPool-worker-25] INFO  PERFORMANCE - 
                [] [120ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 120ms
2025-07-20 21:50:16.421 [MessageBroker-1] INFO  PERFORMANCE - 
                [generateDailyNotifications] [202ms] [] DB_OPERATION: $Proxy207.findAllDueCardsForUser took 202ms
2025-07-20 21:50:16.535 [MessageBroker-1] ERROR PERFORMANCE - 
                [generateDailyNotifications] [79ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 79ms with error: InvalidDataAccessResourceUsageException
2025-07-20 21:50:16.839 [MessageBroker-1] ERROR PERFORMANCE - 
                [generateDailyNotifications] [296ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 296ms with error: InvalidDataAccessResourceUsageException
2025-07-20 21:50:16.854 [MessageBroker-1] ERROR PERFORMANCE - 
                [generateDailyNotifications] [6ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 6ms with error: InvalidDataAccessResourceUsageException
2025-07-20 21:50:16.854 [MessageBroker-1] WARN  PERFORMANCE - 
                [generateDailyNotifications] [1016ms] [] SLOW_SERVICE_METHOD: DashboardService.generateDailyNotifications took 1016ms
2025-07-20 21:50:16.877 [ForkJoinPool.commonPool-worker-25] INFO  PERFORMANCE - 
                [] [367ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 367ms
2025-07-20 21:50:16.946 [MessageBroker-11] WARN  PERFORMANCE - 
                [cacheWarming] [1119ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1119ms
2025-07-20 21:50:16.948 [MessageBroker-11] WARN  PERFORMANCE - 
                [cacheWarming] [1128ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1128ms
2025-07-20 21:50:17.039 [StudyCards-Async-5] WARN  PERFORMANCE - 
                [createDueCardsReminder] [548ms] [] SLOW_DB_OPERATION: $Proxy220.save took 548ms
2025-07-20 21:50:17.039 [StudyCards-Async-5] INFO  PERFORMANCE - 
                [createDueCardsReminder] [580ms] [] SERVICE_METHOD: NotificationService.createDueCardsReminder took 580ms
2025-07-20 21:50:17.112 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [137ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 137ms
2025-07-20 21:50:17.376 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [135ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 135ms
2025-07-20 21:50:18.399 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [1019ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 1019ms
2025-07-20 21:50:18.654 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [133ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 133ms
2025-07-20 21:50:18.655 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1816ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1816ms
2025-07-20 21:50:18.657 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1820ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1820ms
2025-07-20 21:50:19.965 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [1307ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1307ms
2025-07-20 21:50:20.321 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1663ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1663ms
2025-07-20 21:50:20.322 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1665ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1665ms
2025-07-20 21:50:20.493 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [134ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 134ms
2025-07-20 21:50:20.922 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [429ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 429ms
2025-07-20 21:50:21.054 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [129ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 129ms
2025-07-20 21:50:21.332 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1009ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1009ms
2025-07-20 21:50:21.334 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1012ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1012ms
2025-07-20 21:50:26.367 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [5030ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 5030ms
2025-07-20 21:50:26.439 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5103ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 5103ms
2025-07-20 21:50:26.440 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5106ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 5106ms
2025-07-20 21:51:01.377 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [810ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 810ms
2025-07-20 21:51:01.377 [ForkJoinPool.commonPool-worker-25] WARN  PERFORMANCE - 
                [] [5786ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 5786ms
2025-07-20 21:51:01.428 [MessageBroker-16] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5925ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 5925ms
2025-07-20 21:51:01.429 [MessageBroker-16] WARN  PERFORMANCE - 
                [periodicHealthCheck] [5933ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 5933ms
