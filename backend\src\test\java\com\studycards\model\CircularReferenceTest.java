package com.studycards.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashSet;

/**
 * Test to verify that circular reference issues in hashCode/equals methods are resolved
 */
public class CircularReferenceTest {

    @Test
    @DisplayName("Should not cause StackOverflowError when calculating hashCode for User with favorite decks")
    void testUserHashCodeWithFavoriteDecks() {
        // Given
        User user = User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password")
                .build();
        user.setId(1L);

        Deck deck = Deck.builder()
                .title("Test Deck")
                .isPublic(true)
                .creator(user)
                .build();
        deck.setId(2L);

        // Create bidirectional relationship
        user.getFavoriteDecks().add(deck);
        deck.getFavoriteBy().add(user);

        // When & Then - should not throw StackOverflowError
        assertDoesNotThrow(() -> {
            int userHashCode = user.hashCode();
            int deckHashCode = deck.hashCode();
            
            // Verify hash codes are calculated successfully
            assertNotEquals(0, userHashCode);
            assertNotEquals(0, deckHashCode);
        });
    }

    @Test
    @DisplayName("Should not cause StackOverflowError when calculating hashCode for Deck with cards")
    void testDeckHashCodeWithCards() {
        // Given
        User user = User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password")
                .build();
        user.setId(1L);

        Deck deck = Deck.builder()
                .title("Test Deck")
                .isPublic(true)
                .creator(user)
                .build();
        deck.setId(2L);

        Card card = Card.builder()
                .question("Test Question")
                .answer("Test Answer")
                .difficultyLevel(1)
                .deck(deck)
                .build();
        card.setId(3L);

        // Create bidirectional relationship
        deck.getCards().add(card);

        // When & Then - should not throw StackOverflowError
        assertDoesNotThrow(() -> {
            int deckHashCode = deck.hashCode();
            int cardHashCode = card.hashCode();
            
            // Verify hash codes are calculated successfully
            assertNotEquals(0, deckHashCode);
            assertNotEquals(0, cardHashCode);
        });
    }

    @Test
    @DisplayName("Should not cause StackOverflowError when adding entities to HashSet")
    void testEntitiesInHashSet() {
        // Given
        User user = User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password")
                .build();
        user.setId(1L);

        Deck deck = Deck.builder()
                .title("Test Deck")
                .isPublic(true)
                .creator(user)
                .build();
        deck.setId(2L);

        // Create bidirectional relationship
        user.getFavoriteDecks().add(deck);
        deck.getFavoriteBy().add(user);

        // When & Then - should not throw StackOverflowError when using in collections
        assertDoesNotThrow(() -> {
            HashSet<User> users = new HashSet<>();
            HashSet<Deck> decks = new HashSet<>();
            
            users.add(user);
            decks.add(deck);
            
            // Verify collections work properly
            assertTrue(users.contains(user));
            assertTrue(decks.contains(deck));
        });
    }

    @Test
    @DisplayName("Should maintain proper equals behavior based on ID")
    void testEqualsBasedOnId() {
        // Given
        User user1 = User.builder()
                .username("testuser1")
                .email("<EMAIL>")
                .password("password")
                .build();
        user1.setId(1L);

        User user2 = User.builder()
                .username("testuser2")
                .email("<EMAIL>")
                .password("password")
                .build();
        user2.setId(1L); // Same ID

        User user3 = User.builder()
                .username("testuser3")
                .email("<EMAIL>")
                .password("password")
                .build();
        user3.setId(2L); // Different ID

        // When & Then
        assertEquals(user1, user2); // Same ID should be equal
        assertNotEquals(user1, user3); // Different ID should not be equal
        assertEquals(user1.hashCode(), user2.hashCode()); // Same ID should have same hash code
    }
}
