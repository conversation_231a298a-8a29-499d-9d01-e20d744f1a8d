import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { BrowserRouter } from 'react-router-dom';
import StudySession from '../pages/StudySession';

// Mock the API
jest.mock('../api/study', () => ({
  createStudySession: jest.fn(() => Promise.resolve({ id: 1 })), // Fixed: should return 'id' not 'sessionId'
  recordEnhancedCardPerformance: jest.fn(() => Promise.resolve({})),
  completeStudySession: jest.fn(() => Promise.resolve({})),
  getSessionRatings: jest.fn(() => Promise.resolve({})),
}));

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ deckId: '1' }),
  useNavigate: () => jest.fn(),
}));

// Mock other dependencies
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
  },
}));

jest.mock('dompurify', () => ({
  sanitize: jest.fn((input) => input),
}));

const mockCards = [
  { id: 1, question: 'What is React?', answer: 'A JavaScript library', difficultyLevel: 1 },
  { id: 2, question: 'What is JSX?', answer: 'JavaScript XML', difficultyLevel: 2 },
  { id: 3, question: 'What is a component?', answer: 'Reusable UI element', difficultyLevel: 1 },
];

const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('StudySession Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should wait for session creation before showing cards', async () => {
    // Mock delayed session creation
    const mockCreateSession = jest.fn(() =>
      new Promise(resolve => setTimeout(() => resolve({ id: 1 }), 100))
    );

    // Mock the cards API
    jest.doMock('../api/cards', () => ({
      getCardsByDeckId: jest.fn(() => Promise.resolve(mockCards)),
    }));

    // Mock the study API with delayed session creation
    jest.doMock('../api/study', () => ({
      createStudySession: mockCreateSession,
      recordEnhancedCardPerformance: jest.fn(() => Promise.resolve({})),
      completeStudySession: jest.fn(() => Promise.resolve({})),
      getSessionRatings: jest.fn(() => Promise.resolve({})),
    }));

    const mockUseQuery = jest.fn()
      .mockReturnValueOnce({ data: mockCards, isLoading: false, error: null }) // cards query
      .mockReturnValue({ data: null, isLoading: false, error: null }); // other queries

    const mockUseMutation = jest.fn()
      .mockReturnValueOnce({ // createSessionMutation
        mutate: mockCreateSession,
        isLoading: true, // Initially loading
        isError: false,
        error: null,
      })
      .mockReturnValue({ // other mutations
        mutate: jest.fn(),
        isLoading: false,
        isError: false,
        error: null,
      });

    jest.doMock('react-query', () => ({
      ...jest.requireActual('react-query'),
      useQuery: mockUseQuery,
      useMutation: mockUseMutation,
    }));

    render(
      <TestWrapper>
        <StudySession />
      </TestWrapper>
    );

    // Should show loading state initially
    expect(screen.getByText('Preparing study session...')).toBeInTheDocument();

    // Should not show cards yet
    expect(screen.queryByText('What is React?')).not.toBeInTheDocument();

    // Should not show rating buttons yet
    expect(screen.queryByText('Again')).not.toBeInTheDocument();
    expect(screen.queryByText('Good')).not.toBeInTheDocument();
  });

  test('should handle card rating and advance to next card', async () => {
    // Mock the cards query
    const mockUseQuery = jest.fn()
      .mockReturnValueOnce({ data: mockCards, isLoading: false, error: null }) // cards query
      .mockReturnValue({ data: null, isLoading: false, error: null }); // other queries

    jest.doMock('react-query', () => ({
      ...jest.requireActual('react-query'),
      useQuery: mockUseQuery,
      useMutation: jest.fn(() => ({
        mutate: jest.fn(),
        isLoading: false,
        error: null,
      })),
    }));

    render(
      <TestWrapper>
        <StudySession />
      </TestWrapper>
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('What is React?')).toBeInTheDocument();
    });

    // Simulate rating a card (should advance to next card)
    const ratingButton = screen.getByText('Good');
    fireEvent.click(ratingButton);

    // Should advance to next card
    await waitFor(() => {
      expect(screen.getByText('What is JSX?')).toBeInTheDocument();
    });
  });

  test('should handle skip functionality and update statistics correctly', async () => {
    const mockUseQuery = jest.fn()
      .mockReturnValueOnce({ data: mockCards, isLoading: false, error: null })
      .mockReturnValue({ data: null, isLoading: false, error: null });

    jest.doMock('react-query', () => ({
      ...jest.requireActual('react-query'),
      useQuery: mockUseQuery,
      useMutation: jest.fn(() => ({
        mutate: jest.fn(),
        isLoading: false,
        error: null,
      })),
    }));

    render(
      <TestWrapper>
        <StudySession />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('What is React?')).toBeInTheDocument();
    });

    // Skip the first card
    const skipButton = screen.getByText('Skip');
    fireEvent.click(skipButton);

    // Should advance to next card
    await waitFor(() => {
      expect(screen.getByText('What is JSX?')).toBeInTheDocument();
    });

    // Skip the second card
    fireEvent.click(skipButton);

    // Should advance to third card
    await waitFor(() => {
      expect(screen.getByText('What is a component?')).toBeInTheDocument();
    });

    // Rate the third card as correct
    const goodButton = screen.getByText('Good');
    fireEvent.click(goodButton);

    // Should complete the session and show results
    await waitFor(() => {
      expect(screen.getByText('Study Session Complete!')).toBeInTheDocument();
    });

    // Check accuracy calculation (should be 100% since only 1 card was rated and it was correct)
    // Skipped cards should not affect accuracy
    await waitFor(() => {
      expect(screen.getByText('100%')).toBeInTheDocument();
      expect(screen.getByText('1 correct answers')).toBeInTheDocument();
    });
  });

  test('should calculate accuracy correctly with mixed ratings', async () => {
    const mockUseQuery = jest.fn()
      .mockReturnValueOnce({ data: mockCards, isLoading: false, error: null })
      .mockReturnValue({ data: null, isLoading: false, error: null });

    jest.doMock('react-query', () => ({
      ...jest.requireActual('react-query'),
      useQuery: mockUseQuery,
      useMutation: jest.fn(() => ({
        mutate: jest.fn(),
        isLoading: false,
        error: null,
      })),
    }));

    render(
      <TestWrapper>
        <StudySession />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('What is React?')).toBeInTheDocument();
    });

    // Rate first card as incorrect
    const againButton = screen.getByText('Again');
    fireEvent.click(againButton);

    await waitFor(() => {
      expect(screen.getByText('What is JSX?')).toBeInTheDocument();
    });

    // Rate second card as correct
    const goodButton = screen.getByText('Good');
    fireEvent.click(goodButton);

    await waitFor(() => {
      expect(screen.getByText('What is a component?')).toBeInTheDocument();
    });

    // Skip third card
    const skipButton = screen.getByText('Skip');
    fireEvent.click(skipButton);

    // Should complete session
    await waitFor(() => {
      expect(screen.getByText('Study Session Complete!')).toBeInTheDocument();
    });

    // Accuracy should be 50% (1 correct out of 2 rated cards, skip doesn't count)
    await waitFor(() => {
      expect(screen.getByText('50%')).toBeInTheDocument();
      expect(screen.getByText('1 correct answers')).toBeInTheDocument();
    });
  });

  test('should not show confetti for poor performance', async () => {
    const mockUseQuery = jest.fn()
      .mockReturnValueOnce({ data: mockCards, isLoading: false, error: null })
      .mockReturnValue({ data: null, isLoading: false, error: null });

    jest.doMock('react-query', () => ({
      ...jest.requireActual('react-query'),
      useQuery: mockUseQuery,
      useMutation: jest.fn(() => ({
        mutate: jest.fn(),
        isLoading: false,
        error: null,
      })),
    }));

    render(
      <TestWrapper>
        <StudySession />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('What is React?')).toBeInTheDocument();
    });

    // Rate all cards as incorrect (0% accuracy)
    const againButton = screen.getByText('Again');

    // First card
    fireEvent.click(againButton);
    await waitFor(() => {
      expect(screen.getByText('What is JSX?')).toBeInTheDocument();
    });

    // Second card
    fireEvent.click(againButton);
    await waitFor(() => {
      expect(screen.getByText('What is a component?')).toBeInTheDocument();
    });

    // Third card
    fireEvent.click(againButton);

    // Should complete session
    await waitFor(() => {
      expect(screen.getByText('Study Session Complete!')).toBeInTheDocument();
    });

    // Should show 0% accuracy and NO confetti
    await waitFor(() => {
      expect(screen.getByText('0%')).toBeInTheDocument();
      expect(screen.getByText('0 correct answers')).toBeInTheDocument();
    });

    // Confetti should not be present (no confetti component rendered)
    expect(screen.queryByTestId('confetti')).not.toBeInTheDocument();
  });

  test('should auto-shuffle cards on load and not have shuffle button', async () => {
    const mockUseQuery = jest.fn()
      .mockReturnValueOnce({ data: mockCards, isLoading: false, error: null })
      .mockReturnValue({ data: null, isLoading: false, error: null });

    jest.doMock('react-query', () => ({
      ...jest.requireActual('react-query'),
      useQuery: mockUseQuery,
      useMutation: jest.fn(() => ({
        mutate: jest.fn(),
        isLoading: false,
        error: null,
      })),
    }));

    render(
      <TestWrapper>
        <StudySession />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('What is React?')).toBeInTheDocument();
    });

    // Shuffle button should not exist
    expect(screen.queryByText('Shuffle')).not.toBeInTheDocument();
    expect(screen.queryByText('Original Order')).not.toBeInTheDocument();

    // Cards should be loaded (auto-shuffled internally)
    expect(screen.getByText('What is React?')).toBeInTheDocument();
  });
});
