# DeckDiscoveryService LazyInitializationException Fix

## Problem Description

The application was experiencing `LazyInitializationException` errors when accessing the DeckDiscoveryService endpoints (`/api/discover/trending` and `/api/discover/new`). The error occurred because:

1. The `DeckDiscoveryService.getTrendingDecks()` and `getNewlyCreatedDecks()` methods were not annotated with `@Transactional`
2. These methods called repository methods that returned Deck entities with lazy-loaded User (creator) relationships
3. When `subscriptionStatusService.hasActiveSubscription(deck.getCreator())` was called, it tried to access `user.getSubscriptionStatus()` on a lazy-loaded proxy
4. Since there was no active Hibernate session, the lazy-loaded User proxy could not be initialized

## Root Cause Analysis

The stack trace showed the exception originated from:
```
SubscriptionStatusService.updateExpiredTrialForUser(SubscriptionStatusService.java:172)
  at user.getSubscriptionStatus() // Line 172 - accessing lazy-loaded property
```

This was called from:
```
DeckDiscoveryService.getTrendingDecks() -> subscriptionStatusService.hasActiveSubscription(deck.getCreator())
DeckDiscoveryService.getNewlyCreatedDecks() -> subscriptionStatusService.hasActiveSubscription(deck.getCreator())
DeckDiscoveryService.getDecksByTag() -> subscriptionStatusService.hasActiveSubscription(deck.getCreator())
```

## Solution Implemented

### 1. Fixed DeckDiscoveryService Methods

Added `@Transactional(readOnly = true)` annotations to methods that access lazy-loaded User properties:

**Before:**
```java
@Cacheable(value = "trendingDecks", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #createdAfter")
public Page<DeckResponse> getTrendingDecks(Pageable pageable, LocalDateTime createdAfter) {
```

**After:**
```java
@Cacheable(value = "trendingDecks", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #createdAfter")
@Transactional(readOnly = true)
public Page<DeckResponse> getTrendingDecks(Pageable pageable, LocalDateTime createdAfter) {
```

### 2. Fixed Methods in DeckDiscoveryService

- `getTrendingDecks()` - Added `@Transactional(readOnly = true)`
- `getNewlyCreatedDecks()` - Added `@Transactional(readOnly = true)`
- `getDecksByTag()` - Added `@Transactional(readOnly = true)`

### 3. Enhanced SubscriptionStatusService

Added `@Transactional` annotation to `hasActiveSubscription()` method:

```java
@Transactional
public boolean hasActiveSubscription(User user) {
```

This ensures that when the method calls `updateExpiredTrialForUser()` and `updateExpiredSubscriptionForUser()` (which are write operations), it has the proper transaction context.

### 4. Enhanced ContentVisibilityService

Added missing `@Transactional(readOnly = true)` annotations to methods that access User properties:

- `canAccessPremiumFeatures()`
- `isProfileVisible()`
- `canCollaborate()`
- `canAccessStatistics()`
- `canAccessPatternAnalysis()`

## Files Modified

1. `backend/src/main/java/com/studycards/service/DeckDiscoveryService.java`
   - Added `@Transactional(readOnly = true)` to `getTrendingDecks()`, `getNewlyCreatedDecks()`, and `getDecksByTag()`
   - Added import for `org.springframework.transaction.annotation.Transactional`

2. `backend/src/main/java/com/studycards/service/SubscriptionStatusService.java`
   - Added `@Transactional` to `hasActiveSubscription()` method

3. `backend/src/main/java/com/studycards/service/ContentVisibilityService.java`
   - Added `@Transactional(readOnly = true)` to multiple methods that access User properties

## Tests Created

1. `backend/src/test/java/com/studycards/service/DeckDiscoveryServiceLazyInitializationTest.java`
   - Unit tests with mocked dependencies to verify the fix works

2. `backend/src/test/java/com/studycards/integration/DeckDiscoveryLazyInitializationFixTest.java`
   - Integration tests to verify the fix works in a real Spring context

## Benefits of This Solution

1. **Minimal Code Changes**: Only added annotations, no structural changes to existing code
2. **Performance**: Uses read-only transactions where appropriate, which can be optimized by the database
3. **Consistency**: Follows the same pattern used elsewhere in the codebase
4. **Maintainability**: Clear transaction boundaries make the code easier to understand and maintain
5. **Caching Compatibility**: The `@Transactional` annotations work well with the existing `@Cacheable` annotations

## Verification

The fix can be verified by:

1. Running the new unit and integration tests
2. Testing the endpoints manually:
   - `GET /api/discover/trending`
   - `GET /api/discover/new`
   - `GET /api/discover/decks/by-tag?tagName=test`
3. Checking the application logs for absence of LazyInitializationException errors

## Prevention

To prevent similar issues in the future:

1. Always add `@Transactional(readOnly = true)` to service methods that read lazy-loaded entity relationships
2. Use `@Transactional` (without readOnly) for methods that perform write operations
3. Consider using eager loading with `JOIN FETCH` in repository queries when you know you'll need the related entities
4. Add integration tests for new service methods that work with entity relationships

## Related Issues

This fix also resolves similar potential issues in:
- ContentVisibilityService methods that access User subscription status
- Any other service methods that might access lazy-loaded User properties through Deck entities

The fix ensures that all subscription status checks have proper transaction context, preventing LazyInitializationException across the application.
