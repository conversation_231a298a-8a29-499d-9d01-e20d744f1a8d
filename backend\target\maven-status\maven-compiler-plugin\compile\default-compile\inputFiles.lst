D:\studyCards\backend\src\main\java\com\studycards\controller\OAuth2Controller.java
D:\studyCards\backend\src\main\java\com\studycards\repository\StudySessionRepository.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CollaborationController.java
D:\studyCards\backend\src\main\java\com\studycards\monitoring\AlertContext.java
D:\studyCards\backend\src\main\java\com\studycards\event\CacheInvalidationEventListener.java
D:\studyCards\backend\src\main\java\com\studycards\repository\DeckRepositoryCustom.java
D:\studyCards\backend\src\main\java\com\studycards\service\AuthService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\MonitoringController.java
D:\studyCards\backend\src\main\java\com\studycards\controller\PublicStatisticsController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckAccessValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\CardHintService.java
D:\studyCards\backend\src\main\java\com\studycards\interceptor\SubscriptionInterceptor.java
D:\studyCards\backend\src\main\java\com\studycards\enums\CollaboratorPermission.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CardRepository.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserPreferencesService.java
D:\studyCards\backend\src\main\java\com\studycards\config\DataInitializer.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CreateCollaborationTemplateRequest.java
D:\studyCards\backend\src\main\java\com\studycards\model\SearchHistory.java
D:\studyCards\backend\src\main\java\com\studycards\util\CircuitBreaker.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StartAdaptiveExamRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\paymob\PaymobAuthResponse.java
D:\studyCards\backend\src\main\java\com\studycards\type\JsonNodeType.java
D:\studyCards\backend\src\main\java\com\studycards\service\AuditService.java
D:\studyCards\backend\src\main\java\com\studycards\service\ContentVisibilityService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckAuthorizationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PasswordGenerationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserSearchResponse.java
D:\studyCards\backend\src\main\java\com\studycards\repository\LoginActivityRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\QuickSearchResponse.java
D:\studyCards\backend\src\main\java\com\studycards\repository\SecurityAuditLogRepository.java
D:\studyCards\backend\src\main\java\com\studycards\entity\CardHint.java
D:\studyCards\backend\src\main\java\com\studycards\service\AuditLoggingService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedInvitationDetails.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BulkReviewResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\ErrorMessageService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\StudySessionException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserAchievementResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationExportResponse.java
D:\studyCards\backend\src\main\java\com\studycards\i18n\EnumMessageService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticPatternAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\controller\UserPreferencesController.java
D:\studyCards\backend\src\main\java\com\studycards\exception\SecurityException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\paymob\PaymobPaymentKeyRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\RefreshTokenService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AIEnhancedFeedback.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudyActivityResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticComparisonResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaboratorResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\RepositoryValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\PaymobWebhookController.java
D:\studyCards\backend\src\main\java\com\studycards\service\JwtConfigurationValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\monitoring\EnumMonitoringService.java
D:\studyCards\backend\src\main\java\com\studycards\config\RateLimitingConfig.java
D:\studyCards\backend\src\main\java\com\studycards\repository\BillingTransactionRepository.java
D:\studyCards\backend\src\main\java\com\studycards\service\ExamValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AdaptiveExamSession.java
D:\studyCards\backend\src\main\java\com\studycards\model\PasswordHistory.java
D:\studyCards\backend\src\main\java\com\studycards\repository\PasswordHistoryRepository.java
D:\studyCards\backend\src\main\java\com\studycards\converter\QuestionTypeConverter.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardPerformanceResponse.java
D:\studyCards\backend\src\main\java\com\studycards\exception\InvalidCaptchaException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticPerformanceMetrics.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ValidationErrorResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\AnswerFeedbackService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ChangePasswordRequest.java
D:\studyCards\backend\src\main\java\com\studycards\repository\AnswerEvaluationReportRepository.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckService.java
D:\studyCards\backend\src\main\java\com\studycards\model\AuditLog.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckSummaryResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BatchCardCreateRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CaptchaController.java
D:\studyCards\backend\src\main\java\com\studycards\controller\UserReportController.java
D:\studyCards\backend\src\main\java\com\studycards\config\SpacedRepetitionConfig.java
D:\studyCards\backend\src\main\java\com\studycards\config\RateLimitingFilter.java
D:\studyCards\backend\src\main\java\com\studycards\audit\EnumAuditRepository.java
D:\studyCards\backend\src\main\java\com\studycards\model\NotificationPreferences.java
D:\studyCards\backend\src\main\java\com\studycards\model\User.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\SearchCacheService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedCardPerformanceRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamCompletionResult.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DashboardResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamConfigRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\controller\SubscriptionValidationController.java
D:\studyCards\backend\src\main\java\com\studycards\config\CacheConfig.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PasswordResetRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CaptchaVerificationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\StatisticsMonitoringService.java
D:\studyCards\backend\src\main\java\com\studycards\service\CollaborationAnalyticsService.java
D:\studyCards\backend\src\main\java\com\studycards\service\SubscriptionSchedulerService.java
D:\studyCards\backend\src\main\java\com\studycards\service\StudySessionService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\SubscriptionLimitException.java
D:\studyCards\backend\src\main\java\com\studycards\service\UnifiedScoringService.java
D:\studyCards\backend\src\main\java\com\studycards\service\RateLimitService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AnswerComparisonMetricsController.java
D:\studyCards\backend\src\main\java\com\studycards\controller\PaymentController.java
D:\studyCards\backend\src\main\java\com\studycards\model\RefreshToken.java
D:\studyCards\backend\src\main\java\com\studycards\repository\AnswerFeedbackRepository.java
D:\studyCards\backend\src\main\java\com\studycards\service\LanguageDetectionService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AnalyticsController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SubscriptionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerMetrics.java
D:\studyCards\backend\src\main\java\com\studycards\service\EnhancedAnalyticsService.java
D:\studyCards\backend\src\main\java\com\studycards\repository\ExamTemplateRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CreateStudySessionRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LearningEfficiencyMetrics.java
D:\studyCards\backend\src\main\java\com\studycards\service\PerformanceMonitoringService.java
D:\studyCards\backend\src\main\java\com\studycards\config\AsyncConfig.java
D:\studyCards\backend\src\main\java\com\studycards\service\RecommendationService.java
D:\studyCards\backend\src\main\java\com\studycards\enums\AlertSeverity.java
D:\studyCards\backend\src\main\java\com\studycards\config\PaymobConfig.java
D:\studyCards\backend\src\main\java\com\studycards\controller\MobileStudyController.java
D:\studyCards\backend\src\main\java\com\studycards\repository\NotificationRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamTemplateRequest.java
D:\studyCards\backend\src\main\java\com\studycards\repository\PaymentTransactionRepository.java
D:\studyCards\backend\src\main\java\com\studycards\controller\SubscriptionController.java
D:\studyCards\backend\src\main\java\com\studycards\service\ImprovedAnalyticsService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckCreationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\FallbackType.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerEvaluationReportRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\MobileStudyRateLimitingService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\LoginActivityController.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CardPerformanceRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PaymentRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\UnifiedSearchController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UpdateUserProfileRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserProgressResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\AdvancedAnalyticsService.java
D:\studyCards\backend\src\main\java\com\studycards\service\WordEmbeddingService.java
D:\studyCards\backend\src\main\java\com\studycards\config\RecommendationConfig.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PasswordValidationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ConcurrentOperationException.java
D:\studyCards\backend\src\main\java\com\studycards\validation\ProductionValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\config\InternationalizationConfig.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SubscriptionStatusValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\SpacedRepetitionService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ShareDeckRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnalyticsRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardOwnershipValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\enums\BillingInterval.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CardHintRepository.java
D:\studyCards\backend\src\main\java\com\studycards\config\SubscriptionMigrationStartup.java
D:\studyCards\backend\src\main\java\com\studycards\config\WebSecurityConfig.java
D:\studyCards\backend\src\main\java\com\studycards\controller\EmailVerificationController.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AnswerFeedbackController.java
D:\studyCards\backend\src\main\java\com\studycards\security\AuthTokenFilter.java
D:\studyCards\backend\src\main\java\com\studycards\controller\ReportsController.java
D:\studyCards\backend\src\main\java\com\studycards\service\ResponseMappingService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationTrendItem.java
D:\studyCards\backend\src\main\java\com\studycards\model\CollaborationActivity.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BulkUpdateResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerInfo.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AIInsight.java
D:\studyCards\backend\src\main\java\com\studycards\repository\NotificationPreferencesRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BatchCardOperationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SubscriptionPlanResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedExamSessionRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DailyGoalResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SubscriptionValidationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\repository\AnnotationRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BulkPermissionUpdateRequest.java
D:\studyCards\backend\src\main\java\com\studycards\model\SubscriptionPlan.java
D:\studyCards\backend\src\main\java\com\studycards\exception\AnswerComparisonException.java
D:\studyCards\backend\src\main\java\com\studycards\audit\EnumAuditEvent.java
D:\studyCards\backend\src\main\java\com\studycards\model\Deck.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ServiceException.java
D:\studyCards\backend\src\main\java\com\studycards\config\ValidationConfig.java
D:\studyCards\backend\src\main\java\com\studycards\event\SyncEvent.java
D:\studyCards\backend\src\main\java\com\studycards\model\AnswerEvaluationReport.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamSecurityMetrics.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationHealthResponse.java
D:\studyCards\backend\src\main\java\com\studycards\model\Card.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudySessionAccessValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserPreferencesResponse.java
D:\studyCards\backend\src\main\java\com\studycards\event\DeckCreatedEvent.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ResourceNotFoundException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\MobileStudyProgressRequest.java
D:\studyCards\backend\src\main\java\com\studycards\exception\BadRequestException.java
D:\studyCards\backend\src\main\java\com\studycards\service\SearchAnalyticsService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerComparisonResult.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticAnalysisResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\PasswordResetService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudyStatisticsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\exception\RateLimitExceededException.java
D:\studyCards\backend\src\main\java\com\studycards\model\DeckCollaborator.java
D:\studyCards\backend\src\main\java\com\studycards\event\CacheInvalidationEvent.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudySessionStartResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\TokenRefreshResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\RealTimeInsights.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CaptchaResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\TokenRefreshRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardSearchRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\DashboardController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SpacedRepetitionAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PaymentDataValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\DiscoveryController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PersonalizedRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\service\TokenBlacklistService.java
D:\studyCards\backend\src\main\java\com\studycards\converter\BillingStatusConverter.java
D:\studyCards\backend\src\main\java\com\studycards\model\ExamTemplate.java
D:\studyCards\backend\src\main\java\com\studycards\model\BaseEntity.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StrategyRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\repository\UserPreferencesRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LearningProgressionAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\config\ProductionCacheConfig.java
D:\studyCards\backend\src\main\java\com\studycards\service\JwtMetricsService.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserBlockResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\TagSummary.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CardTagRepository.java
D:\studyCards\backend\src\main\java\com\studycards\listener\SyncEventListener.java
D:\studyCards\backend\src\main\java\com\studycards\config\JwtProperties.java
D:\studyCards\backend\src\main\java\com\studycards\service\RateLimitingService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AdaptiveExamResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AdminSubscriptionController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ContentRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationTemplateResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckCollaborationStatsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedInvitationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\exception\AccountLockedException.java
D:\studyCards\backend\src\main\java\com\studycards\exception\TokenExpiredException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\InvitationListResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticGradingResponse.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ErrorResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ScheduleRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SharedDecksResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ErrorResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudySessionFilterRequest.java
D:\studyCards\backend\src\main\java\com\studycards\enums\SessionType.java
D:\studyCards\backend\src\main\java\com\studycards\controller\NotificationPreferencesController.java
D:\studyCards\backend\src\main\java\com\studycards\service\NotificationService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\BusinessRuleException.java
D:\studyCards\backend\src\main\java\com\studycards\service\BillingDataInitializerService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LoginRequest.java
D:\studyCards\backend\src\main\java\com\studycards\interceptor\EmailVerificationInterceptor.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerDashboard.java
D:\studyCards\backend\src\main\java\com\studycards\exception\InsufficientSubscriptionException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LearningPatternAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\config\StripeConfig.java
D:\studyCards\backend\src\main\java\com\studycards\repository\TagRepository.java
D:\studyCards\backend\src\main\java\com\studycards\model\StudySession.java
D:\studyCards\backend\src\main\java\com\studycards\dto\FavoriteResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\PublicStatisticsService.java
D:\studyCards\backend\src\main\java\com\studycards\repository\UserReportRepository.java
D:\studyCards\backend\src\main\java\com\studycards\service\AIGradingService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardHintDto.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudySessionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\repository\DeckRepositoryCustomImpl.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AuthController.java
D:\studyCards\backend\src\main\java\com\studycards\exception\AuthenticationException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerReportResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\PasswordGenerationService.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserProfileService.java
D:\studyCards\backend\src\main\java\com\studycards\config\PasswordEncoderConfig.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamAnalyticsData.java
D:\studyCards\backend\src\main\java\com\studycards\model\BillingTransaction.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticGradingRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckSearchRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SearchSuggestionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\StudySessionServiceExtension.java
D:\studyCards\backend\src\main\java\com\studycards\service\ValidationMonitoringService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamPerformanceMetrics.java
D:\studyCards\backend\src\main\java\com\studycards\dto\TopCollaboratorItem.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BulkReviewRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticInsightsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\event\StudySessionStartedEvent.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\RecommendationType.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardRequest.java
D:\studyCards\backend\src\main\java\com\studycards\repository\AuditLogRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PredictiveInsights.java
D:\studyCards\backend\src\main\java\com\studycards\model\LoginActivity.java
D:\studyCards\backend\src\main\java\com\studycards\config\AnswerComparisonCacheConfig.java
D:\studyCards\backend\src\main\java\com\studycards\exception\DataIntegrityException.java
D:\studyCards\backend\src\main\java\com\studycards\service\AnswerReportService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ConflictException.java
D:\studyCards\backend\src\main\java\com\studycards\repository\RefreshTokenRepository.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerStatistics.java
D:\studyCards\backend\src\main\java\com\studycards\config\MetricsConfig.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationInvitationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\SemanticAnalysisService.java
D:\studyCards\backend\src\main\java\com\studycards\security\OAuth2AuthenticationFailureHandler.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserService.java
D:\studyCards\backend\src\main\java\com\studycards\service\DashboardService.java
D:\studyCards\backend\src\main\java\com\studycards\aspect\RepositorySecurityAspect.java
D:\studyCards\backend\src\main\java\com\studycards\controller\ExamTemplateController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserReportRequest.java
D:\studyCards\backend\src\main\java\com\studycards\model\PaymentTransaction.java
D:\studyCards\backend\src\main\java\com\studycards\utils\EnumValidationUtils.java
D:\studyCards\backend\src\main\java\com\studycards\repository\PaymentMethodRepository.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\BulkCircuitBreakerResult.java
D:\studyCards\backend\src\main\java\com\studycards\service\CollaborationActivityService.java
D:\studyCards\backend\src\main\java\com\studycards\service\TransactionManagementService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AdaptiveLearningPath.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ScoringResult.java
D:\studyCards\backend\src\main\java\com\studycards\exception\StudyCardsCacheConfigurationException.java
D:\studyCards\backend\src\main\java\com\studycards\repository\ExamPerformanceHistoryRepository.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerTestResult.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ReviewCardsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticInsightsRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PaymentIntentResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserProfileResponse.java
D:\studyCards\backend\src\main\java\com\studycards\enums\BillingStatus.java
D:\studyCards\backend\src\main\java\com\studycards\service\NotificationPreferencesService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AdDisplayResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationActivityItem.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CreatePaymentIntentRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\MobileStudyCacheService.java
D:\studyCards\backend\src\main\java\com\studycards\service\EmailVerificationReminderService.java
D:\studyCards\backend\src\main\java\com\studycards\model\PasswordResetToken.java
D:\studyCards\backend\src\main\java\com\studycards\controller\StatisticsHealthController.java
D:\studyCards\backend\src\main\java\com\studycards\repository\DeckTagRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckPerformanceResponse.java
D:\studyCards\backend\src\main\java\com\studycards\exception\StudyCardsCacheException.java
D:\studyCards\backend\src\main\java\com\studycards\service\MobileStudyService.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CardPerformanceHistoryRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\InvitationResult.java
D:\studyCards\backend\src\main\java\com\studycards\dto\KnowledgeGap.java
D:\studyCards\backend\src\main\java\com\studycards\model\PaymentMethod.java
D:\studyCards\backend\src\main\java\com\studycards\service\AnswerComparisonService.java
D:\studyCards\backend\src\main\java\com\studycards\util\PaginationUtil.java
D:\studyCards\backend\src\main\java\com\studycards\repository\DeckRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ApplyTemplateRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\PerformanceMonitoringController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ApiResponse.java
D:\studyCards\backend\src\main\java\com\studycards\entity\AnswerReport.java
D:\studyCards\backend\src\main\java\com\studycards\service\SecurityAuditService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ActiveSessionExistsException.java
D:\studyCards\backend\src\main\java\com\studycards\service\StudyRecommendationService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\DeckCreationException.java
D:\studyCards\backend\src\main\java\com\studycards\event\CardDeletedEvent.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EmailVerificationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\CacheSecurityService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerFeedbackRequest.java
D:\studyCards\backend\src\main\java\com\studycards\repository\DeckCollaboratorRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudyRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SessionPatterns.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticAnalysisRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\StudyCardsMonitoringService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserReportResponse.java
D:\studyCards\backend\src\main\java\com\studycards\repository\UserBlockRepository.java
D:\studyCards\backend\src\main\java\com\studycards\controller\EnumController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationInsightsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\NotificationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckMappingContext.java
D:\studyCards\backend\src\main\java\com\studycards\config\WebSocketConfig.java
D:\studyCards\backend\src\main\java\com\studycards\security\AuthEntryPointJwt.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CardHintController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CompleteStudySessionRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\StatisticsScheduledMonitor.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserReportService.java
D:\studyCards\backend\src\main\java\com\studycards\service\SecurityValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserActivityResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerEvaluationReportResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\FallbackStrategy.java
D:\studyCards\backend\src\main\java\com\studycards\service\SubscriptionValidationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticAnalysisDetails.java
D:\studyCards\backend\src\main\java\com\studycards\exception\DuplicateDeckException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CaptchaVerificationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\FraudDetectionService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\QuestionTypeMetadata.java
D:\studyCards\backend\src\main\java\com\studycards\config\AnswerComparisonConfig.java
D:\studyCards\backend\src\main\java\com\studycards\service\PaymobPaymentService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudySchedulePrediction.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ConceptAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\dto\MasteryPrediction.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamValidationResult.java
D:\studyCards\backend\src\main\java\com\studycards\service\StripePaymentService.java
D:\studyCards\backend\src\main\java\com\studycards\config\SearchOptimizationConfig.java
D:\studyCards\backend\src\main\java\com\studycards\service\SyncConflictResolver.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AttachPaymentMethodRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CollaborationManagementController.java
D:\studyCards\backend\src\main\java\com\studycards\utils\Utils.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedDeckResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\StudySessionsPageResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AdaptiveTestingRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserSummaryResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LearningAnalyticsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CollaborationInvitationRepository.java
D:\studyCards\backend\src\main\java\com\studycards\model\Tag.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ValidationException.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerHealthCheck.java
D:\studyCards\backend\src\main\java\com\studycards\validation\EnumValidatorImpl.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CognitiveLoadAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\audit\EnumAuditService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\SearchController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\MobileStudySessionRequest.java
D:\studyCards\backend\src\main\java\com\studycards\model\DeckTag.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationDashboardResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerPerformanceProfile.java
D:\studyCards\backend\src\main\java\com\studycards\config\MobileStudyCacheConfig.java
D:\studyCards\backend\src\main\java\com\studycards\model\CardTag.java
D:\studyCards\backend\src\main\java\com\studycards\model\UserPreferences.java
D:\studyCards\backend\src\main\java\com\studycards\exception\CardOperationException.java
D:\studyCards\backend\src\main\java\com\studycards\aspect\PerformanceMonitoringAspect.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PasswordGenerationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerAlertConfig.java
D:\studyCards\backend\src\main\java\com\studycards\service\AnalyticsService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\TokenRefreshException.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AuditController.java
D:\studyCards\backend\src\main\java\com\studycards\service\CacheEvictionService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ForbiddenException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedExamSessionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\enums\ValidationEventType.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PermissionMatrixResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\EnumCircuitBreakerService.java
D:\studyCards\backend\src\main\java\com\studycards\service\EmailService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckRecommendationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SignupRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\CacheUtilityService.java
D:\studyCards\backend\src\main\java\com\studycards\service\ReportsService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ErrorPatternAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\enums\SharingMethod.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardValidationRequest.java
D:\studyCards\backend\src\main\java\com\studycards\entity\UserBlock.java
D:\studyCards\backend\src\main\java\com\studycards\model\CollaborationInvitation.java
D:\studyCards\backend\src\main\java\com\studycards\security\JwtUtils.java
D:\studyCards\backend\src\main\java\com\studycards\service\ImprovedCollaborationService.java
D:\studyCards\backend\src\main\java\com\studycards\service\FileStorageService.java
D:\studyCards\backend\src\main\java\com\studycards\service\EmailVerificationService.java
D:\studyCards\backend\src\main\java\com\studycards\model\CardPerformanceHistory.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AdaptiveExamController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamSessionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\CollaborationService.java
D:\studyCards\backend\src\main\java\com\studycards\service\ExamTemplateService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedAcceptanceResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardDeletionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CardPerformanceRequest.java
D:\studyCards\backend\src\main\java\com\studycards\service\SubscriptionStatusService.java
D:\studyCards\backend\src\main\java\com\studycards\service\SearchPerformanceService.java
D:\studyCards\backend\src\main\java\com\studycards\enums\ErrorType.java
D:\studyCards\backend\src\main\java\com\studycards\enums\QuestionType.java
D:\studyCards\backend\src\main\java\com\studycards\dto\paymob\PaymobOrderRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UpdatePermissionRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PersonalizedRecommendations.java
D:\studyCards\backend\src\main\java\com\studycards\validation\EnumValidator.java
D:\studyCards\backend\src\main\java\com\studycards\service\MetricsService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\SemanticAnalysisController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\NextQuestionRecommendation.java
D:\studyCards\backend\src\main\java\com\studycards\repository\PasswordResetTokenRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\MotivationFactors.java
D:\studyCards\backend\src\main\java\com\studycards\service\AdaptiveTestingService.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerEvent.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserPreferencesRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamConfigRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamHistoryResponse.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerStatus.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CardController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LoginActivityDTO.java
D:\studyCards\backend\src\main\java\com\studycards\entity\AnswerFeedback.java
D:\studyCards\backend\src\main\java\com\studycards\config\PerformanceConfig.java
D:\studyCards\backend\src\main\java\com\studycards\util\TextProcessingUtil.java
D:\studyCards\backend\src\main\java\com\studycards\entity\UserReport.java
D:\studyCards\backend\src\main\java\com\studycards\service\SubscriptionLimitService.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserStatisticsService.java
D:\studyCards\backend\src\main\java\com\studycards\config\CircuitBreakerConfig.java
D:\studyCards\backend\src\main\java\com\studycards\security\OAuth2AuthenticationSuccessHandler.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerConfigModel.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BillingHistoryResponse.java
D:\studyCards\backend\src\main\java\com\studycards\model\Notification.java
D:\studyCards\backend\src\main\java\com\studycards\service\CaptchaService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PublicStatisticsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\SearchService.java
D:\studyCards\backend\src\main\java\com\studycards\repository\AnswerReportRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\paymob\PaymobOrderResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamSessionRequest.java
D:\studyCards\backend\src\main\java\com\studycards\repository\UserRepository.java
D:\studyCards\backend\src\main\java\com\studycards\exception\ResourceAlreadyExistsException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\paymob\PaymobAuthRequest.java
D:\studyCards\backend\src\main\java\com\studycards\model\SecurityAuditLog.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerReportRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationActivityResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DifficultyProgression.java
D:\studyCards\backend\src\main\java\com\studycards\dto\RiskAnalysis.java
D:\studyCards\backend\src\main\java\com\studycards\enums\Difficulty.java
D:\studyCards\backend\src\main\java\com\studycards\service\PasswordSecurityService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\DataAccessException.java
D:\studyCards\backend\src\main\java\com\studycards\repository\SubscriptionPlanRepository.java
D:\studyCards\backend\src\main\java\com\studycards\controller\AnnotationController.java
D:\studyCards\backend\src\main\java\com\studycards\model\ExamPerformanceHistory.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AuthenticationValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BillingStatusMetadata.java
D:\studyCards\backend\src\main\java\com\studycards\dto\NotificationPreferencesResponse.java
D:\studyCards\backend\src\main\java\com\studycards\exception\SubscriptionRequiredException.java
D:\studyCards\backend\src\main\java\com\studycards\exception\PaymentException.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PerformancePrediction.java
D:\studyCards\backend\src\main\java\com\studycards\dto\InvitationItem.java
D:\studyCards\backend\src\main\java\com\studycards\dto\RegistrationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\StripeWebhookController.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckDiscoveryService.java
D:\studyCards\backend\src\main\java\com\studycards\service\DeckCardOperationService.java
D:\studyCards\backend\src\main\java\com\studycards\repository\SearchHistoryRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\PaymentMethodResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\PasswordGenerationController.java
D:\studyCards\backend\src\main\java\com\studycards\service\SubscriptionMigrationService.java
D:\studyCards\backend\src\main\java\com\studycards\service\SubscriptionService.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserBlockService.java
D:\studyCards\backend\src\main\java\com\studycards\exception\GlobalExceptionHandler.java
D:\studyCards\backend\src\main\java\com\studycards\service\EnhancedNotificationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\LearningStyleProfile.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedStudySessionResponse.java
D:\studyCards\backend\src\main\java\com\studycards\enums\SubscriptionStatus.java
D:\studyCards\backend\src\main\java\com\studycards\model\Annotation.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AnswerFeedbackResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\StudySessionController.java
D:\studyCards\backend\src\main\java\com\studycards\monitoring\AlertingService.java
D:\studyCards\backend\src\main\java\com\studycards\service\JwtRateLimitService.java
D:\studyCards\backend\src\main\java\com\studycards\service\RepositoryUsageExampleService.java
D:\studyCards\backend\src\main\java\com\studycards\service\CacheInvalidationService.java
D:\studyCards\backend\src\main\java\com\studycards\repository\CollaborationActivityRepository.java
D:\studyCards\backend\src\main\java\com\studycards\dto\paymob\PaymobPaymentKeyResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ForgotPasswordRequest.java
D:\studyCards\backend\src\main\java\com\studycards\circuitbreaker\CircuitBreakerEventType.java
D:\studyCards\backend\src\main\java\com\studycards\dto\JwtResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\UserLearningProfileService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AdaptiveTestingResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AIGradingRequest.java
D:\studyCards\backend\src\main\java\com\studycards\exception\JwtException.java
D:\studyCards\backend\src\main\java\com\studycards\controller\DeckController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\DeckHierarchyResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CircuitBreakerController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ReviewCardsRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\UserController.java
D:\studyCards\backend\src\main\java\com\studycards\service\CardService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ProcessAnswerRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\SemanticComparisonRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AIGradingResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\UnifiedSearchService.java
D:\studyCards\backend\src\main\java\com\studycards\security\UserDetailsImpl.java
D:\studyCards\backend\src\main\java\com\studycards\service\SecurityService.java
D:\studyCards\backend\src\main\java\com\studycards\service\AnnotationService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ShareDeckResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\BulkRemoveCollaboratorsRequest.java
D:\studyCards\backend\src\main\java\com\studycards\dto\EnhancedInvitationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\service\AlertingService.java
D:\studyCards\backend\src\main\java\com\studycards\service\LoginActivityService.java
D:\studyCards\backend\src\main\java\com\studycards\dto\UserSummary.java
D:\studyCards\backend\src\main\java\com\studycards\dto\RegistrationValidationResponse.java
D:\studyCards\backend\src\main\java\com\studycards\controller\FileController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\NotificationPreferencesRequest.java
D:\studyCards\backend\src\main\java\com\studycards\security\CustomOAuth2UserService.java
D:\studyCards\backend\src\main\java\com\studycards\controller\CardTagController.java
D:\studyCards\backend\src\main\java\com\studycards\StudycardsApplication.java
D:\studyCards\backend\src\main\java\com\studycards\model\Category.java
D:\studyCards\backend\src\main\java\com\studycards\security\UserDetailsServiceImpl.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaboratorRequest.java
D:\studyCards\backend\src\main\java\com\studycards\controller\NotificationController.java
D:\studyCards\backend\src\main\java\com\studycards\config\WebMvcConfig.java
D:\studyCards\backend\src\main\java\com\studycards\enums\AuditEventType.java
D:\studyCards\backend\src\main\java\com\studycards\controller\SearchPerformanceController.java
D:\studyCards\backend\src\main\java\com\studycards\dto\ExamTemplateResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\CollaborationAnalyticsResponse.java
D:\studyCards\backend\src\main\java\com\studycards\dto\AutoSaveDraftRequest.java
