package com.studycards.validation;

import com.studycards.model.Card;
import com.studycards.model.Deck;
import com.studycards.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that the ease factor validation fix works correctly
 * and prevents the ConstraintViolationException that was occurring.
 */
@ExtendWith(MockitoExtension.class)
class EaseFactorValidationTest {

    @InjectMocks
    private ProductionValidationService validationService;

    @Mock
    private com.studycards.service.ContentVisibilityService contentVisibilityService;

    private Card testCard;

    @BeforeEach
    void setUp() {
        // Create a test deck
        Deck testDeck = new Deck();
        testDeck.setId(1L);
        testDeck.setName("Test Deck");

        // Create a test user
        User testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");

        // Create a test card
        testCard = new Card();
        testCard.setId(1L);
        testCard.setQuestion("Test Question");
        testCard.setAnswer("Test Answer");
        testCard.setDeck(testDeck);
        testCard.setCreatedBy(testUser);
        testCard.setDifficultyLevel(1);
        testCard.setReviewCount(0);
        testCard.setCorrectCount(0);
        testCard.setLearningProgress(0.0f);
        testCard.setIntervalDays(1);
    }

    @Test
    void testValidateCard_WithValidEaseFactor_ShouldNotThrowException() {
        // Arrange
        testCard.setEaseFactor(BigDecimal.valueOf(2.0));

        // Act & Assert - should not throw any exception
        assertDoesNotThrow(() -> {
            Card validatedCard = validationService.validateCard(testCard);
            assertNotNull(validatedCard);
            assertEquals(BigDecimal.valueOf(2.0), validatedCard.getEaseFactor());
        });
    }

    @Test
    void testValidateCard_WithLowEaseFactor_ShouldClampToMinimum() {
        // Arrange - set ease factor below minimum (1.3)
        testCard.setEaseFactor(BigDecimal.valueOf(1.0));

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert - should be clamped to minimum value
        assertNotNull(validatedCard);
        assertEquals(BigDecimal.valueOf(1.3), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_WithHighEaseFactor_ShouldClampToMaximum() {
        // Arrange - set ease factor above maximum (5.0)
        testCard.setEaseFactor(BigDecimal.valueOf(6.0));

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert - should be clamped to maximum value
        assertNotNull(validatedCard);
        assertEquals(BigDecimal.valueOf(5.0), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_WithNullEaseFactor_ShouldSetDefault() {
        // Arrange
        testCard.setEaseFactor(null);

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert - should be set to default value
        assertNotNull(validatedCard);
        assertEquals(BigDecimal.valueOf(2.5), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_WithZeroEaseFactor_ShouldSetDefault() {
        // Arrange
        testCard.setEaseFactor(BigDecimal.ZERO);

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert - should be set to default value
        assertNotNull(validatedCard);
        assertEquals(BigDecimal.valueOf(2.5), validatedCard.getEaseFactor());
    }

    @Test
    void testValidateCard_WithPrecisionEaseFactor_ShouldHandleCorrectly() {
        // Arrange - test with a precise value that might cause floating point issues
        testCard.setEaseFactor(BigDecimal.valueOf(1.30000001)); // Slightly above minimum

        // Act
        Card validatedCard = validationService.validateCard(testCard);

        // Assert - should handle precision correctly
        assertNotNull(validatedCard);
        assertTrue(validatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(1.3)) >= 0);
        assertTrue(validatedCard.getEaseFactor().compareTo(BigDecimal.valueOf(5.0)) <= 0);
    }
}
